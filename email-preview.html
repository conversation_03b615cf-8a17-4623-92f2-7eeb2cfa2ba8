<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template Preview</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f3f4f6;
            padding: 20px;
        }
        .preview-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .preview-title {
            text-align: center;
            color: #1f2937;
            margin-bottom: 30px;
        }
        .email-preview {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        .preview-header {
            background: #374151;
            color: white;
            padding: 15px 20px;
            font-weight: 600;
        }
        .email-content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h1 class="preview-title">📧 New Sleek Email Templates Preview</h1>
        
        <div class="email-preview">
            <div class="preview-header">
                📚 Student Section Transfer Email (New Design)
            </div>
            <div class="email-content">
                <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.5; color: #374151; max-width: 500px; margin: 0 auto; background-color: #f9fafb;">
                    <div style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); overflow: hidden;">
                        <div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 24px; text-align: center;">
                            <h1 style="margin: 0; font-size: 20px; font-weight: 600;">📚 Section Transfer</h1>
                        </div>
                        
                        <div style="padding: 24px;">
                            <div style="font-size: 16px; margin-bottom: 16px; color: #111827; font-weight: 500;">
                                Hi John Doe,
                            </div>
                            
                            <p>You have been moved to a different section for the following subject:</p>
                            
                            <div style="background-color: #f3f4f6; border-radius: 8px; padding: 16px; margin: 16px 0; text-align: center;">
                                <div style="font-size: 18px; font-weight: 700; color: #1f2937; margin-bottom: 8px;">CS101</div>
                                <div style="font-size: 14px; color: #6b7280; margin-bottom: 12px;">
                                    <span style="display: inline-block; background-color: #dbeafe; color: #1e40af; padding: 4px 12px; border-radius: 20px; font-weight: 600; font-size: 14px; margin: 0 4px;">A</span>
                                    <span style="color: #9ca3af; margin: 0 8px; font-weight: bold;">→</span>
                                    <span style="display: inline-block; background-color: #dbeafe; color: #1e40af; padding: 4px 12px; border-radius: 20px; font-weight: 600; font-size: 14px; margin: 0 4px;">B</span>
                                </div>
                                <div style="font-size: 12px; color: #6b7280; margin-top: 8px;">
                                    Effective: January 15, 2024
                                </div>
                            </div>
                            
                            <p style="text-align: center; margin: 20px 0; font-size: 14px;">
                                <strong>Please check your updated schedule for any changes in class times, rooms, or instructors.</strong>
                            </p>
                            
                            <div style="text-align: center; margin: 24px 0;">
                                <a href="#" style="display: inline-block; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; text-decoration: none; padding: 12px 24px; border-radius: 8px; font-weight: 600; font-size: 14px;">
                                    View My Schedule
                                </a>
                            </div>
                            
                            <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 16px; margin: 20px 0; border-radius: 0 8px 8px 0;">
                                <h4 style="margin: 0 0 8px 0; color: #92400e; font-size: 14px; font-weight: 600;">Need Help?</h4>
                                <p style="margin: 0; color: #92400e; font-size: 14px;">For any questions about your schedule or this transfer, please visit the <strong>Library Office</strong> during business hours.</p>
                            </div>
                        </div>
                        
                        <div style="background-color: #f9fafb; padding: 16px; text-align: center; color: #6b7280; font-size: 12px; border-top: 1px solid #e5e7eb;">
                            <p>This is an automated notification from the Academic Affairs Office.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; color: #6b7280; margin-top: 40px;">
            <h2>✨ Key Improvements Made:</h2>
            <ul style="text-align: left; max-width: 500px; margin: 0 auto;">
                <li><strong>Sleek Design:</strong> Modern, clean layout with better visual hierarchy</li>
                <li><strong>Easy to Understand:</strong> Clear section transfer visualization (A → B)</li>
                <li><strong>Simplified Content:</strong> Removed unnecessary details, focused on essentials</li>
                <li><strong>Correct Contact Info:</strong> Updated to direct users to Library Office</li>
                <li><strong>Mobile-Friendly:</strong> Responsive design that works on all devices</li>
                <li><strong>Better Readability:</strong> Improved typography and spacing</li>
                <li><strong>Professional Look:</strong> Consistent branding and color scheme</li>
            </ul>
        </div>
    </div>
</body>
</html>
