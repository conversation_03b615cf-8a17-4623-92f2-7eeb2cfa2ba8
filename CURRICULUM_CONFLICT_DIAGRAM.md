# Subject Enrollment Conflict - Visual Analysis

## Current Problem Flow

```mermaid
graph TD
    A[Student Enrollment] --> B[Select Course: BSIT]
    B --> C[Subject Selection Dropdown]
    C --> D{Current Logic}
    D --> E[Filter by course_id only]
    E --> F[Shows ALL subjects from ALL curriculum years]
    F --> G[Conflict: ITW 326 AND IAS 201 both shown]
    G --> H[❌ Wrong subject enrollment possible]
    
    style G fill:#ffcccc
    style H fill:#ff9999
```

## Proposed Solution Flow

```mermaid
graph TD
    A[Student Enrollment] --> B[Select Course: BSIT]
    B --> C[Get Student's Curriculum Year]
    C --> D[Subject Selection Dropdown]
    D --> E{Enhanced Logic}
    E --> F[Filter by course_id AND curriculum_year]
    F --> G[Shows only curriculum-specific subjects]
    G --> H[✅ Correct subject enrollment]
    
    style G fill:#ccffcc
    style H fill:#99ff99
```

## Database Relationship Analysis

```mermaid
erDiagram
    COURSES {
        int id PK
        string code
        string title
        string curriculum_year
    }
    
    SUBJECTS {
        int id PK
        string code
        string title
        int course_id FK
    }
    
    STUDENTS {
        int id PK
        int course_id FK
    }
    
    SUBJECT_ENROLLMENTS {
        int id PK
        int subject_id FK
        int student_id FK
        int enrollment_id FK
    }
    
    COURSES ||--o{ SUBJECTS : "has many"
    COURSES ||--o{ STUDENTS : "enrolled in"
    SUBJECTS ||--o{ SUBJECT_ENROLLMENTS : "enrolled in"
    STUDENTS ||--o{ SUBJECT_ENROLLMENTS : "enrolls in"
```

## Current vs Proposed Subject Filtering

### Current Implementation
```php
// ❌ Current - Only filters by course_id
$allCourseSubjects = Subject::where('course_id', $selectedCourse)
    ->with('course')
    ->get()
    ->reject(fn ($subject): bool => in_array($subject->id, $enrolledSubjectIds));
```

### Proposed Implementation
```php
// ✅ Proposed - Filters by course_id AND curriculum_year
$student = Student::find($studentId);
$studentCourse = Course::find($student->course_id);
$curriculumYear = $studentCourse->curriculum_year;

$allCourseSubjects = Subject::where('course_id', $selectedCourse)
    ->whereHas('course', function($query) use ($curriculumYear) {
        $query->where('curriculum_year', $curriculumYear);
    })
    ->with('course')
    ->get()
    ->reject(fn ($subject): bool => in_array($subject->id, $enrolledSubjectIds));
```

## Impact Analysis

### Before Fix
- 🔴 Students can see subjects from wrong curriculum
- 🔴 Potential enrollment in incompatible subjects
- 🔴 Data integrity issues
- 🔴 Confusion during enrollment process

### After Fix
- 🟢 Students only see curriculum-appropriate subjects
- 🟢 Enrollment validation prevents conflicts
- 🟢 Improved data integrity
- 🟢 Clear subject identification with curriculum year