; Production PHP Configuration for <PERSON><PERSON> with FrankenPHP
; Optimized for external Redis and PostgreSQL connectivity

; Memory settings
memory_limit = 256M
max_execution_time = 300
max_input_time = 60

; File upload limits
upload_max_filesize = 50M
post_max_size = 50M
max_file_uploads = 20

; Error reporting (production)
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT

; Session configuration
session.gc_maxlifetime = 7200
session.cookie_lifetime = 0
session.cookie_secure = 1
session.cookie_httponly = 1
session.use_strict_mode = 1

; OPcache settings
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 10000
opcache.revalidate_freq = 2
opcache.validate_timestamps = 0
opcache.save_comments = 1
opcache.fast_shutdown = 1

; Realpath cache
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; Connection settings for external services
default_socket_timeout = 60
mysql.connect_timeout = 60
pgsql.connect_timeout = 60

; Redis/networking optimizations
allow_url_fopen = On
user_agent = "Laravel Application"

; Security
expose_php = Off
allow_url_include = Off

; Date
date.timezone = UTC

; Logging
log_errors_max_len = 1024

; Performance
output_buffering = 4096
zlib.output_compression = Off

; File handling
auto_detect_line_endings = Off