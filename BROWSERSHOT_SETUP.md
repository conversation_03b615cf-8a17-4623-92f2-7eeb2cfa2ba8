# BrowsershotService Integration for Nix Deployments

This document explains the BrowsershotService integration that enables PDF generation to work seamlessly with Nix/Nixpacks deployments.

## Overview

The `SendAssessmentNotificationJob` has been updated to use the `BrowsershotService` instead of hardcoded binary paths. This allows the application to automatically detect the correct Chrome, Node.js, and NPM paths in Nix environments.

## Changes Made

### 1. Updated SendAssessmentNotificationJob

**Before:**
```php
// Old hardcoded approach (doesn't work with <PERSON>)
\Spatie\Browsershot\Browsershot::html($html)
    ->setChromePath("/sbin/chromium")
    ->noSandbox()
    ->setNodeBinary("/sbin/node")
    ->setNpmBinary("/sbin/npm")
    ->timeout(120)
    ->margins(10, 10, 10, 10, "mm")
    ->landscape()
    ->format("A4")
    ->printBackground()
    ->waitUntilNetworkIdle()
    ->save($assessmentPath);
```

**After:**
```php
// New BrowsershotService approach (works with <PERSON>)
$pdfOptions = [
    "format" => "A4",
    "margin_top" => 10,
    "margin_bottom" => 10,
    "margin_left" => 10,
    "margin_right" => 10,
    "print_background" => true,
    "landscape" => true,
    "wait_until_network_idle" => true,
    "timeout" => 120,
];

$success = BrowsershotService::generatePdf(
    $html,
    $assessmentPath,
    $pdfOptions
);
```

### 2. Enhanced BrowsershotService

Added support for additional PDF generation options:
- `landscape` - Enable landscape orientation
- `wait_until_network_idle` - Wait for network requests to complete
- `timeout` - Custom timeout values

## Path Detection Logic

The BrowsershotService automatically detects binary paths in the following order:

### 1. Environment Variables (Highest Priority)
```bash
CHROME_PATH=/root/.nix-profile/bin/chromium
NODE_BINARY_PATH=/root/.nix-profile/bin/node
NPM_BINARY_PATH=/root/.nix-profile/bin/npm
```

### 2. Configuration File
Values from `config/browsershot.php`

### 3. Nix Profile Paths
- `/root/.nix-profile/bin/chromium`
- `/root/.nix-profile/bin/node`
- `/root/.nix-profile/bin/npm`
- `/nix/var/nix/profiles/default/bin/[binary]`

### 4. System PATH
Uses `which` command to find binaries in PATH

## Environment Configuration

### For Nix Deployments

Create or update your `.env` file with:

```bash
# Browsershot Binary Paths for Nix (auto-detected, but can be overridden)
CHROME_PATH=/root/.nix-profile/bin/chromium
NODE_BINARY_PATH=/root/.nix-profile/bin/node
NPM_BINARY_PATH=/root/.nix-profile/bin/npm

# Browsershot Configuration
BROWSERSHOT_NO_SANDBOX=true
BROWSERSHOT_TIMEOUT=120
BROWSERSHOT_TEMP_DIRECTORY=/tmp/browsershot-temp

# PDF Generation Settings
BROWSERSHOT_PDF_FORMAT=A4
BROWSERSHOT_PDF_MARGIN_TOP=10
BROWSERSHOT_PDF_MARGIN_BOTTOM=10
BROWSERSHOT_PDF_MARGIN_LEFT=10
BROWSERSHOT_PDF_MARGIN_RIGHT=10
BROWSERSHOT_PDF_PRINT_BACKGROUND=true
```

### Nixpacks Configuration

The `nixpacks.toml` is already configured to:
1. Include chromium in nixPkgs
2. Auto-detect binary paths at startup
3. Set environment variables for all processes
4. Pass environment variables to Laravel queue workers

## Testing

### Test BrowsershotService Functionality

```bash
# Test path detection and PDF generation
php artisan browsershot:test --debug
```

### Test Assessment Job

```bash
# Test the actual assessment notification job
php artisan test:assessment-resend [enrollment_id]
```

### Debug Path Detection

```bash
# Find and verify Chrome/Chromium paths
php artisan find:chrome-path
```

## Troubleshooting

### Common Issues

1. **Chrome not found error**
   - Verify chromium is included in `nixpacks.toml`
   - Check environment variables are set correctly
   - Run `php artisan find:chrome-path` to debug

2. **PDF generation fails**
   - Check temp directory permissions
   - Verify all required binaries are executable
   - Run `php artisan browsershot:test --debug`

3. **Queue worker issues**
   - Ensure environment variables are passed to workers in `nixpacks.toml`
   - Check supervisor configuration includes environment variables

4. **Production Error: "/nix/var/nix/profiles/default/bin/chromium" path issues**
   - This error indicates the wrong Nix path is being detected
   - **Solution**: Explicitly set environment variables in production:
     ```bash
     CHROME_PATH=/root/.nix-profile/bin/chromium
     NODE_BINARY_PATH=/root/.nix-profile/bin/node
     NPM_BINARY_PATH=/root/.nix-profile/bin/npm
     ```
   - The `/root/.nix-profile/bin/` paths are preferred over `/nix/var/nix/profiles/default/bin/`
   - Check deployment logs for path detection messages during startup

5. **Local vs Production Environment Differences**
   - Local development might use different paths (e.g., `/sbin/`, `/usr/bin/`)
   - Production Nix environments use `/root/.nix-profile/bin/` or `/nix/var/nix/profiles/default/bin/`
   - Environment variables take precedence over auto-detection

### Debug Commands

```bash
# Check system information
php artisan browsershot:test --debug

# Find binary paths
php artisan find:chrome-path

# Test assessment PDF generation specifically
php artisan debug:assessment-pdf --no-cleanup

# Test queue worker environment
php artisan test:queue-environment --sync

# Test with real enrollment data
php artisan debug:assessment-pdf [enrollment_id] --no-cleanup

# Monitor queue jobs
php artisan monitor:assessment-jobs
```

### Production Deployment Checklist

1. **Environment Variables**: Set explicit paths in `.env`:
   ```bash
   CHROME_PATH=/root/.nix-profile/bin/chromium
   NODE_BINARY_PATH=/root/.nix-profile/bin/node
   NPM_BINARY_PATH=/root/.nix-profile/bin/npm
   BROWSERSHOT_NO_SANDBOX=true
   BROWSERSHOT_TIMEOUT=120
   ```

2. **Verify Paths**: Check deployment logs for path detection messages

3. **Test After Deployment**: Run diagnostic commands to verify functionality

4. **Queue Worker Configuration**: Ensure workers inherit environment variables

5. **File Permissions**: Verify temp directories are writable

## Benefits

1. **Automatic Path Detection**: No need to hardcode binary paths
2. **Environment Flexibility**: Works in development, staging, and production
3. **Nix Compatibility**: Specifically designed for Nix/Nixpacks deployments
4. **Configuration Priority**: Environment variables take precedence
5. **Error Handling**: Better error reporting and debugging capabilities
6. **Maintainability**: Centralized browsershot configuration

## Migration Notes

- Old hardcoded paths in `/sbin/` have been removed
- Assessment PDF generation now uses BrowsershotService
- Configuration is centralized in `config/browsershot.php`
- Environment variables take precedence over configuration files
- No breaking changes to existing functionality

## Support

If you encounter issues:

1. Run diagnostic commands listed above
2. Check logs for detailed error messages
3. Verify Nix environment includes required packages
4. Ensure environment variables are properly set