services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: dccp-admin:latest
    container_name: dccp-admin-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8000}:8000"
    environment:
      APP_NAME: "${APP_NAME:-DCCP Admin}"
      APP_ENV: production
      APP_DEBUG: false
      APP_KEY: "${APP_KEY}"
      APP_URL: "${APP_URL:-http://localhost:8000}"
      
      # External Database Configuration
      DB_CONNECTION: pgsql
      DB_HOST: "${DB_HOST}"
      DB_PORT: "${DB_PORT:-5432}"
      DB_DATABASE: "${DB_DATABASE}"
      DB_USERNAME: "${DB_USERNAME}"
      DB_PASSWORD: "${DB_PASSWORD}"
      
      # External Redis Configuration
      REDIS_HOST: "${REDIS_HOST}"
      REDIS_PORT: "${REDIS_PORT:-6379}"
      REDIS_PASSWORD: "${REDIS_PASSWORD:-}"
      REDIS_CLIENT: phpredis
      
      # Cache and Session
      CACHE_STORE: redis
      SESSION_DRIVER: redis
      QUEUE_CONNECTION: redis
      
      # Logging
      LOG_CHANNEL: stderr
      LOG_LEVEL: "${LOG_LEVEL:-warning}"
      
      # Mail Configuration
      MAIL_MAILER: "${MAIL_MAILER:-smtp}"
      MAIL_HOST: "${MAIL_HOST}"
      MAIL_PORT: "${MAIL_PORT:-587}"
      MAIL_USERNAME: "${MAIL_USERNAME}"
      MAIL_PASSWORD: "${MAIL_PASSWORD}"
      MAIL_ENCRYPTION: "${MAIL_ENCRYPTION:-tls}"
      MAIL_FROM_ADDRESS: "${MAIL_FROM_ADDRESS}"
      MAIL_FROM_NAME: "${MAIL_FROM_NAME:-DCCP Admin}"
      
      # Optional: Run migrations on startup
      RUN_MIGRATIONS: "${RUN_MIGRATIONS:-false}"
      
      # FrankenPHP/Octane Configuration
      OCTANE_SERVER: frankenphp
      
      # Additional Laravel Settings
      BCRYPT_ROUNDS: 12
      SESSION_LIFETIME: 120
      SESSION_ENCRYPT: false
      
    volumes:
      - app_storage:/app/storage/app
      - app_logs:/app/storage/logs
      - app_cache:/app/storage/framework/cache
      - app_sessions:/app/storage/framework/sessions
      - app_views:/app/storage/framework/views
    
    networks:
      - dccp-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  app_storage:
    driver: local
  app_logs:
    driver: local
  app_cache:
    driver: local
  app_sessions:
    driver: local
  app_views:
    driver: local

networks:
  dccp-network:
    driver: bridge
    external: true