# 🐛 Bug Report: Subject Enrollment Conflict Between Different Curriculum Versions

## 📋 Issue Summary

**Title:** Subject enrollment conflicts between BSIT 2018-2019 and BSIT 2024-2025 curricula causing incorrect subject assignments

**Priority:** High
**Type:** Bug
**Component:** Student Enrollment System
**Affects:** Subject selection during student enrollment process

## 🔍 Problem Description

When enrolling students in subjects, there are conflicts between subjects from different curriculum versions that have similar names but different subject codes. This causes confusion and potential incorrect enrollments.

### Specific Example:
- **Old Curriculum (BSIT 2018-2019):** ITW 326 - Information Assurance
- **New Curriculum (BSIT 2024-2025):** IAS 201 - Information Assurance

Both subjects have the same title "Information Assurance" but belong to different curriculum years and have different subject codes.

## 🚨 Current Impact

- Students may be enrolled in subjects from the wrong curriculum version
- Subject selection dropdown may show conflicting or duplicate entries
- Potential academic record inconsistencies
- Confusion for enrollment staff during the enrollment process

## 🔧 Technical Analysis

Based on code analysis, the issue stems from the subject selection logic in the enrollment system:

### Current Implementation Issues:

1. **Subject Filtering Logic** (`app/Filament/Resources/StudentEnrollmentResource.php` lines 435-482):
   ```php
   // Current logic filters by course_id but doesn't consider curriculum_year
   $allCourseSubjects = Subject::where('course_id', $selectedCourse)
       ->with('course')
       ->get()
       ->reject(fn ($subject): bool => in_array($subject->id, $enrolledSubjectIds));
   ```

2. **Course Model** (`app/Models/Course.php` lines 26, 49):
   - Has `curriculum_year` field but it's not being used in subject filtering
   - Different curriculum versions may have separate course records

3. **Subject Model** (`app/Models/Subject.php` lines 28, 127-130):
   - Subjects are linked to courses via `course_id`
   - No direct curriculum year filtering in subject selection

## 🎯 Expected Behavior

1. **Curriculum-Aware Subject Filtering:**
   - Students should only see subjects from their course's curriculum year
   - Subject dropdown should filter based on both course and curriculum year

2. **Clear Subject Identification:**
   - Subject codes should be unique within the same curriculum
   - Display should clearly indicate curriculum year when necessary

3. **Enrollment Validation:**
   - System should prevent enrollment in subjects from incompatible curriculum versions
   - Validation should occur both on frontend and backend

## 💡 Proposed Solution

### Phase 1: Immediate Fix
1. **Enhance Subject Filtering Logic:**
   ```php
   // Enhanced filtering considering curriculum year
   $allCourseSubjects = Subject::where('course_id', $selectedCourse)
       ->whereHas('course', function($query) use ($studentCurriculumYear) {
           $query->where('curriculum_year', $studentCurriculumYear);
       })
       ->with('course')
       ->get()
       ->reject(fn ($subject): bool => in_array($subject->id, $enrolledSubjectIds));
   ```

2. **Add Curriculum Year to Subject Display:**
   - Show curriculum year in subject dropdown for clarity
   - Format: "ITW 326 - Information Assurance (2018-2019)"

### Phase 2: Long-term Improvements
1. **Database Schema Enhancement:**
   - Add curriculum year validation constraints
   - Create indexes for better performance

2. **Enhanced Validation:**
   - Backend validation to prevent cross-curriculum enrollment
   - Frontend warnings for potential conflicts

3. **Migration Strategy:**
   - Data cleanup for existing conflicting enrollments
   - Audit trail for curriculum changes

## 🔄 Implementation Plan

### Step 1: Analysis & Planning
- [ ] Audit existing subjects for curriculum conflicts
- [ ] Identify all affected subject pairs
- [ ] Document current enrollment data integrity

### Step 2: Backend Changes
- [ ] Update `StudentEnrollmentResource.php` subject filtering logic
- [ ] Add curriculum year validation in `Subject` model
- [ ] Enhance `Course` model methods for curriculum-aware queries

### Step 3: Frontend Improvements
- [ ] Update subject dropdown display format
- [ ] Add curriculum year indicators
- [ ] Implement client-side validation warnings

### Step 4: Data Migration
- [ ] Create migration to fix existing conflicts
- [ ] Update existing enrollment records if necessary
- [ ] Add data validation constraints

### Step 5: Testing
- [ ] Unit tests for curriculum filtering logic
- [ ] Integration tests for enrollment process
- [ ] User acceptance testing with different curriculum scenarios

## 📁 Affected Files

- `app/Filament/Resources/StudentEnrollmentResource.php` (lines 435-482)
- `app/Models/Subject.php` (relationship methods)
- `app/Models/Course.php` (curriculum year handling)
- `app/Models/Classes.php` (subject relationship)
- `database/migrations/` (potential new migrations needed)

## 🧪 Test Cases

### Test Case 1: Curriculum-Specific Subject Filtering
- **Given:** Student from BSIT 2024-2025 curriculum
- **When:** Selecting subjects during enrollment
- **Then:** Only subjects from 2024-2025 curriculum should appear

### Test Case 2: Cross-Curriculum Enrollment Prevention
- **Given:** Student from BSIT 2018-2019 curriculum
- **When:** Attempting to enroll in IAS 201 (2024-2025 subject)
- **Then:** System should prevent enrollment and show appropriate error

### Test Case 3: Subject Display Clarity
- **Given:** Multiple subjects with similar names from different curricula
- **When:** Viewing subject dropdown
- **Then:** Each subject should clearly show its curriculum year

## 🔗 Related Issues

- Subject code conflicts across different courses
- Class scheduling conflicts between curriculum versions
- Student transfer between curriculum versions

## 📊 Acceptance Criteria

- [ ] Students can only enroll in subjects from their course's curriculum year
- [ ] Subject dropdown clearly displays curriculum information
- [ ] Backend validation prevents cross-curriculum enrollment
- [ ] Existing enrollment data integrity is maintained
- [ ] Performance impact is minimal
- [ ] Solution is scalable for future curriculum changes

## 🏷️ Labels

`bug` `high-priority` `enrollment` `curriculum` `subject-management` `data-integrity`

---

**Reporter:** System Administrator  
**Date:** 2025-01-14  
**Environment:** Production/Development  
**Browser:** All browsers affected  
**Database:** PostgreSQL