<?php

declare(strict_types=1);

namespace App\Rules;

use App\Models\Schedule;
use App\Services\GeneralSettingsService;
use Closure;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Validation\ValidationRule;

// use Illuminate\Translation\PotentiallyTranslatedString;

final class ScheduleOverlapRule implements ValidationRule
{
    private $conflictingSchedule;

    /**
     * Run the validation rule.
     *
     * @param  Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(
        string $attribute,
        mixed $value,
        Closure $fail
    ): void {
        $settings = app(GeneralSettingsService::class);
        // dd($attribute);
        foreach ($value as $schedule) {
            $query = Schedule::with('class')
                ->where('day_of_week', $schedule['day_of_week'])
                ->where('room_id', $schedule['room_id'])
                ->whereHas('class', function ($query) use ($settings): void {
                    $query->where('semester', $settings->getCurrentSemester());
                })
                ->where(function ($query) use ($schedule): void {
                    $query
                        ->where(function ($subQuery) use ($schedule): void {
                            // New schedule starts during another schedule
                            $subQuery
                                ->where(
                                    'start_time',
                                    '<=',
                                    $schedule['start_time']
                                )
                                ->where(
                                    'end_time',
                                    '>',
                                    $schedule['start_time']
                                );
                        })
                        ->orWhere(function ($subQuery) use ($schedule): void {
                            // New schedule ends during another schedule
                            $subQuery
                                ->where(
                                    'start_time',
                                    '<',
                                    $schedule['end_time']
                                )
                                ->where(
                                    'end_time',
                                    '>=',
                                    $schedule['end_time']
                                );
                        })
                        ->orWhere(function ($subQuery) use ($schedule): void {
                            // New schedule completely overlaps another schedule
                            $subQuery
                                ->where(
                                    'start_time',
                                    '>=',
                                    $schedule['start_time']
                                )
                                ->where(
                                    'end_time',
                                    '<=',
                                    $schedule['end_time']
                                );
                        });
                });

            if (isset($schedule['id'])) {
                $query->where('id', '!=', $schedule['id']);
            }

            $existingSchedule = $query->first();

            if ($existingSchedule) {
                $this->conflictingSchedule = $existingSchedule;
                break;
            }
        }

        if ($this->conflictingSchedule) {
            $fail($this->message());
            Notification::make()
                ->danger()
                ->title('Schedule Conflict')
                ->body($this->message())
                ->send()
                ->sendToDatabase(auth()->user());
        }
    }

    public function message(): string
    {
        if ($this->conflictingSchedule) {
            $className = $this->conflictingSchedule->class
                ? $this->conflictingSchedule->class->subject_code
                : 'Unknown Class';

            return "This schedule your are trying to add conflicts with another class on '{$className}' on ".
                $this->conflictingSchedule->day_of_week.
                ' in '.
                $this->conflictingSchedule->room->name.
                ' from '.
                $this->conflictingSchedule->start_time->format('h:i A').
                ' to '.
                $this->conflictingSchedule->end_time->format('h:i A').
                '.';
        }

        return 'The schedule conflicts with another schedule. ';
    }
}
