<?php

declare(strict_types=1);

namespace App\Providers\Filament;

use App\Filament\Faculty\Pages\Auth\FacultyRegister;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Nuxtifyts\DashStackTheme\DashStackThemePlugin;

final class FacultyPanelProvider extends PanelProvider
{
    public function register(): void
    {
        if (! config('app.enable_faculty_panel', false)) {
            return;
        }

        parent::register();
    }

    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('faculty')
            ->path('faculty')
            ->authGuard('faculty')
            ->authPasswordBroker('faculty')
            ->brandName('DCCP Faculty')
            ->login()
            ->registration(FacultyRegister::class)
            ->passwordReset()
            ->emailVerification()
            ->profile()
            ->colors([
                'primary' => Color::Blue,
            ])
            ->discoverResources(
                in: app_path('Filament/Faculty/Resources'),
                for: 'App\\Filament\\Faculty\\Resources'
            )
            ->discoverPages(
                in: app_path('Filament/Faculty/Pages'),
                for: 'App\\Filament\\Faculty\\Pages'
            )
            ->pages([Pages\Dashboard::class])
            ->plugins([DashStackThemePlugin::make()])
            ->discoverWidgets(
                in: app_path('Filament/Faculty/Widgets'),
                for: 'App\\Filament\\Faculty\\Widgets'
            )
            ->widgets([Widgets\AccountWidget::class])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([Authenticate::class]);
    }
}
