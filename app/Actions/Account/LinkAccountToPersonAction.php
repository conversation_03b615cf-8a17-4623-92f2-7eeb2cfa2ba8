<?php

declare(strict_types=1);

namespace App\Actions\Account;

use App\Models\Account;
use App\Models\Student;
use App\Models\Faculty;
use App\Models\ShsStudent;
use App\Services\AccountService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\ValidationException;

class LinkAccountToPersonAction
{
    public function __construct(
        private AccountService $accountService
    ) {}

    /**
     * Link an account to a person (Student, Faculty, or ShsStudent)
     */
    public function execute(Account $account, Model $person): Account
    {
        // Validate the person type
        if (!$this->isValidPersonType($person)) {
            throw ValidationException::withMessages([
                'person' => 'Invalid person type. Must be Student, Faculty, or ShsStudent.'
            ]);
        }

        // Check if account is already linked to this person
        if ($this->isAccountLinkedToPerson($account, $person)) {
            throw ValidationException::withMessages([
                'account' => 'Account is already linked to this person.'
            ]);
        }

        return $this->accountService->linkAccountToPerson($account, $person);
    }

    /**
     * Check if the person type is valid for linking
     */
    private function isValidPersonType(Model $person): bool
    {
        return $person instanceof Student || 
               $person instanceof Faculty || 
               $person instanceof ShsStudent;
    }

    /**
     * Check if account is already linked to the given person
     */
    private function isAccountLinkedToPerson(Account $account, Model $person): bool
    {
        if (!$account->person_id || !$account->person_type) {
            return false;
        }

        $personId = $this->getPersonId($person);
        $personType = get_class($person);

        return $account->person_id == $personId && $account->person_type === $personType;
    }

    /**
     * Get person ID based on person type
     */
    private function getPersonId(Model $person): mixed
    {
        if ($person instanceof Student) {
            return $person->id;
        }
        
        if ($person instanceof Faculty) {
            return $person->id;
        }
        
        if ($person instanceof ShsStudent) {
            return $person->student_lrn;
        }
        
        throw new \InvalidArgumentException('Unsupported person type: ' . get_class($person));
    }
}
