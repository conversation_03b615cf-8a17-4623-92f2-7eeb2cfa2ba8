<?php

declare(strict_types=1);

namespace App\Actions\Account;

use App\Models\Account;
use App\Models\Student;
use App\Models\Faculty;
use App\Models\ShsStudent;
use App\Services\AccountService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class CreateAccountAction
{
    public function __construct(
        private AccountService $accountService
    ) {}

    /**
     * Create a new account
     */
    public function execute(array $accountData, ?Model $person = null): Account
    {
        // Validate required fields
        $this->validateAccountData($accountData);

        // Generate username if not provided
        if (empty($accountData['username'])) {
            $accountData['username'] = $this->generateUsername($accountData['email']);
        }

        // Generate password if not provided
        if (empty($accountData['password'])) {
            $accountData['password'] = Str::random(12);
        }

        return $this->accountService->createAccount($accountData, $person);
    }

    /**
     * Create account with person linking
     */
    public function executeWithPerson(array $accountData, Model $person): Account
    {
        // Validate person type
        if (!$this->isValidPersonType($person)) {
            throw ValidationException::withMessages([
                'person' => 'Invalid person type. Must be Student, Faculty, or ShsStudent.'
            ]);
        }

        return $this->execute($accountData, $person);
    }

    /**
     * Validate account data
     */
    private function validateAccountData(array $accountData): void
    {
        if (empty($accountData['email'])) {
            throw ValidationException::withMessages([
                'email' => 'Email is required.'
            ]);
        }

        if (!filter_var($accountData['email'], FILTER_VALIDATE_EMAIL)) {
            throw ValidationException::withMessages([
                'email' => 'Invalid email format.'
            ]);
        }
    }

    /**
     * Generate username from email
     */
    private function generateUsername(string $email): string
    {
        $baseUsername = explode('@', $email)[0];
        $username = $baseUsername;
        $counter = 1;

        // Ensure username is unique
        while (Account::where('username', $username)->exists()) {
            $username = $baseUsername . $counter;
            $counter++;
        }

        return $username;
    }

    /**
     * Check if the person type is valid
     */
    private function isValidPersonType(Model $person): bool
    {
        return $person instanceof Student || 
               $person instanceof Faculty || 
               $person instanceof ShsStudent;
    }
}
