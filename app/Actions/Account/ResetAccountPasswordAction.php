<?php

declare(strict_types=1);

namespace App\Actions\Account;

use App\Models\Account;
use App\Services\AccountService;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class ResetAccountPasswordAction
{
    public function __construct(
        private AccountService $accountService
    ) {}

    /**
     * Reset account password with a provided password
     */
    public function execute(Account $account, string $newPassword): Account
    {
        // Validate password strength
        if (strlen($newPassword) < 8) {
            throw ValidationException::withMessages([
                'password' => 'Password must be at least 8 characters long.'
            ]);
        }

        return $this->accountService->resetPassword($account, $newPassword);
    }

    /**
     * Reset account password with a randomly generated password
     */
    public function executeWithRandomPassword(Account $account): array
    {
        $newPassword = $this->generateRandomPassword();
        
        $updatedAccount = $this->accountService->resetPassword($account, $newPassword);

        return [
            'account' => $updatedAccount,
            'password' => $newPassword
        ];
    }

    /**
     * Generate a random password
     */
    private function generateRandomPassword(int $length = 12): string
    {
        return Str::random($length);
    }
}
