<?php

declare(strict_types=1);

namespace App\Actions\Account;

use App\Models\Account;
use App\Services\AccountService;
use Illuminate\Validation\ValidationException;

class UnlinkAccountFromPersonAction
{
    public function __construct(
        private AccountService $accountService
    ) {}

    /**
     * Unlink an account from its associated person
     */
    public function execute(Account $account): Account
    {
        // Check if account is actually linked to a person
        if (!$this->isAccountLinked($account)) {
            throw ValidationException::withMessages([
                'account' => 'Account is not linked to any person.'
            ]);
        }

        return $this->accountService->unlinkAccountFromPerson($account);
    }

    /**
     * Check if account is linked to a person
     */
    private function isAccountLinked(Account $account): bool
    {
        return !empty($account->person_id) && !empty($account->person_type);
    }
}
