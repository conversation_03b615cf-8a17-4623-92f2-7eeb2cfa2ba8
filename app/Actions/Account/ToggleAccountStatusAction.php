<?php

declare(strict_types=1);

namespace App\Actions\Account;

use App\Models\Account;
use App\Services\AccountService;

class ToggleAccountStatusAction
{
    public function __construct(
        private AccountService $accountService
    ) {}

    /**
     * Toggle account active status
     */
    public function execute(Account $account): Account
    {
        if ($account->is_active) {
            return $this->accountService->deactivateAccount($account);
        }

        return $this->accountService->activateAccount($account);
    }

    /**
     * Activate an account
     */
    public function activate(Account $account): Account
    {
        return $this->accountService->activateAccount($account);
    }

    /**
     * Deactivate an account
     */
    public function deactivate(Account $account): Account
    {
        return $this->accountService->deactivateAccount($account);
    }
}
