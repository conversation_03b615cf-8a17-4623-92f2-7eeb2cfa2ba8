<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Models\Course;
use App\Models\StudentEnrollment;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

final class ClassStatsWidget extends ChartWidget
{
    protected static ?string $heading = 'Total Number of Enrollees per Course';

    public static function canView(): bool
    {
        return auth()->user()->can('widget_ClassStatsWidget');
    }

    protected function getData(): array
    {
        $enrollments = StudentEnrollment::select(
            'course_id',
            DB::raw('COUNT(*) as count')
        )
            ->groupBy('course_id')
            ->withTrashed()
            ->get();

        // Initialize arrays for each program
        $programData = [
            'BSIT' => 0,
            'BSBA' => 0,
            'BSHM' => 0,
        ];

        foreach ($enrollments as $enrollment) {
            $course = Course::find($enrollment->course_id);
            if ($course) {
                // Extract the program code (BSIT, BSBA, BSHM) from course code
                $programCode = explode(' ', (string) $course->code)[0];
                if (array_key_exists($programCode, $programData)) {
                    $programData[$programCode] += $enrollment->count;
                }
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Enrollments',
                    'data' => array_values($programData),
                    'backgroundColor' => [
                        '#FF6384', // Pink for BSIT
                        '#36A2EB', // Blue for BSBA
                        '#FFCE56', // Yellow for BSHM
                    ],
                    'borderColor' => ['#FF6384', '#36A2EB', '#FFCE56'],
                    'borderWidth' => 1,
                ],
            ],
            'labels' => array_keys($programData),
        ];
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'grid' => [
                        'display' => true,
                        'color' => 'rgba(200, 200, 200, 0.2)',
                    ],
                ],
                'x' => [
                    'grid' => [
                        'display' => false,
                    ],
                ],
            ],
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    // Add this method to control the height of the chart
    private function getHeight(): int|string|null
    {
        return 300;
    }
}
