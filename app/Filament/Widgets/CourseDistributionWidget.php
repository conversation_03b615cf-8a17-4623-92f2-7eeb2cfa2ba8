<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Models\Student;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

final class CourseDistributionWidget extends ChartWidget
{
    protected static ?string $heading = 'Students by Course';

    protected int|string|array $columnSpan = 1;

    public static function canView(): bool
    {
        return auth()->user()->can('widget_CourseDistributionWidget');
    }

    protected function getData(): array
    {
        $query = Student::select(
            'courses.code as course_name',
            DB::raw('count(*) as count')
        )->join('courses', 'students.course_id', '=', 'courses.id');

        if (
            isset($this->filters['academic_year']) &&
            $this->filters['academic_year'] !== 'all'
        ) {
            $query->where(
                'students.academic_year',
                $this->filters['academic_year']
            );
        }

        $data = $query
            ->groupBy('courses.id', 'courses.code')
            ->orderBy('count', 'desc')
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Number of Students',
                    'data' => $data->pluck('count')->toArray(),
                    'backgroundColor' => $this->generateColors(count($data)),
                ],
            ],
            'labels' => $data->pluck('course_name')->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => false,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'precision' => 0,
                    ],
                ],
            ],
            'maintainAspectRatio' => false,
        ];
    }

    private function generateColors(int $count): array
    {
        $colors = [
            '#FF6384', // Red
            '#36A2EB', // Blue
            '#FFCE56', // Yellow
            '#4BC0C0', // Green
            '#9966FF', // Purple
            '#FF9F40', // Orange
        ];

        $result = [];
        for ($i = 0; $i < $count; $i++) {
            $result[] = $colors[$i % count($colors)];
        }

        return $result;
    }
}
