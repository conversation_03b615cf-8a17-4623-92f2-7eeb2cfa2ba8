<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Models\StudentEnrollment;
use App\Services\GeneralSettingsService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

final class EnrollmentOverviewWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';

    public static function canView(): bool
    {
        return auth()->user()->can('widget_EnrollmentOverviewWidget');
    }

    protected function getStats(): array
    {
        $settingsService = new GeneralSettingsService;
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
        $currentSemester = $settingsService->getCurrentSemester();
        $totalEnrollments = StudentEnrollment::where(
            'school_year',
            $currentSchoolYear
        )
            ->where('semester', $currentSemester)
            ->withTrashed()
            ->count();
        // dd($totalEnrollments);

        $pendingEnrollments = StudentEnrollment::where(
            'school_year',
            $currentSchoolYear
        )
            ->where('semester', $currentSemester)
            ->where('status', 'Pending')
              ->withTrashed()
            ->count();

        $completedEnrollments = StudentEnrollment::where(
            'school_year',
            $currentSchoolYear
        )
            ->where('semester', $currentSemester)
            ->whereIn('status', ['Enrolled', 'Completed'])
              ->withTrashed()
            ->count();

        $completionRate =
            $totalEnrollments > 0
                ? number_format(
                    ($completedEnrollments / $totalEnrollments) * 100,
                    1
                )
                : 0;

        return [
            Stat::make('Current Enrollments', $totalEnrollments)
                ->description(
                    "Total enrollments for {$currentSchoolYear} Semester {$currentSemester}"
                )
                ->descriptionIcon('heroicon-m-document-text')
                ->chart([7, 3, 4, 5, 6, $totalEnrollments])
                ->color('primary'),

            Stat::make('Pending Enrollments', $pendingEnrollments)
                ->description('Awaiting processing or approval')
                ->descriptionIcon('heroicon-m-clock')
                ->chart([7, 3, 4, 5, 6, $pendingEnrollments])
                ->color('warning'),

            Stat::make('Completion Rate', $completionRate.'%')
                ->description("{$completedEnrollments} enrollments completed")
                ->descriptionIcon('heroicon-m-check-circle')
                ->chart([7, 3, 4, 5, 6, $completionRate])
                ->color('success'),
        ];
    }
}
