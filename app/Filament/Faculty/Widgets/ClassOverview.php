<?php

declare(strict_types=1);

namespace App\Filament\Faculty\Widgets;

use App\Models\ClassEnrollment;
use App\Models\Classes;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

final class ClassOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Classes', Classes::where('faculty_id', auth()->id())->count())
                ->icon('heroicon-o-rectangle-stack')
                ->description('Your current teaching load')
                ->chart([7, 2, 5, 8, 3])
                ->color('primary'),

            Stat::make('Pending Grades', ClassEnrollment::whereHas('class', fn ($q) => $q->where('faculty_id', auth()->id()))
                ->where('is_grades_finalized', false)
                ->count())
                ->icon('heroicon-o-clipboard-document')
                ->description('Require your attention')
                ->color('warning'),

            Stat::make('Verified Grades', ClassEnrollment::whereHas('class', fn ($q) => $q->where('faculty_id', auth()->id()))
                ->where('is_grades_verified', true)
                ->count())
                ->icon('heroicon-o-shield-check')
                ->description('Completed submissions')
                ->color('success'),
        ];
    }
}
