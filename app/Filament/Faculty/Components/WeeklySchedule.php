<?php

declare(strict_types=1);

namespace App\Filament\Faculty\Components;

use App\Models\Classes;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

final class WeeklySchedule extends Component
{
    public array $schedules = [];

    public array $days = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
    ];

    public function __construct(
        public ?Classes $class = null,
        $schedules = null,
        public bool $showEmptyDays = true,
        public bool $showRoomDetails = true
    ) {
        if ($schedules) {
            $this->schedules = $schedules;
        } elseif ($this->class instanceof Classes) {
            $this->schedules = $this->class->formatted_weekly_schedule;
        }
    }

    public function render(): View
    {
        $displayDays = $this->showEmptyDays
            ? $this->days
            : $this->filterEmptyDays();

        return view('filament.faculty.components.weekly-schedule', [
            'days' => $displayDays,
            'schedule' => $this->schedules,
            'showRoomDetails' => $this->showRoomDetails,
            'class' => $this->class,
        ]);
    }

    private function filterEmptyDays(): array
    {
        return array_filter($this->days, function ($day): bool {
            $dayLower = mb_strtolower($day);

            return isset($this->schedules[$dayLower]) &&
                count($this->schedules[$dayLower]) > 0;
        });
    }
}
