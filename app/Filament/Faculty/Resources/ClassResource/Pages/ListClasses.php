<?php

declare(strict_types=1);

namespace App\Filament\Faculty\Resources\ClassResource\Pages;

use App\Filament\Faculty\Resources\ClassResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListClasses extends ListRecords
{
    protected static string $resource = ClassResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
