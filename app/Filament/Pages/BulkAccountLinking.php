<?php

declare(strict_types=1);

namespace App\Filament\Pages;

use App\Models\Account;
use App\Models\Student;
use App\Models\Faculty;
use App\Models\ShsStudent;
use App\Services\AccountService;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Builder;

class BulkAccountLinking extends Page implements HasForms, HasTable
{
    use InteractsWithForms, InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-link';

    protected static string $view = 'filament.pages.bulk-account-linking';

    protected static ?string $title = 'Bulk Account Linking';

    protected static ?string $navigationLabel = 'Bulk Account Linking';

    protected static ?string $navigationGroup = 'Account Management';

    protected static ?int $navigationSort = 2;

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                TextColumn::make('name')
                    ->label('Account Name')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->copyable(),

                TextColumn::make('role')
                    ->label('Role')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'admin' => 'danger',
                        'faculty' => 'warning',
                        'student' => 'success',
                        'guest' => 'secondary',
                        default => 'secondary',
                    }),

                TextColumn::make('suggested_person')
                    ->label('Suggested Link')
                    ->getStateUsing(function (Account $record) {
                        return $this->getSuggestedPerson($record);
                    })
                    ->html(),
            ])
            ->actions([
                Action::make('link_suggested')
                    ->label('Link Suggested')
                    ->icon('heroicon-o-link')
                    ->color('success')
                    ->visible(fn (Account $record) => $this->hasSuggestedPerson($record))
                    ->action(function (Account $record, AccountService $accountService) {
                        $suggestion = $this->findSuggestedPerson($record);
                        
                        if ($suggestion) {
                            try {
                                $accountService->linkAccountToPerson($record, $suggestion);
                                
                                Notification::make()
                                    ->title('Account Linked Successfully')
                                    ->body("Account linked to {$this->getPersonDisplayName($suggestion)}")
                                    ->success()
                                    ->send();
                                    
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Linking Failed')
                                    ->body($e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        }
                    }),

                Action::make('manual_link')
                    ->label('Manual Link')
                    ->icon('heroicon-o-cog')
                    ->color('warning')
                    ->form([
                        Select::make('person_type')
                            ->label('Person Type')
                            ->options([
                                Student::class => 'College Student',
                                Faculty::class => 'Faculty Member',
                                ShsStudent::class => 'SHS Student',
                            ])
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn (callable $set) => $set('person_id', null)),

                        Select::make('person_id')
                            ->label('Select Person')
                            ->options(function (callable $get) {
                                $personType = $get('person_type');
                                
                                if ($personType === Student::class) {
                                    return Student::whereDoesntHave('account')
                                        ->get()
                                        ->mapWithKeys(fn ($student) => [
                                            $student->id => "{$student->first_name} {$student->last_name} (ID: {$student->id})"
                                        ]);
                                }
                                
                                if ($personType === Faculty::class) {
                                    return Faculty::whereNotIn('email', function ($query) {
                                            $query->select('email')
                                                ->from('accounts')
                                                ->where('person_type', Faculty::class)
                                                ->whereNotNull('email');
                                        })
                                        ->get()
                                        ->mapWithKeys(fn ($faculty) => [
                                            $faculty->id => "{$faculty->getFullNameAttribute()} ({$faculty->email})"
                                        ]);
                                }
                                
                                if ($personType === ShsStudent::class) {
                                    return ShsStudent::whereDoesntHave('account')
                                        ->get()
                                        ->mapWithKeys(fn ($student) => [
                                            $student->student_lrn => "{$student->fullname} (LRN: {$student->student_lrn})"
                                        ]);
                                }
                                
                                return [];
                            })
                            ->searchable()
                            ->required()
                            ->visible(fn (callable $get) => filled($get('person_type'))),
                    ])
                    ->action(function (Account $record, array $data, AccountService $accountService) {
                        try {
                            $personType = $data['person_type'];
                            $personId = $data['person_id'];
                            
                            $person = $personType::find($personId);
                            
                            if (!$person) {
                                throw new \Exception('Person not found');
                            }
                            
                            $accountService->linkAccountToPerson($record, $person);
                            
                            Notification::make()
                                ->title('Account Linked Successfully')
                                ->body("Account linked to {$this->getPersonDisplayName($person)}")
                                ->success()
                                ->send();
                                
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Linking Failed')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                BulkAction::make('link_all_suggested')
                    ->label('Link All Suggested')
                    ->icon('heroicon-o-link')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Link All Suggested Accounts')
                    ->modalDescription('This will link all selected accounts to their suggested persons. Are you sure?')
                    ->action(function (Collection $records, AccountService $accountService) {
                        $linked = 0;
                        $failed = 0;
                        
                        foreach ($records as $record) {
                            $suggestion = $this->findSuggestedPerson($record);
                            
                            if ($suggestion) {
                                try {
                                    $accountService->linkAccountToPerson($record, $suggestion);
                                    $linked++;
                                } catch (\Exception) {
                                    $failed++;
                                }
                            }
                        }
                        
                        Notification::make()
                            ->title('Bulk Linking Complete')
                            ->body("Successfully linked {$linked} accounts. {$failed} failed.")
                            ->success()
                            ->send();
                    }),
            ]);
    }

    protected function getTableQuery(): Builder
    {
        return Account::query()
            ->whereNull('person_id')
            ->orWhereNull('person_type');
    }

    /**
     * Get suggested person for an account
     */
    private function getSuggestedPerson(Account $account): string
    {
        $suggestion = $this->findSuggestedPerson($account);
        
        if (!$suggestion) {
            return '<span class="text-gray-500">No suggestion</span>';
        }
        
        $personType = match (get_class($suggestion)) {
            Student::class => 'Student',
            Faculty::class => 'Faculty',
            ShsStudent::class => 'SHS Student',
            default => 'Unknown'
        };
        
        return "<span class=\"text-green-600 font-medium\">{$personType}: {$this->getPersonDisplayName($suggestion)}</span>";
    }

    /**
     * Check if account has a suggested person
     */
    private function hasSuggestedPerson(Account $account): bool
    {
        return $this->findSuggestedPerson($account) !== null;
    }

    /**
     * Find suggested person for an account based on email matching
     */
    private function findSuggestedPerson(Account $account): ?object
    {
        if (!$account->email) {
            return null;
        }
        
        // Try to find matching student
        $student = Student::where('email', $account->email)->first();
        if ($student && !$student->account()->exists()) {
            return $student;
        }

        // Try to find matching faculty
        $faculty = Faculty::where('email', $account->email)->first();
        if ($faculty) {
            // Check if faculty already has an account by checking if any account exists with this email and Faculty person_type
            $existingAccount = Account::where('email', $faculty->email)
                ->where('person_type', Faculty::class)
                ->exists();
            if (!$existingAccount) {
                return $faculty;
            }
        }

        // Try to find matching SHS student
        $shsStudent = ShsStudent::where('email', $account->email)->first();
        if ($shsStudent && !$shsStudent->account()->exists()) {
            return $shsStudent;
        }
        
        return null;
    }

    /**
     * Get display name for a person
     */
    private function getPersonDisplayName($person): string
    {
        if ($person instanceof Student) {
            return "{$person->first_name} {$person->last_name}";
        }
        
        if ($person instanceof Faculty) {
            return $person->getFullNameAttribute();
        }
        
        if ($person instanceof ShsStudent) {
            return $person->fullname ?? 'Unknown';
        }
        
        return 'Unknown';
    }
}
