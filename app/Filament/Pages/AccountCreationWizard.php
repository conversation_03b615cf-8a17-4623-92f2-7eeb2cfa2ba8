<?php

declare(strict_types=1);

namespace App\Filament\Pages;

use App\Models\Account;
use App\Models\Student;
use App\Models\Faculty;
use App\Models\ShsStudent;
use App\Models\Course;
use App\Models\ShsStrand;
use App\Actions\Account\CreateAccountAction;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Notifications\Notification;
use Illuminate\Support\Str;

class AccountCreationWizard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-user-plus';

    protected static string $view = 'filament.pages.account-creation-wizard';

    protected static ?string $title = 'Account Creation Wizard';

    protected static ?string $navigationLabel = 'Create Account Wizard';

    protected static ?string $navigationGroup = 'Account Management';

    protected static ?int $navigationSort = 3;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Wizard::make([
                    Step::make('Account Type')
                        ->description('Choose how to create the account')
                        ->schema([
                            Select::make('creation_type')
                                ->label('Account Creation Type')
                                ->options([
                                    'standalone' => 'Create standalone account only',
                                    'link_existing' => 'Create account and link to existing person',
                                    'create_with_person' => 'Create account with new person data',
                                ])
                                ->required()
                                ->reactive()
                                ->afterStateUpdated(fn (callable $set) => [
                                    $set('person_type', null),
                                    $set('existing_person_id', null),
                                ]),

                            Select::make('person_type')
                                ->label('Person Type')
                                ->options([
                                    'student' => 'College Student',
                                    'faculty' => 'Faculty Member',
                                    'shs_student' => 'SHS Student',
                                ])
                                ->visible(fn (callable $get) => in_array($get('creation_type'), ['link_existing', 'create_with_person']))
                                ->required(fn (callable $get) => in_array($get('creation_type'), ['link_existing', 'create_with_person']))
                                ->reactive()
                                ->afterStateUpdated(fn (callable $set) => $set('existing_person_id', null)),

                            Select::make('existing_person_id')
                                ->label('Select Existing Person')
                                ->options(function (callable $get) {
                                    $personType = $get('person_type');
                                    
                                    if ($personType === 'student') {
                                        return Student::whereDoesntHave('account')
                                            ->get()
                                            ->mapWithKeys(fn ($student) => [
                                                $student->id => "{$student->first_name} {$student->last_name} (ID: {$student->id})"
                                            ]);
                                    }
                                    
                                    if ($personType === 'faculty') {
                                        return Faculty::whereNotIn('email', function ($query) {
                                                $query->select('email')
                                                    ->from('accounts')
                                                    ->where('person_type', Faculty::class)
                                                    ->whereNotNull('email');
                                            })
                                            ->get()
                                            ->mapWithKeys(fn ($faculty) => [
                                                $faculty->id => "{$faculty->getFullNameAttribute()} ({$faculty->email})"
                                            ]);
                                    }
                                    
                                    if ($personType === 'shs_student') {
                                        return ShsStudent::whereDoesntHave('account')
                                            ->get()
                                            ->mapWithKeys(fn ($student) => [
                                                $student->student_lrn => "{$student->fullname} (LRN: {$student->student_lrn})"
                                            ]);
                                    }
                                    
                                    return [];
                                })
                                ->searchable()
                                ->visible(fn (callable $get) => $get('creation_type') === 'link_existing' && filled($get('person_type')))
                                ->required(fn (callable $get) => $get('creation_type') === 'link_existing'),
                        ]),

                    Step::make('Account Information')
                        ->description('Enter account details')
                        ->schema([
                            Section::make('Basic Account Information')
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            TextInput::make('name')
                                                ->label('Full Name')
                                                ->required()
                                                ->maxLength(255),

                                            TextInput::make('username')
                                                ->label('Username')
                                                ->required()
                                                ->unique(Account::class, 'username')
                                                ->maxLength(255)
                                                ->alphaDash()
                                                ->helperText('Leave empty to auto-generate from email'),

                                            TextInput::make('email')
                                                ->label('Email Address')
                                                ->email()
                                                ->required()
                                                ->unique(Account::class, 'email')
                                                ->maxLength(255),

                                            TextInput::make('phone')
                                                ->label('Phone Number')
                                                ->tel()
                                                ->maxLength(20),

                                            TextInput::make('password')
                                                ->label('Password')
                                                ->password()
                                                ->required()
                                                ->minLength(8)
                                                ->helperText('Minimum 8 characters'),

                                            Select::make('role')
                                                ->label('Role')
                                                ->options([
                                                    'admin' => 'Administrator',
                                                    'faculty' => 'Faculty',
                                                    'student' => 'Student',
                                                    'guest' => 'Guest',
                                                ])
                                                ->required()
                                                ->default(function (callable $get) {
                                                    return match ($get('person_type')) {
                                                        'student', 'shs_student' => 'student',
                                                        'faculty' => 'faculty',
                                                        default => 'guest',
                                                    };
                                                }),
                                        ]),
                                ]),

                            Section::make('Account Settings')
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            Toggle::make('is_active')
                                                ->label('Account Active')
                                                ->default(true),

                                            Toggle::make('is_notification_active')
                                                ->label('Notifications Enabled')
                                                ->default(true),
                                        ]),
                                ]),
                        ]),

                    Step::make('Person Information')
                        ->description('Enter person details')
                        ->visible(fn (callable $get) => $get('creation_type') === 'create_with_person')
                        ->schema([
                            Section::make('Student Information')
                                ->visible(fn (callable $get) => $get('person_type') === 'student')
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            TextInput::make('student_first_name')
                                                ->label('First Name')
                                                ->required(),

                                            TextInput::make('student_last_name')
                                                ->label('Last Name')
                                                ->required(),

                                            TextInput::make('student_middle_name')
                                                ->label('Middle Name'),

                                            Select::make('student_gender')
                                                ->label('Gender')
                                                ->options([
                                                    'Male' => 'Male',
                                                    'Female' => 'Female',
                                                ])
                                                ->required(),

                                            DatePicker::make('student_birth_date')
                                                ->label('Birth Date')
                                                ->required(),

                                            Select::make('course_id')
                                                ->label('Course')
                                                ->options(Course::pluck('title', 'id'))
                                                ->searchable()
                                                ->required(),

                                            TextInput::make('student_address')
                                                ->label('Address')
                                                ->columnSpanFull(),
                                        ]),
                                ]),

                            Section::make('Faculty Information')
                                ->visible(fn (callable $get) => $get('person_type') === 'faculty')
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            TextInput::make('faculty_first_name')
                                                ->label('First Name')
                                                ->required(),

                                            TextInput::make('faculty_last_name')
                                                ->label('Last Name')
                                                ->required(),

                                            TextInput::make('faculty_middle_name')
                                                ->label('Middle Name'),

                                            TextInput::make('faculty_department')
                                                ->label('Department'),

                                            DatePicker::make('faculty_birth_date')
                                                ->label('Birth Date'),

                                            TextInput::make('faculty_phone')
                                                ->label('Phone Number')
                                                ->tel(),

                                            Textarea::make('faculty_biography')
                                                ->label('Biography')
                                                ->columnSpanFull(),
                                        ]),
                                ]),

                            Section::make('SHS Student Information')
                                ->visible(fn (callable $get) => $get('person_type') === 'shs_student')
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            TextInput::make('shs_student_lrn')
                                                ->label('LRN (Learner Reference Number)')
                                                ->required()
                                                ->unique(ShsStudent::class, 'student_lrn'),

                                            TextInput::make('shs_fullname')
                                                ->label('Full Name')
                                                ->required(),

                                            Select::make('shs_gender')
                                                ->label('Gender')
                                                ->options([
                                                    'Male' => 'Male',
                                                    'Female' => 'Female',
                                                ])
                                                ->required(),

                                            Select::make('shs_grade_level')
                                                ->label('Grade Level')
                                                ->options([
                                                    'Grade 11' => 'Grade 11',
                                                    'Grade 12' => 'Grade 12',
                                                ])
                                                ->required(),

                                            DatePicker::make('shs_birth_date')
                                                ->label('Birth Date'),

                                            Select::make('strand_id')
                                                ->label('Strand')
                                                ->options(ShsStrand::pluck('strand_name', 'id'))
                                                ->searchable(),

                                            TextInput::make('shs_address')
                                                ->label('Complete Address')
                                                ->columnSpanFull(),
                                        ]),
                                ]),
                        ]),

                    Step::make('Review & Create')
                        ->description('Review and create the account')
                        ->schema([
                            Section::make('Review Information')
                                ->schema([
                                    // This will be populated dynamically in the view
                                ]),
                        ]),
                ])
                ->submitAction(\Filament\Actions\Action::make('submit')
                    ->label('Create Account')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->submit('create')),
            ])
            ->statePath('data');
    }

    public function create(): void
    {
        $data = $this->form->getState();
        
        try {
            $createAccountAction = app(CreateAccountAction::class);
            
            // Prepare account data
            $accountData = [
                'name' => $data['name'],
                'username' => $data['username'] ?: $this->generateUsername($data['email']),
                'email' => $data['email'],
                'phone' => $data['phone'] ?? null,
                'password' => $data['password'],
                'role' => $data['role'],
                'is_active' => $data['is_active'] ?? true,
                'is_notification_active' => $data['is_notification_active'] ?? true,
            ];
            
            $person = null;
            
            // Handle person creation or linking
            if ($data['creation_type'] === 'link_existing') {
                $person = $this->findExistingPerson($data);
            } elseif ($data['creation_type'] === 'create_with_person') {
                $person = $this->createNewPerson($data);
            }
            
            // Create account
            $account = $createAccountAction->execute($accountData, $person);
            
            Notification::make()
                ->title('Account Created Successfully')
                ->body("Account has been created for {$account->name}")
                ->success()
                ->send();
                
            $this->redirect(route('filament.admin.resources.accounts.view', $account));
            
        } catch (\Exception $e) {
            Notification::make()
                ->title('Account Creation Failed')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    private function findExistingPerson(array $data): ?object
    {
        $personType = $data['person_type'];
        $personId = $data['existing_person_id'];
        
        return match ($personType) {
            'student' => Student::find($personId),
            'faculty' => Faculty::find($personId),
            'shs_student' => ShsStudent::where('student_lrn', $personId)->first(),
            default => null,
        };
    }

    private function createNewPerson(array $data): ?object
    {
        $personType = $data['person_type'];
        
        return match ($personType) {
            'student' => $this->createStudent($data),
            'faculty' => $this->createFaculty($data),
            'shs_student' => $this->createShsStudent($data),
            default => null,
        };
    }

    private function createStudent(array $data): Student
    {
        return Student::create([
            'first_name' => $data['student_first_name'],
            'last_name' => $data['student_last_name'],
            'middle_name' => $data['student_middle_name'] ?? null,
            'gender' => $data['student_gender'],
            'birth_date' => $data['student_birth_date'],
            'address' => $data['student_address'] ?? null,
            'course_id' => $data['course_id'],
            'email' => $data['email'],
            'academic_year' => 1, // Default to 1st year
        ]);
    }

    private function createFaculty(array $data): Faculty
    {
        return Faculty::create([
            'id' => Str::uuid(),
            'first_name' => $data['faculty_first_name'],
            'last_name' => $data['faculty_last_name'],
            'middle_name' => $data['faculty_middle_name'] ?? null,
            'email' => $data['email'],
            'phone_number' => $data['faculty_phone'] ?? null,
            'department' => $data['faculty_department'] ?? null,
            'birth_date' => $data['faculty_birth_date'] ?? null,
            'biography' => $data['faculty_biography'] ?? null,
        ]);
    }

    private function createShsStudent(array $data): ShsStudent
    {
        return ShsStudent::create([
            'student_lrn' => $data['shs_student_lrn'],
            'fullname' => $data['shs_fullname'],
            'gender' => $data['shs_gender'],
            'grade_level' => $data['shs_grade_level'],
            'birthdate' => $data['shs_birth_date'] ?? null,
            'complete_address' => $data['shs_address'] ?? null,
            'strand_id' => $data['strand_id'] ?? null,
            'email' => $data['email'],
        ]);
    }

    private function generateUsername(string $email): string
    {
        $baseUsername = explode('@', $email)[0];
        $username = $baseUsername;
        $counter = 1;

        while (Account::where('username', $username)->exists()) {
            $username = $baseUsername . $counter;
            $counter++;
        }

        return $username;
    }
}
