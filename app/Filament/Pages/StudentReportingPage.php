<?php

declare(strict_types=1);

namespace App\Filament\Pages;

use App\Services\StudentReportingService;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class StudentReportingPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Student Analytics';

    protected static ?string $title = 'Student Analytics Dashboard';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.pages.student-reporting-page';

    public array $reportData = [];

    protected ?StudentReportingService $reportingService = null;

    public function mount(): void
    {
        try {
            $this->reportingService = app(StudentReportingService::class);

            // Load report data
            $this->loadReportData();
        } catch (\Exception $e) {
            // If there's an error, set empty report data
            $this->reportData = [];

            Notification::make()
                ->title('Error Loading Data')
                ->body('Unable to load report data: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function loadReportData(): void
    {
        if (!$this->reportingService) {
            $this->reportingService = app(StudentReportingService::class);
        }

        $this->reportData = $this->reportingService->generateDashboardReport();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('exportData')
                ->label('Export Data')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->form([
                    Section::make('Export Options')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    Select::make('format')
                                        ->label('Export Format')
                                        ->options([
                                            'csv' => 'Excel (CSV)',
                                            'pdf' => 'PDF Document',
                                        ])
                                        ->default('csv')
                                        ->required(),

                                    Select::make('course_filter')
                                        ->label('Course Filter')
                                        ->options([
                                            'all' => 'All Courses',
                                            'BSIT' => 'BSIT Only',
                                            'BSBA' => 'BSBA Only',
                                            'BSHM' => 'BSHM Only',
                                        ])
                                        ->default('all')
                                        ->required(),
                                ]),

                            Grid::make(2)
                                ->schema([
                                    Select::make('year_level_filter')
                                        ->label('Year Level Filter')
                                        ->options([
                                            'all' => 'All Year Levels',
                                            '1' => '1st Year Only',
                                            '2' => '2nd Year Only',
                                            '3' => '3rd Year Only',
                                            '4' => '4th Year Only',
                                        ])
                                        ->default('all')
                                        ->required(),

                                    Checkbox::make('preview_mode')
                                        ->label('Preview Only (First 10 Records)')
                                        ->default(false),
                                ]),
                        ])
                ])
                ->action(function (array $data): void {
                    $this->handleExport($data);
                }),

            Action::make('refreshData')
                ->label('Refresh Data')
                ->icon('heroicon-o-arrow-path')
                ->action(function (): void {
                    $this->loadReportData();
                }),
        ];
    }

    /**
     * Handle the export action
     */
    public function handleExport(array $data): void
    {
        try {
            // Ensure we have the reporting service
            if (!$this->reportingService) {
                $this->reportingService = app(StudentReportingService::class);
            }

            // Prepare filters from form data
            $filters = [
                'course_filter' => $data['course_filter'] ?? 'all',
                'year_level_filter' => $data['year_level_filter'] ?? 'all',
                'preview_mode' => $data['preview_mode'] ?? false,
            ];

            // Handle preview mode
            if (isset($data['preview_mode']) && $data['preview_mode']) {
                // Generate preview data
                $preview = $this->reportingService->generateExportPreview($filters);

                // Store in session for display
                session(['export_preview' => $preview]);

                // Notify user
                Notification::make()
                    ->title('Export Preview Generated')
                    ->body('Preview shows ' . count($preview['students']) . ' of ' . $preview['total_count'] . ' total records.')
                    ->info()
                    ->send();
            } else {
                // Queue the actual export job
                $format = $data['format'] ?? 'csv';
                $userId = Auth::id() ?? 1; // Fallback to admin user if not authenticated

                $exportJobId = $this->reportingService->queueExport($filters, $format, $userId);

                // Notify user
                Notification::make()
                    ->title('Export Queued Successfully')
                    ->body('Your export has been queued for processing. You will receive a notification when it\'s ready.')
                    ->success()
                    ->send();
            }
        } catch (\Exception $e) {
            // Log the error
            Log::error('Export failed: ' . $e->getMessage(), [
                'exception' => $e,
                'data' => $data,
            ]);

            // Notify user
            Notification::make()
                ->title('Export Failed')
                ->body('Failed to process export: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    // public static function canAccess(): bool
    // {
    //     return auth()->user()->can('view_student_reports') || auth()->user()->hasRole('admin');
    // }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }
}
