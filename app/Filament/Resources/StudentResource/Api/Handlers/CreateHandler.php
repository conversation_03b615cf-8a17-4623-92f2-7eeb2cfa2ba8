<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentResource\Api\Handlers;

use App\Filament\Resources\StudentResource;
use App\Filament\Resources\StudentResource\Api\Requests\CreateStudentRequest;
use Rupadana\ApiService\Http\Handlers;

final class CreateHandler extends Handlers
{
    public static ?string $uri = '/';

    public static ?string $resource = StudentResource::class;

    public static function getMethod()
    {
        return Handlers::POST;
    }

    public static function getModel()
    {
        return self::$resource::getModel();
    }

    /**
     * Create Student
     */
    public function handler(CreateStudentRequest $request): \Illuminate\Http\JsonResponse
    {
        $model = new (self::getModel());

        $model->fill($request->all());

        $model->save();

        return self::sendSuccessResponse($model, 'Successfully Create Resource');
    }
}
