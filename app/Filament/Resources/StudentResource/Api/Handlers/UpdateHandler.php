<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentResource\Api\Handlers;

use App\Filament\Resources\StudentResource;
use App\Filament\Resources\StudentResource\Api\Requests\UpdateStudentRequest;
use Rupadana\ApiService\Http\Handlers;

final class UpdateHandler extends Handlers
{
    public static ?string $uri = '/{id}';

    public static ?string $resource = StudentResource::class;

    public static function getMethod()
    {
        return Handlers::PUT;
    }

    public static function getModel()
    {
        return self::$resource::getModel();
    }

    /**
     * Update Student
     */
    public function handler(UpdateStudentRequest $request): \Illuminate\Http\JsonResponse
    {
        $id = $request->route('id');

        $model = self::getModel()::find($id);

        if (! $model) {
            return self::sendNotFoundResponse();
        }

        $model->fill($request->all());

        $model->save();

        return self::sendSuccessResponse($model, 'Successfully Update Resource');
    }
}
