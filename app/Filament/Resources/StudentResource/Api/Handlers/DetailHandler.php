<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentResource\Api\Handlers;

use App\Filament\Resources\StudentResource;
use App\Filament\Resources\StudentResource\Api\Transformers\StudentTransformer;
use Illuminate\Http\Request;
use Rupadana\ApiService\Http\Handlers;
use Spatie\QueryBuilder\QueryBuilder;

final class DetailHandler extends Handlers
{
    public static ?string $uri = '/{id}';

    public static ?string $resource = StudentResource::class;

    /**
     * Show Student
     *
     * @return StudentTransformer
     */
    public function handler(Request $request): \Illuminate\Http\JsonResponse|StudentTransformer
    {
        $id = $request->route('id');

        $query = self::getEloquentQuery();

        $query = QueryBuilder::for(
            $query->where(self::getKeyName(), $id)
        )
            ->first();

        if (! $query) {
            return self::sendNotFoundResponse();
        }

        return new StudentTransformer($query);
    }
}
