<?php

namespace App\Filament\Resources\StudentResource\Widgets;

use App\Models\Student;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

final class StudentsWidget extends BaseWidget
{
    // Increase polling interval or disable real-time updates
    protected static ?string $pollingInterval = null;

    protected int|string|array $columnSpan = "full"; // Or however many columns it should span

    // Cache chart data
    protected function getStats(): array
    {
        $stats = cache()
            ->tags(["student_stats"])
            ->remember("student_stats", 24 * 3600, function (): array {
                $query = Student::query();

                return [
                    "total" => $query->count(),
                    "average_age" => $query->average("age"),
                    "gender_counts" => $query
                        ->selectRaw("gender, COUNT(*) as count")
                        ->groupBy("gender")
                        ->pluck("count", "gender"),
                ];
            });

        $totalStudents = $stats["total"];
        $averageAge = $stats["average_age"];
        $genderCounts = $stats["gender_counts"];
        $maleCount = $genderCounts->get("male", 0);
        $femaleCount = $genderCounts->get("female", 0);

        // Simplified static chart data instead of dynamic calculations
        $chartData = [
            "registrations" => [10, 20, 30, 40, 50, 60, 70],
            "ages" => [20, 21, 22, 23, 24, 25, 26],
            "gender" => [
                "male" => [5, 10, 15, 20, 25, 30, 35],
                "female" => [5, 10, 15, 20, 25, 30, 35],
            ],
        ];

        return [
            Stat::make("Total Students", number_format($totalStudents))
                ->description("Total registered students")
                ->icon("heroicon-s-user-group")
                ->color("primary")
                ->chart($chartData["registrations"]),

            Stat::make("Average Age", number_format($averageAge, 1) . " years")
                ->description("Average age of students")
                ->icon("heroicon-s-calendar")
                ->color("success")
                ->chart($chartData["ages"]),

            Stat::make("Male Students", number_format($maleCount))
                ->description("Total male students")
                ->icon("heroicon-s-user")
                ->color("info")
                ->chart($chartData["gender"]["male"]),

            Stat::make("Female Students", number_format($femaleCount))
                ->description("Total female students")
                ->icon("heroicon-s-user")
                ->color("warning")
                ->chart($chartData["gender"]["female"]),
        ];
    }

    private function getMonthlyRegistrations(): array
    {
        return cache()->remember(
            "monthly_registrations",
            24 * 3600,
            fn() => Student::query()
                ->selectRaw("MONTH(created_at) as month, COUNT(*) as count")
                ->whereYear("created_at", now()->year)
                ->groupBy("month")
                ->orderBy("month")
                ->pluck("count")
                ->toArray()
        );
    }

    private function getMonthlyAverageAges(): array
    {
        return cache()->remember(
            "monthly_average_ages",
            24 * 3600,
            fn() => Student::query()
                ->selectRaw(
                    "MONTH(created_at) as month, AVG(age) as average_age"
                )
                ->whereYear("created_at", now()->year)
                ->groupBy("month")
                ->orderBy("month")
                ->pluck("average_age")
                ->map(fn($age): float => round($age, 1))
                ->toArray()
        );
    }

    private function getMonthlyGenderCounts(): array
    {
        return cache()->remember(
            "monthly_gender_counts",
            24 * 3600,
            function (): array {
                $counts = Student::query()
                    ->selectRaw(
                        "MONTH(created_at) as month, gender, COUNT(*) as count"
                    )
                    ->whereYear("created_at", now()->year)
                    ->groupBy("month", "gender")
                    ->orderBy("month")
                    ->get()
                    ->groupBy("gender");

                return [
                    "male" => $counts
                        ->get("male", collect())
                        ->pluck("count")
                        ->toArray(),
                    "female" => $counts
                        ->get("female", collect())
                        ->pluck("count")
                        ->toArray(),
                ];
            }
        );
    }
}
