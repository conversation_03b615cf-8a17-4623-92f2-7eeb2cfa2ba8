<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentResource\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Database\Eloquent\Model;

final class StudentStats extends BaseWidget
{
    public ?Model $record = null;

    protected function getStats(): array
    {
        $currentSchoolYear = config('app.school_year');
        $currentSemester = config('app.semester');

        $studentTuition = $this->record->StudentTuition()
            ->where('school_year', $currentSchoolYear)
            ->where('semester', $currentSemester)
            ->first();

        $totalTuition = $studentTuition->total_tuition ?? 0;
        $totalBalance = $studentTuition->total_balance ?? 0;
        $totalLectures = $studentTuition->total_lectures ?? 0;
        $totalLaboratory = $studentTuition->total_laboratory ?? 0;
        $totalMiscellaneousFees = $studentTuition->total_miscellaneous_fees ?? 0;
        // $totalSubjects = count($this->record->subjects) ?? 0;

        // $completedSubjects = $this->record->subjectEnrolled->whereNotNull('grade')->count() ?? 0;
        // $inProgressSubjects = $this->record->subjectEnrolled->whereNull('grade')->count() ?? 0;
        // $incompleteSubjects = $this->record->subjects->count() - $completedSubjects - $inProgressSubjects ?? 0;

        return [
            Stat::make('Total Tuition', number_format($totalTuition))
                ->description('Total tuition fees')
                ->descriptionIcon('heroicon-o-banknotes')
                ->color('primary'),

            Stat::make('Total Balance', number_format($totalBalance))
                ->description('Students remaining balance')
                ->descriptionIcon('heroicon-o-banknotes')
                ->color('danger'),

            Stat::make('Total Lectures', number_format($totalLectures))
                ->label('Total Lecture Fees')
                ->description('Students total lecture fees')
                ->descriptionIcon('heroicon-o-banknotes')
                ->color('success'),

            Stat::make('Total Laboratory Fees', number_format($totalLaboratory))
                ->description('Students total laboratory fees')
                ->descriptionIcon('heroicon-o-banknotes')
                ->color('warning'),

            Stat::make('Miscellaneous Fees', number_format($totalMiscellaneousFees))
                ->description('Total miscellaneous fees')
                ->descriptionIcon('heroicon-o-banknotes')
                ->color('info'),
            // Stat::make('Total Subjects', $totalSubjects)
            //     ->description('Total subjects in the course')
            //     ->descriptionIcon('heroicon-o-academic-cap')
            //     ->color('primary'),
            // Stat::make('Completed Subjects', $completedSubjects)
            //     ->description('Subjects with a grade')
            //     ->descriptionIcon('heroicon-o-check-circle')
            //     ->color('success'),
            // Stat::make('In-Progress Subjects', $inProgressSubjects)
            //     ->description('Enrolled subjects without a grade')
            //     ->descriptionIcon('heroicon-o-clock')
            //     ->color('warning'),
            // Stat::make('Incomplete Subjects', $incompleteSubjects)
            //     ->description('Subjects not yet enrolled')
            //     ->descriptionIcon('heroicon-o-x-circle')
            //     ->color('danger'),

        ];
    }
}
