<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

final class StudentTuitionRelationManager extends RelationManager
{
    protected static string $relationship = 'StudentTuition';

    protected static bool $isLazy = false;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('overall_tuition')
                    ->required()
                    ->numeric()
                    ->maxLength(255),
                Forms\Components\TextInput::make('total_balance')
                    ->required()
                    ->numeric()
                    ->maxLength(255),
                Forms\Components\TextInput::make('total_lectures')
                    ->required()
                    ->numeric()
                    ->maxLength(255),
                Forms\Components\TextInput::make('total_laboratory')
                    ->required()
                    ->numeric()
                    ->maxLength(255),
                Forms\Components\TextInput::make('total_miscelaneous_fees')
                    ->required()
                    ->numeric()
                    ->maxLength(255),
                Forms\Components\TextInput::make('status')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('semester')
                    ->options([
                        '1' => '1st Semester',
                        '2' => '2nd Semester',
                    ])
                    ->required(),
                Forms\Components\Select::make('school_year')
                    ->required()
                    ->label('School Year')
                    ->options(function (): array {
                        $startYear = 2000;
                        $endYear = now()->year;
                        $years = [];
                        for ($year = $startYear; $year <= $endYear; $year++) {
                            $years["$year - ".($year + 1)] = "$year - ".($year + 1);
                        }

                        return $years;
                    }),
                Forms\Components\Select::make('academic_year')
                    ->options([
                        '1' => '1st Year',
                        '2' => '2nd Year',
                        '3' => '3rd Year',
                        '4' => '4th Year',
                    ])
                    ->required(),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                Tables\Columns\TextColumn::make('total_tuition'),
                Tables\Columns\TextColumn::make('total_balance'),
                Tables\Columns\TextColumn::make('total_lectures'),
                Tables\Columns\TextColumn::make('total_laboratory'),
                Tables\Columns\TextColumn::make('total_miscelaneous_fees'),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('semester'),
                Tables\Columns\TextColumn::make('school_year'),
                Tables\Columns\TextColumn::make('academic_year'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
