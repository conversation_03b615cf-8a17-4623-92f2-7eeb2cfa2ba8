<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentResource\RelationManagers;

use App\Models\Subject;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\InfoList;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

final class SubjectsRelationManager extends RelationManager
{
    protected static string $relationship = 'Subjects';

    protected static bool $isLazy = false;

    // public function form(Form $form): Form
    // {
    //     return $form
    //         ->schema([
    //             Forms\Components\TextInput::make('code')
    //                 ->required()
    //                 ->maxLength(255),
    //             Forms\Components\TextInput::make('title')
    //                 ->required()
    //                 ->maxLength(255),
    //             Forms\Components\TextInput::make('units')
    //                 ->required()
    //                 ->numeric(),
    //             Forms\Components\TextInput::make('lecture')
    //                 ->required()
    //                 ->numeric(),
    //             Forms\Components\TextInput::make('laboratory')
    //                 ->numeric(),
    //             Forms\Components\TextInput::make('pre_riquisite')
    //                 ->json(),
    //             Forms\Components\TextInput::make('academic_year')
    //                 ->required()
    //                 ->numeric(),
    //             Forms\Components\TextInput::make('semester')
    //                 ->required()
    //                 ->numeric(),
    //             Forms\Components\Select::make('course_id')
    //                 ->relationship('course', 'code')
    //                 ->required(),
    //         ]);
    // }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code'),
                Tables\Columns\TextColumn::make('title'),
                Tables\Columns\TextColumn::make('units'),
                Tables\Columns\TextColumn::make('lecture'),
                Tables\Columns\TextColumn::make('laboratory'),

                Tables\Columns\TextColumn::make('academic_year'),
                Tables\Columns\TextColumn::make('semester'),
                Tables\Columns\TextColumn::make('course.code'),
            ])
            ->filters([
                SelectFilter::make('academic_year')
                    ->options(
                        Subject::query()
                            ->distinct()
                            ->pluck('academic_year', 'academic_year')
                            ->toArray()
                    )
                    ->label('Academic Year'),
                SelectFilter::make('semester')
                    ->options(
                        Subject::query()
                            ->distinct()
                            ->pluck('semester', 'semester')
                            ->toArray()
                    )
                    ->label('Semester'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                // Tables\Actions\ViewAction::make(),
                // Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ]);
    }

    public function infoList(InfoList $infoList): InfoList
    {
        return $infoList
            ->schema([
                Tabs::make('Details')
                    ->tabs([
                        Tabs\Tab::make('General Information')
                            ->schema([
                                Section::make('Basic Details')
                                    ->schema([
                                        TextEntry::make('code')->label('Code'),
                                        TextEntry::make('title')->label('Title'),
                                        TextEntry::make('units')->label('Units'),
                                        TextEntry::make('lecture')->label('Lecture Hours'),
                                        TextEntry::make('laboratory')->label('Laboratory Hours'),
                                    ]),
                                Section::make('Academic Details')
                                    ->schema([
                                        TextEntry::make('academic_year')->label('Academic Year'),
                                        TextEntry::make('semester')->label('Semester'),
                                        TextEntry::make('course.code')->label('Course Code'),
                                    ]),
                            ]),
                        Tabs\Tab::make('Pre-requisites')
                            ->schema([
                                TextEntry::make('pre_riquisite')->label('Pre-requisites'),
                            ]),
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ]);
    }
}
