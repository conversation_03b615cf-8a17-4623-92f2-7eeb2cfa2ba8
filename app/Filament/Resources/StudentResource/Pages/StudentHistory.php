<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentResource\Pages;

use App\Filament\Resources\StudentResource;
use App\Models\StudentTransaction;
use App\Models\StudentTuition;
use App\Models\SubjectEnrollment;
use Exception;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Collection;

final class StudentHistory extends Page
{
    use InteractsWithRecord;

    protected static string $resource = StudentResource::class;

    protected static string $view = 'filament.admin.resources.students-resource.pages.student-history';

    protected static ?string $title = 'Student History';

    public function mount(int|string $record): void
    {
        $this->record = $this->resolveRecord($record);
    }

    protected function getViewData(): array
    {
        return [
            'record' => $this->record,
            'academicHistory' => $this->getAcademicHistory(),
            'financialHistory' => $this->getFinancialHistory(),
            'recentTransactions' => $this->getRecentTransactions(),
            'totalTuition' => $this->getTotalTuition(),
            'totalPaid' => $this->getTotalPaid(),
            'totalBalance' => $this->getTotalBalance(),
        ];
    }

    private function getAcademicHistory(): Collection
    {
        try {
            // Load all subject enrollments for the student, grouped by school year and semester
            $subjectEnrollments = SubjectEnrollment::where('student_id', $this->record->id)
                ->with(['subject', 'class'])
                ->orderBy('school_year', 'desc')
                ->orderBy('semester', 'asc')
                ->get();

            // Group by school year and semester
            return $subjectEnrollments->groupBy('school_year')
                ->map(fn ($yearGroup) => $yearGroup->groupBy(function ($item): string {
                    $semesterNames = [
                        1 => '1st Semester',
                        2 => '2nd Semester',
                        3 => 'Summer',
                    ];

                    return $semesterNames[$item->semester] ?? "Semester {$item->semester}";
                }));
        } catch (Exception) {
            return collect();
        }
    }

    private function getFinancialHistory(): Collection
    {
        try {
            // Load all tuition records for the student, grouped by school year
            $tuitionRecords = StudentTuition::where('student_id', $this->record->id)
                ->orderBy('school_year', 'desc')
                ->orderBy('semester', 'asc')
                ->get();

            return $tuitionRecords->groupBy('school_year');
        } catch (Exception) {
            return collect();
        }
    }

    private function getRecentTransactions(): Collection
    {
        try {
            // Load recent payment transactions for the student
            return StudentTransaction::where('student_id', $this->record->id)
                ->with(['transaction'])
                ->whereHas('transaction', function ($query): void {
                    $query->where('status', 'completed');
                })
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();
        } catch (Exception) {
            return collect();
        }
    }

    private function getTotalTuition(): float
    {
        try {
            return StudentTuition::where('student_id', $this->record->id)
                ->sum('overall_tuition') ?? 0;
        } catch (Exception) {
            return 0;
        }
    }

    private function getTotalBalance(): float
    {
        try {
            return StudentTuition::where('student_id', $this->record->id)
                ->sum('total_balance') ?? 0;
        } catch (Exception) {
            return 0;
        }
    }

    private function getTotalPaid(): float
    {
        return $this->getTotalTuition() - $this->getTotalBalance();
    }
}
