<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\GradeApprovalResource\Pages;
use App\Models\ClassEnrollment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

final class GradeApprovalResource extends Resource
{
    protected static ?string $model = ClassEnrollment::class;


    protected static ?string $navigationGroup = 'Academic Management';

    protected static ?string $navigationLabel = 'Grade Approvals';

    protected static ?string $pluralModelLabel = 'Grade Approvals';

    public static function canAccess(): bool
    {
        if (Auth::user()->hasRole('registrar')) {
            return true;
        }

        return (bool) Auth::user()->hasRole('super_admin');
    }

    public static function getNavigationBadge(): ?string
    {
        return (string) self::getModel()::where('is_grades_finalized', true)
            ->where('is_grades_verified', false)
            ->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return self::getNavigationBadge() > 0 ? 'warning' : null;
    }

    public static function form(Form $form): Form
    {
        return $form->schema([

            Forms\Components\Section::make('Student Information')
                ->schema([
                    Forms\Components\TextInput::make('student.full_name')
                        ->label('Student Name'),

                    Forms\Components\TextInput::make('student_id')
                        ->label('Student ID'),
                ])
                ->columns(2),

            Forms\Components\Section::make('Grade Information')->schema([
                Forms\Components\Grid::make(3)->schema([
                    Forms\Components\TextInput::make('prelim_grade')
                        ->numeric()
                        ->suffix('%')
                        ->minValue(0)
                        ->maxValue(100)
                        ->step(0.01)
                        ->live(),

                    Forms\Components\TextInput::make('midterm_grade')
                        ->numeric()
                        ->suffix('%')
                        ->minValue(0)
                        ->maxValue(100)
                        ->step(0.01)
                        ->live(),

                    Forms\Components\TextInput::make('finals_grade')
                        ->numeric()
                        ->suffix('%')
                        ->minValue(0)
                        ->maxValue(100)
                        ->step(0.01)
                        ->live(),
                ]),

                Forms\Components\Grid::make(2)->schema([
                    Forms\Components\TextInput::make('total_average')
                        ->numeric()
                        ->suffix('%')
                        ->minValue(0)
                        ->maxValue(100)
                        ->step(0.01)
                        ->disabled(),

                    Forms\Components\TextInput::make('remarks')
                        ->datalist([
                            'PASSED',
                            'FAILED',
                            'INCOMPLETE',
                            'DROPPED',
                            'WITHDRAWN',
                        ]),
                ]),

                Forms\Components\Actions::make([
                    Forms\Components\Actions\Action::make('recalculate')
                        ->label('Recalculate Average')
                        ->icon('heroicon-o-calculator')
                        ->color('warning')
                        ->action(function ($livewire, $data): void {
                            if (
                                isset($data['prelim_grade']) &&
                                isset($data['midterm_grade']) &&
                                isset($data['finals_grade']) &&
                                is_numeric($data['prelim_grade']) &&
                                is_numeric($data['midterm_grade']) &&
                                is_numeric($data['finals_grade'])
                            ) {
                                $average = round(
                                    ($data['prelim_grade'] + $data['midterm_grade'] + $data['finals_grade']) / 3,
                                    2
                                );
                                $livewire->form->fill([
                                    'total_average' => $average,
                                    'remarks' => $average >= 75 ? 'PASSED' : 'FAILED',
                                ]);
                            }
                        }),
                ])->alignment(\Filament\Support\Enums\Alignment::Center),
            ]),

            Forms\Components\Section::make('Verification')
                ->schema([
                    Forms\Components\DateTimePicker::make('submitted_at')
                        ->label('Submitted by Faculty')
                        ->disabled()
                        ->formatStateUsing(
                            fn (Model $record) => $record->is_grades_finalized
                                ? $record->updated_at
                                : null
                        ),

                    Forms\Components\Toggle::make('is_grades_verified')
                        ->label('Approve Grade Submission')
                        ->helperText(
                            'Once approved, the faculty cannot modify these grades.'
                        )
                        ->default(false)
                        ->required(),

                    Forms\Components\Toggle::make('is_grades_finalized')
                        ->label('Finalize Grades')
                        ->helperText(
                            'Mark these grades as finalized by faculty.'
                        )
                        ->default(false),

                    Forms\Components\Textarea::make('verification_notes')
                        ->label('Verification Notes')
                        ->placeholder('Add any notes about this grade verification')
                        ->columnSpanFull(),
                ])
                ->columns(3),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('class.subject.code')
                    ->label('Subject')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('class.section')
                    ->label('Section')
                    ->badge(),

                Tables\Columns\TextColumn::make('class.faculty.first_name')
                    ->label('Faculty')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('student.full_name')
                    ->label('Student')
                    ->description(fn ($record) => $record->student->student_id)
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_average')
                    ->numeric()
                    ->sortable()
                    ->color(fn ($state): string => $state >= 75 ? 'success' : 'danger')
                    ->weight('bold'),

                Tables\Columns\IconColumn::make('status')
                    ->label('Status')
                    ->icons([
                        'heroicon-o-check-circle' => fn (
                            $record
                        ) => $record->is_grades_verified,
                        'heroicon-o-clock' => fn (
                            $record
                        ): bool => $record->is_grades_finalized &&
                            ! $record->is_grades_verified,
                        'heroicon-o-pencil' => fn (
                            $record
                        ): bool => ! $record->is_grades_finalized,
                    ])
                    ->colors([
                        'success' => fn ($record) => $record->is_grades_verified,
                        'warning' => fn (
                            $record
                        ): bool => $record->is_grades_finalized &&
                            ! $record->is_grades_verified,
                        'gray' => fn ($record): bool => ! $record->is_grades_finalized,
                    ]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('faculty')->relationship(
                    'class.faculty',
                    'last_name'
                ),

                Tables\Filters\SelectFilter::make('verification_status')
                    ->options([
                        'pending' => 'Pending Approval',
                        'approved' => 'Approved',
                        'draft' => 'Draft (Not Finalized)',
                    ])
                    ->query(fn (Builder $query, array $data) => $query
                        ->when($data['value'] === 'pending', fn ($q) => $q
                            ->where('is_grades_finalized', true)
                            ->where('is_grades_verified', false))
                        ->when($data['value'] === 'approved', fn ($q) => $q->where('is_grades_verified', true))
                        ->when($data['value'] === 'draft', fn ($q) => $q->where('is_grades_finalized', false))),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(fn (Model $record): bool => Auth::user()->hasRole('registrar') || Auth::user()->hasRole('super_admin')),

                Tables\Actions\Action::make('view_details')
                    ->icon('heroicon-o-eye')
                    ->url(
                        fn (Model $record): string => self::getUrl('view', [
                            'record' => $record,
                        ])
                    ),

                Tables\Actions\Action::make('approve')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(
                        fn (Model $record): bool => $record->is_grades_finalized &&
                            ! $record->is_grades_verified
                    )
                    ->requiresConfirmation()
                    ->action(function (Model $record): void {
                        $record->update([
                            'is_grades_verified' => true,
                            'verified_by' => Auth::id(),
                            'verified_at' => now(),
                        ]);
                    }),

                Tables\Actions\Action::make('revoke')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn (Model $record): bool => $record->is_grades_verified && Auth::user()->hasRole('registrar'))
                    ->requiresConfirmation()
                    ->action(function (Model $record): void {
                        $record->update([
                            'is_grades_verified' => false,
                            'verified_by' => null,
                            'verified_at' => null,
                        ]);
                    }),

                Tables\Actions\Action::make('recalculate')
                    ->icon('heroicon-o-calculator')
                    ->color('warning')
                    ->visible(fn (Model $record): bool => Auth::user()->hasRole('registrar') || Auth::user()->hasRole('super_admin'))
                    ->action(function (Model $record): void {
                        if (
                            is_numeric($record->prelim_grade) &&
                            is_numeric($record->midterm_grade) &&
                            is_numeric($record->finals_grade)
                        ) {
                            $average = round(
                                ($record->prelim_grade + $record->midterm_grade + $record->finals_grade) / 3,
                                2
                            );
                            $record->update([
                                'total_average' => $average,
                                'remarks' => $average >= 75 ? 'PASSED' : 'FAILED',
                            ]);
                        }
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Recalculate Average')
                    ->modalDescription('This will recalculate the total average based on the current prelim, midterm, and finals grades.')
                    ->modalSubmitActionLabel('Yes, recalculate'),

                Tables\Actions\DeleteAction::make()
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->visible(fn (Model $record): bool => Auth::user()->hasRole('registrar') || Auth::user()->hasRole('super_admin'))
                    ->requiresConfirmation()
                    ->modalHeading('Delete Grade Approval')
                    ->modalDescription('Are you sure you want to delete this grade approval? This action cannot be undone.')
                    ->modalSubmitActionLabel('Yes, delete it'),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('bulk_approve')
                    ->label('Approve Selected')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->requiresConfirmation()
                    ->action(function (
                        \Illuminate\Database\Eloquent\Collection $records
                    ): void {
                        $records->each(function ($record): void {
                            if (
                                $record->is_grades_finalized &&
                                ! $record->is_grades_verified
                            ) {
                                $record->update([
                                    'is_grades_verified' => true,
                                    'verified_by' => Auth::id(),
                                    'verified_at' => now(),
                                ]);
                            }
                        });
                    }),

                Tables\Actions\DeleteBulkAction::make()
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Delete Selected Grade Approvals')
                    ->modalDescription('Are you sure you want to delete the selected grade approvals? This action cannot be undone.')
                    ->modalSubmitActionLabel('Yes, delete them')
                    ->visible(fn (): bool => Auth::user()->hasRole('registrar') || Auth::user()->hasRole('super_admin')),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGradeApprovals::route('/'),
            'edit' => Pages\EditGradeApproval::route('/{record}/edit'),
            'view' => Pages\ViewGradeApproval::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereHas('class', function ($query): void {
                $query->whereNotNull('faculty_id');
            })
            ->where(function ($query): void {
                $query
                    ->where('is_grades_finalized', true)
                    ->orWhere('is_grades_verified', true);
            });
    }
}
