<?php

declare(strict_types=1);

namespace App\Filament\Resources\FacultyResource\RelationManagers;

use App\Models\Classes;
use App\Services\GeneralSettingsService;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

final class ClassesRelationManager extends RelationManager
{
    protected static string $relationship = 'classes';

    protected static ?string $title = 'Assigned Classes';

    protected static ?string $recordTitleAttribute = 'subject_code';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('subject_code')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('subject_code')
            ->columns([
                TextColumn::make('subject_code')
                    ->label('Subject Code')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('subject_title')
                    ->label('Subject Title')
                    ->searchable()
                    ->getStateUsing(fn ($record) => $record->subject_title),
                TextColumn::make('section')
                    ->label('Section')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('classification')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'college' => 'success',
                        'shs' => 'info',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn ($state): string => $state === 'shs' ? 'SHS' : 'College'),
                TextColumn::make('display_info')
                    ->label('Course/Track Info')
                    ->getStateUsing(fn ($record) => $record->display_info),
                TextColumn::make('school_year')
                    ->label('School Year')
                    ->sortable(),
                TextColumn::make('semester')
                    ->label('Semester')
                    ->sortable()
                    ->formatStateUsing(fn ($state) => match ($state) {
                        1 => '1st Semester',
                        2 => '2nd Semester',
                        default => $state
                    }),
                TextColumn::make('student_count')
                    ->label('Students')
                    ->getStateUsing(fn ($record) => $record->student_count)
                    ->badge()
                    ->color('primary'),
                TextColumn::make('maximum_slots')
                    ->label('Max Slots')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('classification')
                    ->label('Type')
                    ->options([
                        'college' => 'College',
                        'shs' => 'SHS',
                    ]),
                Tables\Filters\SelectFilter::make('semester')
                    ->label('Semester')
                    ->options([
                        1 => '1st Semester',
                        2 => '2nd Semester',
                    ]),
            ])
            ->headerActions([
                Action::make('assignNewClasses')
                    ->label('Assign New Classes')
                    ->icon('heroicon-o-plus')
                    ->color('success')
                    ->form([
                        Select::make('class_ids')
                            ->label('Select Classes to Assign')
                            ->multiple()
                            ->searchable()
                            ->preload()
                            ->options(function () {
                                $settingsService = app(GeneralSettingsService::class);

                                return Classes::currentAcademicPeriod()
                                    ->whereNull('faculty_id')
                                    ->get()
                                    ->mapWithKeys(function ($class) {
                                        $type = $class->isShs() ? 'SHS' : 'College';
                                        $info = $class->isShs() ? $class->formatted_track_strand : $class->formatted_course_codes;
                                        $label = "{$class->subject_title} - {$class->section} ({$type})";
                                        if ($info && $info !== 'N/A') {
                                            $label .= " - {$info}";
                                        }

                                        return [$class->id => $label];
                                    });
                            })
                            ->hint('Only showing unassigned classes for current academic period'),
                    ])
                    ->action(function (array $data): void {
                        if (! empty($data['class_ids'])) {
                            Classes::whereIn('id', $data['class_ids'])
                                ->update(['faculty_id' => (string) $this->getOwnerRecord()->id]);

                            $count = count($data['class_ids']);
                            Notification::make()
                                ->title('Classes Assigned Successfully')
                                ->body("Assigned {$count} class(es) to {$this->getOwnerRecord()->full_name}")
                                ->success()
                                ->send();
                        }
                    }),
            ])
            ->actions([
                Action::make('unassign')
                    ->label('Unassign')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Unassign Faculty from Class')
                    ->modalDescription('Are you sure you want to unassign this faculty member from this class?')
                    ->action(function ($record): void {
                        $record->update(['faculty_id' => null]);

                        Notification::make()
                            ->title('Faculty Unassigned')
                            ->body("Faculty has been unassigned from {$record->subject_title} - {$record->section}")
                            ->success()
                            ->send();
                    }),
                Tables\Actions\ViewAction::make()
                    ->url(fn ($record) => route('filament.admin.resources.classes.view', $record)),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Action::make('unassignSelected')
                        ->label('Unassign Selected')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->modalHeading('Unassign Faculty from Selected Classes')
                        ->modalDescription('Are you sure you want to unassign this faculty member from the selected classes?')
                        ->action(function ($records): void {
                            $count = $records->count();
                            $records->each(fn ($record) => $record->update(['faculty_id' => null]));

                            Notification::make()
                                ->title('Faculty Unassigned')
                                ->body("Faculty has been unassigned from {$count} class(es)")
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('school_year', 'desc')
            ->modifyQueryUsing(function (Builder $query) {
                // Show current academic period classes by default, but allow viewing all
                $settingsService = app(GeneralSettingsService::class);
                $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
                $currentSemester = $settingsService->getCurrentSemester();

                return $query->whereRaw('faculty_id::text = ?', [$this->getOwnerRecord()->id])
                    ->orderByRaw('
                        CASE
                            WHEN school_year = ? AND semester = ? THEN 1
                            ELSE 2
                        END, school_year DESC, semester DESC
                    ', [$currentSchoolYear, $currentSemester]);
            });
    }
}
