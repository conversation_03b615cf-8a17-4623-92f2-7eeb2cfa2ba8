<?php

declare(strict_types=1);

namespace App\Filament\Resources\GradeApprovalResource\Pages;

use App\Filament\Resources\GradeApprovalResource;
use Filament\Actions;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Enums\FontWeight;
use Illuminate\Support\Facades\Auth;

final class ViewGradeApproval extends ViewRecord
{
    protected static string $resource = GradeApprovalResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist->schema([
            Section::make('Class Information')->schema([
                Grid::make(3)->schema([
                    TextEntry::make('class.subject.code')
                        ->label('Subject Code')
                        ->weight(FontWeight::Bold),

                    TextEntry::make('class.subject.title')->label(
                        'Subject Title'
                    ),

                    TextEntry::make('class.section')
                        ->label('Section')
                        ->badge()
                        ->color('primary'),
                ]),

                TextEntry::make('class.faculty.name')
                    ->label('Faculty')
                    ->icon('heroicon-o-academic-cap'),
            ]),

            Section::make('Student Information')->schema([
                Grid::make(3)->schema([
                    TextEntry::make('student.full_name')
                        ->label('Student Name')
                        ->weight(FontWeight::Bold),

                    TextEntry::make('student.id')
                        ->label('Student ID')
                        ->badge(),

                    TextEntry::make('student.formatted_academic_year')
                        ->label('Year Level')
                        ->badge()
                        ->color('success'),
                ]),

                Grid::make(2)->schema([
                    TextEntry::make('student.course.code')
                        ->label('Course')
                        ->icon('heroicon-o-academic-cap'),

                    TextEntry::make('student.email')
                        ->label('Email')
                        ->icon('heroicon-o-envelope'),
                ]),
            ]),

            Section::make('Grade Information')->schema([
                Grid::make(3)->schema([
                    TextEntry::make('prelim_grade')
                        ->label('Prelim')
                        ->numeric(decimalPlaces: 2)
                        ->badge()
                        ->color(
                            fn ($state): string => $state >= 75 ? 'success' : 'danger'
                        )
                        ->suffix('%'),

                    TextEntry::make('midterm_grade')
                        ->label('Midterm')
                        ->numeric(decimalPlaces: 2)
                        ->badge()
                        ->color(
                            fn ($state): string => $state >= 75 ? 'success' : 'danger'
                        )
                        ->suffix('%'),

                    TextEntry::make('finals_grade')
                        ->label('Finals')
                        ->numeric(decimalPlaces: 2)
                        ->badge()
                        ->color(
                            fn ($state): string => $state >= 75 ? 'success' : 'danger'
                        )
                        ->suffix('%'),
                ]),

                Grid::make(2)->schema([
                    TextEntry::make('total_average')
                        ->label('Total Average')
                        ->numeric(decimalPlaces: 2)
                        ->suffix('%')
                        ->weight(FontWeight::Bold)
                        ->color(
                            fn ($record): string => $record->total_average >= 75
                                ? 'success'
                                : 'danger'
                        ),

                    TextEntry::make('remarks')
                        ->label('Remarks')
                        ->badge()
                        ->color(
                            fn ($state): string => $state === 'PASSED'
                                ? 'success'
                                : 'danger'
                        ),
                ]),
            ]),

            Section::make('Verification Status')->schema([
                Grid::make(2)->schema([
                    TextEntry::make('verification_status')
                        ->label('Status')
                        ->badge()
                        ->formatStateUsing(
                            fn ($record): string => $record->is_grades_verified
                                ? 'Approved'
                                : ($record->is_grades_finalized
                                    ? 'Pending Approval'
                                    : 'Draft')
                        )
                        ->color(
                            fn ($record): string => $record->is_grades_verified
                                ? 'success'
                                : ($record->is_grades_finalized
                                    ? 'warning'
                                    : 'gray')
                        ),

                    TextEntry::make('verified_at')
                        ->label('Verification Date')
                        ->date('F j, Y, g:i a')
                        ->visible(fn ($record): bool => $record->verified_at !== null),
                ]),

                TextEntry::make('verification_notes')
                    ->label('Verification Notes')
                    ->columnSpanFull()
                    ->visible(fn ($record): bool => $record->verification_notes !== null),
            ]),
        ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn (): bool => Auth::user()->hasRole('registrar') || Auth::user()->hasRole('super_admin')),

            Actions\DeleteAction::make()
                ->visible(fn (): bool => Auth::user()->hasRole('registrar') || Auth::user()->hasRole('super_admin')),

            Actions\Action::make('approve')
                ->label('Approve Grades')
                ->icon('heroicon-o-check')
                ->color('success')
                ->visible(
                    fn (): bool => $this->record->is_grades_finalized &&
                        ! $this->record->is_grades_verified
                )
                ->requiresConfirmation()
                ->action(function (): void {
                    $this->record->update([
                        'is_grades_verified' => true,
                        'verified_by' => Auth::id(),
                        'verified_at' => now(),
                    ]);

                }),

            Actions\Action::make('revoke')
                ->label('Revoke Approval')
                ->icon('heroicon-o-x-mark')
                ->color('danger')
                ->visible(fn () => $this->record->is_grades_verified)
                ->requiresConfirmation()
                ->action(function (): void {
                    $this->record->update([
                        'is_grades_verified' => false,
                        'verified_by' => null,
                        'verified_at' => null,
                    ]);

                }),
        ];
    }
}
