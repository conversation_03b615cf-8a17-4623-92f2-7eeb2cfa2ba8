<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\AccountResource\Pages;
use App\Models\Account;
use App\Models\Student;
use App\Models\Faculty;
use App\Models\ShsStudent;

use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;

use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;

class AccountResource extends Resource
{
    protected static ?string $model = Account::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationLabel = 'Account Management';

    protected static ?string $modelLabel = 'Account';

    protected static ?string $pluralModelLabel = 'Accounts';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Account Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Full Name')
                                    ->required()
                                    ->maxLength(255),

                                TextInput::make('username')
                                    ->label('Username')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(255)
                                    ->alphaDash(),

                                TextInput::make('email')
                                    ->label('Email Address')
                                    ->email()
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(255),

                                TextInput::make('phone')
                                    ->label('Phone Number')
                                    ->tel()
                                    ->maxLength(20),

                                Select::make('role')
                                    ->label('Role')
                                    ->options([
                                        'admin' => 'Administrator',
                                        'faculty' => 'Faculty',
                                        'student' => 'Student',
                                        'guest' => 'Guest',
                                    ])
                                    ->required()
                                    ->default('guest'),

                                TextInput::make('password')
                                    ->label('Password')
                                    ->password()
                                    ->dehydrateStateUsing(fn ($state) => filled($state) ? bcrypt($state) : null)
                                    ->dehydrated(fn ($state) => filled($state))
                                    ->required(fn (string $context): bool => $context === 'create')
                                    ->minLength(8),
                            ]),
                    ]),

                Section::make('Account Settings')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Toggle::make('is_active')
                                    ->label('Account Active')
                                    ->default(true)
                                    ->helperText('Inactive accounts cannot log in'),

                                Toggle::make('is_notification_active')
                                    ->label('Notifications Enabled')
                                    ->default(true)
                                    ->helperText('Enable/disable email notifications'),
                            ]),
                    ]),

                Section::make('Person Linking')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Select::make('person_type')
                                    ->label('Person Type')
                                    ->options([
                                        Student::class => 'College Student',
                                        Faculty::class => 'Faculty Member',
                                        ShsStudent::class => 'SHS Student',
                                    ])
                                    ->nullable()
                                    ->reactive()
                                    ->afterStateUpdated(fn (callable $set) => $set('person_id', null)),

                                Select::make('person_id')
                                    ->label('Linked Person')
                                    ->options(function (callable $get) {
                                        $personType = $get('person_type');
                                        
                                        if ($personType === Student::class) {
                                            return Student::query()
                                                ->get()
                                                ->mapWithKeys(fn ($student) => [
                                                    $student->id => "{$student->first_name} {$student->last_name} (ID: {$student->id})"
                                                ]);
                                        }
                                        
                                        if ($personType === Faculty::class) {
                                            return Faculty::whereNotIn('email', function ($query) {
                                                    $query->select('email')
                                                        ->from('accounts')
                                                        ->where('person_type', Faculty::class)
                                                        ->whereNotNull('email');
                                                })
                                                ->get()
                                                ->mapWithKeys(fn ($faculty) => [
                                                    $faculty->id => "{$faculty->getFullNameAttribute()} ({$faculty->email})"
                                                ]);
                                        }
                                        
                                        if ($personType === ShsStudent::class) {
                                            return ShsStudent::query()
                                                ->get()
                                                ->mapWithKeys(fn ($student) => [
                                                    $student->student_lrn => "{$student->fullname} (LRN: {$student->student_lrn})"
                                                ]);
                                        }
                                        
                                        return [];
                                    })
                                    ->searchable()
                                    ->nullable()
                                    ->visible(fn (callable $get) => filled($get('person_type'))),
                            ]),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('name')
                    ->label('Name')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Medium),

                TextColumn::make('username')
                    ->label('Username')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                TextColumn::make('role')
                    ->label('Role')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'admin' => 'danger',
                        'faculty' => 'warning',
                        'student' => 'success',
                        'guest' => 'secondary',
                        default => 'secondary',
                    })
                    ->sortable(),

                TextColumn::make('linked_person')
                    ->label('Linked To')
                    ->getStateUsing(function (Account $record) {
                        if (!$record->person_id || !$record->person_type) {
                            return 'Not Linked';
                        }

                        $person = $record->getPerson();
                        if (!$person) {
                            return 'Invalid Link';
                        }

                        $personType = match ($record->person_type) {
                            Student::class => 'Student',
                            Faculty::class => 'Faculty',
                            ShsStudent::class => 'SHS Student',
                            default => 'Unknown'
                        };

                        return $personType . ': ' . self::getPersonDisplayName($person);
                    })
                    ->searchable()
                    ->sortable(),

                IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->sortable(),

                TextColumn::make('last_login')
                    ->label('Last Login')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Never'),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('role')
                    ->options([
                        'admin' => 'Administrator',
                        'faculty' => 'Faculty',
                        'student' => 'Student',
                        'guest' => 'Guest',
                    ]),

                TernaryFilter::make('is_active')
                    ->label('Account Status')
                    ->placeholder('All accounts')
                    ->trueLabel('Active accounts')
                    ->falseLabel('Inactive accounts'),

                SelectFilter::make('person_type')
                    ->label('Linked To')
                    ->options([
                        Student::class => 'College Students',
                        Faculty::class => 'Faculty Members',
                        ShsStudent::class => 'SHS Students',
                    ])
                    ->placeholder('All account types'),

                TernaryFilter::make('has_person')
                    ->label('Linking Status')
                    ->placeholder('All accounts')
                    ->trueLabel('Linked accounts')
                    ->falseLabel('Unlinked accounts')
                    ->queries(
                        true: fn (Builder $query) => $query->whereNotNull('person_id')->whereNotNull('person_type'),
                        false: fn (Builder $query) => $query->whereNull('person_id')->orWhereNull('person_type'),
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAccounts::route('/'),
            'create' => Pages\CreateAccount::route('/create'),
            'view' => Pages\ViewAccount::route('/{record}'),
            'edit' => Pages\EditAccount::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    /**
     * Get display name for a person
     */
    private static function getPersonDisplayName($person): string
    {
        if ($person instanceof Student) {
            return "{$person->first_name} {$person->last_name}";
        }
        
        if ($person instanceof Faculty) {
            return $person->getFullNameAttribute();
        }
        
        if ($person instanceof ShsStudent) {
            return $person->fullname ?? 'Unknown';
        }
        
        return 'Unknown';
    }
}
