<?php

declare(strict_types=1);

namespace App\Filament\Resources\AccountResource\Pages;

use App\Filament\Resources\AccountResource;
use App\Models\Account;
use App\Models\Student;
use App\Models\Faculty;
use App\Models\ShsStudent;
use App\Actions\Account\LinkAccountToPersonAction;
use App\Actions\Account\UnlinkAccountFromPersonAction;
use App\Actions\Account\ToggleAccountStatusAction;
use App\Actions\Account\ResetAccountPasswordAction;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;

class ListAccounts extends ListRecords
{
    protected static string $resource = AccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            
            Actions\Action::make('bulk_link_accounts')
                ->label('Bulk Link Accounts')
                ->icon('heroicon-o-link')
                ->color('info')
                ->url(fn () => route('filament.admin.pages.bulk-account-linking')),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Action::make('link_person')
                ->label('Link Person')
                ->icon('heroicon-o-link')
                ->color('success')
                ->visible(fn (Account $record) => !$record->person_id || !$record->person_type)
                ->form([
                    Select::make('person_type')
                        ->label('Person Type')
                        ->options([
                            Student::class => 'College Student',
                            Faculty::class => 'Faculty Member',
                            ShsStudent::class => 'SHS Student',
                        ])
                        ->required()
                        ->reactive()
                        ->afterStateUpdated(fn (callable $set) => $set('person_id', null)),

                    Select::make('person_id')
                        ->label('Select Person')
                        ->options(function (callable $get) {
                            $personType = $get('person_type');
                            
                            if ($personType === Student::class) {
                                return Student::whereDoesntHave('account')
                                    ->get()
                                    ->mapWithKeys(fn ($student) => [
                                        $student->id => "{$student->first_name} {$student->last_name} (ID: {$student->id})"
                                    ]);
                            }
                            
                            if ($personType === Faculty::class) {
                                return Faculty::whereNotIn('email', function ($query) {
                                        $query->select('email')
                                            ->from('accounts')
                                            ->where('person_type', Faculty::class)
                                            ->whereNotNull('email');
                                    })
                                    ->get()
                                    ->mapWithKeys(fn ($faculty) => [
                                        $faculty->id => "{$faculty->getFullNameAttribute()} ({$faculty->email})"
                                    ]);
                            }
                            
                            if ($personType === ShsStudent::class) {
                                return ShsStudent::whereDoesntHave('account')
                                    ->get()
                                    ->mapWithKeys(fn ($student) => [
                                        $student->student_lrn => "{$student->fullname} (LRN: {$student->student_lrn})"
                                    ]);
                            }
                            
                            return [];
                        })
                        ->searchable()
                        ->required()
                        ->visible(fn (callable $get) => filled($get('person_type'))),
                ])
                ->action(function (Account $record, array $data, LinkAccountToPersonAction $action) {
                    try {
                        $personType = $data['person_type'];
                        $personId = $data['person_id'];
                        
                        $person = $personType::find($personId);
                        
                        if (!$person) {
                            throw new \Exception('Person not found');
                        }
                        
                        $action->execute($record, $person);
                        
                        Notification::make()
                            ->title('Account Linked Successfully')
                            ->body("Account has been linked to {$this->getPersonDisplayName($person)}")
                            ->success()
                            ->send();
                            
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Linking Failed')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Action::make('unlink_person')
                ->label('Unlink Person')
                ->icon('heroicon-o-x-mark')
                ->color('warning')
                ->visible(fn (Account $record) => $record->person_id && $record->person_type)
                ->requiresConfirmation()
                ->modalHeading('Unlink Account from Person')
                ->modalDescription('Are you sure you want to unlink this account? The account will be set to guest role.')
                ->action(function (Account $record, UnlinkAccountFromPersonAction $action) {
                    try {
                        $action->execute($record);
                        
                        Notification::make()
                            ->title('Account Unlinked Successfully')
                            ->body('Account has been unlinked from the person')
                            ->success()
                            ->send();
                            
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Unlinking Failed')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Action::make('toggle_status')
                ->label(fn (Account $record) => $record->is_active ? 'Deactivate' : 'Activate')
                ->icon(fn (Account $record) => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                ->color(fn (Account $record) => $record->is_active ? 'danger' : 'success')
                ->requiresConfirmation()
                ->modalHeading(fn (Account $record) => $record->is_active ? 'Deactivate Account' : 'Activate Account')
                ->modalDescription(fn (Account $record) => $record->is_active 
                    ? 'Are you sure you want to deactivate this account? The user will not be able to log in.'
                    : 'Are you sure you want to activate this account? The user will be able to log in.')
                ->action(function (Account $record, ToggleAccountStatusAction $action) {
                    try {
                        $action->execute($record);
                        
                        $status = $record->fresh()->is_active ? 'activated' : 'deactivated';
                        
                        Notification::make()
                            ->title('Account Status Updated')
                            ->body("Account has been {$status} successfully")
                            ->success()
                            ->send();
                            
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Status Update Failed')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Action::make('reset_password')
                ->label('Reset Password')
                ->icon('heroicon-o-key')
                ->color('warning')
                ->form([
                    TextInput::make('new_password')
                        ->label('New Password')
                        ->password()
                        ->required()
                        ->minLength(8)
                        ->helperText('Leave empty to generate a random password'),
                ])
                ->action(function (Account $record, array $data, ResetAccountPasswordAction $action) {
                    try {
                        if (empty($data['new_password'])) {
                            $result = $action->executeWithRandomPassword($record);
                            $newPassword = $result['password'];
                        } else {
                            $action->execute($record, $data['new_password']);
                            $newPassword = $data['new_password'];
                        }
                        
                        Notification::make()
                            ->title('Password Reset Successfully')
                            ->body("New password: {$newPassword}")
                            ->success()
                            ->persistent()
                            ->send();
                            
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Password Reset Failed')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }

    /**
     * Get display name for a person
     */
    private function getPersonDisplayName(Model $person): string
    {
        if ($person instanceof Student) {
            return "{$person->first_name} {$person->last_name}";
        }
        
        if ($person instanceof Faculty) {
            return $person->getFullNameAttribute();
        }
        
        if ($person instanceof ShsStudent) {
            return $person->fullname ?? 'Unknown';
        }
        
        return 'Unknown';
    }
}
