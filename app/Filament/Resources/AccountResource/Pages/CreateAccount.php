<?php

declare(strict_types=1);

namespace App\Filament\Resources\AccountResource\Pages;

use App\Filament\Resources\AccountResource;
use App\Actions\Account\CreateAccountAction;
use App\Models\Student;
use App\Models\Faculty;
use App\Models\ShsStudent;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;

class CreateAccount extends CreateRecord
{
    protected static string $resource = AccountResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        try {
            $createAccountAction = app(CreateAccountAction::class);
            
            // Extract person data if provided
            $person = null;
            if (!empty($data['person_type']) && !empty($data['person_id'])) {
                $personType = $data['person_type'];
                $personId = $data['person_id'];
                
                $person = $personType::find($personId);
                
                if (!$person) {
                    throw new \Exception('Selected person not found');
                }
            }
            
            // Remove person data from account data
            unset($data['person_type'], $data['person_id']);
            
            // Create account with or without person linking
            $account = $createAccountAction->execute($data, $person);
            
            if ($person) {
                Notification::make()
                    ->title('Account Created and Linked Successfully')
                    ->body("Account has been created and linked to {$this->getPersonDisplayName($person)}")
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Account Created Successfully')
                    ->body('Account has been created without linking to any person')
                    ->success()
                    ->send();
            }
            
            return $account;
            
        } catch (\Exception $e) {
            Notification::make()
                ->title('Account Creation Failed')
                ->body($e->getMessage())
                ->danger()
                ->send();
                
            throw $e;
        }
    }

    /**
     * Get display name for a person
     */
    private function getPersonDisplayName(Model $person): string
    {
        if ($person instanceof Student) {
            return "{$person->first_name} {$person->last_name}";
        }
        
        if ($person instanceof Faculty) {
            return $person->getFullNameAttribute();
        }
        
        if ($person instanceof ShsStudent) {
            return $person->fullname ?? 'Unknown';
        }
        
        return 'Unknown';
    }
}
