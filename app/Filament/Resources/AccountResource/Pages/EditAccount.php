<?php

declare(strict_types=1);

namespace App\Filament\Resources\AccountResource\Pages;

use App\Filament\Resources\AccountResource;
use App\Actions\Account\LinkAccountToPersonAction;
use App\Actions\Account\UnlinkAccountFromPersonAction;
use App\Models\Student;
use App\Models\Faculty;
use App\Models\ShsStudent;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;

class EditAccount extends EditRecord
{
    protected static string $resource = AccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        try {
            // Handle person linking/unlinking
            $currentPersonId = $record->person_id;
            $currentPersonType = $record->person_type;
            $newPersonId = $data['person_id'] ?? null;
            $newPersonType = $data['person_type'] ?? null;
            
            // Remove person data from account data for now
            unset($data['person_type'], $data['person_id']);
            
            // Update basic account information
            $record->update($data);
            
            // Handle person linking changes
            if ($this->shouldUnlinkPerson($currentPersonId, $currentPersonType, $newPersonId, $newPersonType)) {
                $unlinkAction = app(UnlinkAccountFromPersonAction::class);
                $record = $unlinkAction->execute($record);
                
                Notification::make()
                    ->title('Person Unlinked')
                    ->body('Account has been unlinked from the person')
                    ->warning()
                    ->send();
            }
            
            if ($this->shouldLinkPerson($newPersonId, $newPersonType)) {
                $person = $newPersonType::find($newPersonId);
                
                if ($person) {
                    $linkAction = app(LinkAccountToPersonAction::class);
                    $record = $linkAction->execute($record, $person);
                    
                    Notification::make()
                        ->title('Person Linked')
                        ->body("Account has been linked to {$this->getPersonDisplayName($person)}")
                        ->success()
                        ->send();
                } else {
                    Notification::make()
                        ->title('Linking Failed')
                        ->body('Selected person not found')
                        ->danger()
                        ->send();
                }
            }
            
            return $record->fresh();
            
        } catch (\Exception $e) {
            Notification::make()
                ->title('Update Failed')
                ->body($e->getMessage())
                ->danger()
                ->send();
                
            throw $e;
        }
    }

    /**
     * Check if person should be unlinked
     */
    private function shouldUnlinkPerson($currentPersonId, $currentPersonType, $newPersonId, $newPersonType): bool
    {
        // If currently linked but new data is empty, unlink
        if ($currentPersonId && $currentPersonType && (!$newPersonId || !$newPersonType)) {
            return true;
        }
        
        // If person changed, unlink first (will be relinked after)
        if ($currentPersonId && $currentPersonType && 
            ($currentPersonId != $newPersonId || $currentPersonType !== $newPersonType)) {
            return true;
        }
        
        return false;
    }

    /**
     * Check if person should be linked
     */
    private function shouldLinkPerson($newPersonId, $newPersonType): bool
    {
        return !empty($newPersonId) && !empty($newPersonType);
    }

    /**
     * Get display name for a person
     */
    private function getPersonDisplayName(Model $person): string
    {
        if ($person instanceof Student) {
            return "{$person->first_name} {$person->last_name}";
        }
        
        if ($person instanceof Faculty) {
            return $person->getFullNameAttribute();
        }
        
        if ($person instanceof ShsStudent) {
            return $person->fullname ?? 'Unknown';
        }
        
        return 'Unknown';
    }
}
