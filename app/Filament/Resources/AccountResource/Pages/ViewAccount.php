<?php

declare(strict_types=1);

namespace App\Filament\Resources\AccountResource\Pages;

use App\Filament\Resources\AccountResource;
use App\Models\Student;
use App\Models\Faculty;
use App\Models\ShsStudent;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;
use Illuminate\Database\Eloquent\Model;

class ViewAccount extends ViewRecord
{
    protected static string $resource = AccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Account Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('id')
                                    ->label('Account ID'),

                                TextEntry::make('name')
                                    ->label('Full Name'),

                                TextEntry::make('username')
                                    ->label('Username')
                                    ->copyable(),

                                TextEntry::make('email')
                                    ->label('Email Address')
                                    ->copyable(),

                                TextEntry::make('phone')
                                    ->label('Phone Number')
                                    ->placeholder('Not provided'),

                                TextEntry::make('role')
                                    ->label('Role')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'admin' => 'danger',
                                        'faculty' => 'warning',
                                        'student' => 'success',
                                        'guest' => 'secondary',
                                        default => 'secondary',
                                    }),
                            ]),
                    ]),

                Section::make('Account Status')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                IconEntry::make('is_active')
                                    ->label('Account Active')
                                    ->boolean(),

                                IconEntry::make('is_notification_active')
                                    ->label('Notifications Enabled')
                                    ->boolean(),

                                TextEntry::make('last_login')
                                    ->label('Last Login')
                                    ->dateTime()
                                    ->placeholder('Never logged in'),
                            ]),
                    ]),

                Section::make('Person Linking')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('person_type')
                                    ->label('Person Type')
                                    ->formatStateUsing(fn (?string $state): string => match ($state) {
                                        Student::class => 'College Student',
                                        Faculty::class => 'Faculty Member',
                                        ShsStudent::class => 'SHS Student',
                                        null => 'Not Linked',
                                        default => 'Unknown Type',
                                    })
                                    ->badge()
                                    ->color(fn (?string $state): string => match ($state) {
                                        Student::class => 'success',
                                        Faculty::class => 'warning',
                                        ShsStudent::class => 'info',
                                        null => 'secondary',
                                        default => 'danger',
                                    }),

                                TextEntry::make('linked_person_details')
                                    ->label('Linked Person')
                                    ->getStateUsing(function ($record) {
                                        if (!$record->person_id || !$record->person_type) {
                                            return 'Not linked to any person';
                                        }

                                        $person = $record->getPerson();
                                        if (!$person) {
                                            return 'Invalid link - person not found';
                                        }

                                        return $this->getPersonDetails($person);
                                    }),
                            ]),
                    ]),

                Section::make('System Information')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('created_at')
                                    ->label('Created At')
                                    ->dateTime(),

                                TextEntry::make('updated_at')
                                    ->label('Last Updated')
                                    ->dateTime(),

                                TextEntry::make('email_verified_at')
                                    ->label('Email Verified')
                                    ->dateTime()
                                    ->placeholder('Not verified'),
                            ]),
                    ])
                    ->collapsible(),

                Section::make('Login Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('agent')
                                    ->label('Last User Agent')
                                    ->placeholder('Not available'),

                                TextEntry::make('host')
                                    ->label('Last Host')
                                    ->placeholder('Not available'),
                            ]),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    /**
     * Get detailed information about the linked person
     */
    private function getPersonDetails(Model $person): string
    {
        if ($person instanceof Student) {
            $course = $person->course ? $person->course->course_name : 'No Course';
            return "{$person->first_name} {$person->last_name} (ID: {$person->id}) - {$course}";
        }
        
        if ($person instanceof Faculty) {
            $department = $person->department ?? 'No Department';
            return "{$person->getFullNameAttribute()} ({$person->email}) - {$department}";
        }
        
        if ($person instanceof ShsStudent) {
            $strand = $person->strand ? $person->strand->strand_name : 'No Strand';
            return "{$person->fullname} (LRN: {$person->student_lrn}) - {$strand}";
        }
        
        return 'Unknown person type';
    }
}
