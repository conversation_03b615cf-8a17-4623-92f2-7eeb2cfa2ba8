<?php

declare(strict_types=1);

namespace App\Filament\Resources\ShsTrackResource\Pages;

use App\Filament\Resources\ShsTrackResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

final class ViewShsTrack extends ViewRecord
{
    protected static string $resource = ShsTrackResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
