<?php

declare(strict_types=1);

namespace App\Filament\Resources\ShsTrackResource\Pages;

use App\Filament\Resources\ShsTrackResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditShsTrack extends EditRecord
{
    protected static string $resource = ShsTrackResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
