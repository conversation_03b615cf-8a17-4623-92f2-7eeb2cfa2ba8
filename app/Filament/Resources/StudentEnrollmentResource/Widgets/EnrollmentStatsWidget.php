<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentEnrollmentResource\Widgets;

use App\Models\GeneralSetting; // Import GeneralSetting
use App\Models\StudentEnrollment;
use App\Services\GeneralSettingsService;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Cache; // Import Cache
use Illuminate\Support\Facades\DB;

// Add this import

final class EnrollmentStatsWidget extends ChartWidget
{
    protected static ?string $heading = 'Enrollment Distribution';

    protected static ?string $maxHeight = '300px';

    protected function getData(): array
    {
        // Fetch current academic period settings using the service
        $settingsService = app(GeneralSettingsService::class);
        $settingsService->getCurrentSchoolYearString();
        $settingsService->getCurrentSemester();

        // Base query with academic period scope
        $baseQuery = StudentEnrollment::query()
            ->currentAcademicPeriod(); // Apply the scope
        // ->withTrashed(); // Decide if you want to include trashed records in stats

        // --- Course Distribution Data ---
        $courseData = (clone $baseQuery) // Clone the scoped base query
            ->select([
                DB::raw('COUNT(*) as count'),
                DB::raw('CASE
                    WHEN EXISTS (
                        SELECT 1 FROM students s
                        INNER JOIN courses c ON s.course_id = c.id
                        WHERE CAST(student_enrollment.student_id AS BIGINT) = s.id -- Use BIGINT
                        AND c.code LIKE \'BSIT%\'
                    ) THEN \'BSIT\'
                    WHEN EXISTS (
                        SELECT 1 FROM students s
                        INNER JOIN courses c ON s.course_id = c.id
                        WHERE CAST(student_enrollment.student_id AS BIGINT) = s.id -- Use BIGINT
                        AND c.code LIKE \'BSHM%\'
                    ) THEN \'BSHM\'
                    WHEN EXISTS (
                        SELECT 1 FROM students s
                        INNER JOIN courses c ON s.course_id = c.id
                        WHERE CAST(student_enrollment.student_id AS BIGINT) = s.id -- Use BIGINT
                        AND c.code LIKE \'BSBA%\'
                    ) THEN \'BSBA\'
                    ELSE \'Others\'
                END as course'),
            ])
            ->groupBy('course') // Grouping by the calculated CASE statement
            ->get();

        // --- Academic Year Distribution Data ---
        $yearData = (clone $baseQuery) // Clone the scoped base query again
            ->select([
                DB::raw('COUNT(*) as count'),
                'academic_year',
            ])
            ->groupBy('academic_year')
            ->get();

        $courseLabels = $courseData->pluck('course')->toArray();
        $courseCounts = $courseData->pluck('count')->toArray();

        $yearLabels = $yearData->map(fn ($item): string => match ($item->academic_year) {
            '1' => '1st Year',
            '2' => '2nd Year',
            '3' => '3rd Year',
            '4' => '4th Year',
            default => 'Unknown'
        })->toArray();
        $yearCounts = $yearData->pluck('count')->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'Course Distribution',
                    'data' => $courseCounts,
                    'backgroundColor' => [
                        'rgb(54, 162, 235)',   // BSIT - Blue
                        'rgb(255, 99, 132)',   // BSHM - Red
                        'rgb(255, 206, 86)',   // BSBA - Yellow
                        'rgb(75, 192, 192)',   // Others - Green
                    ],
                ],
                [
                    'label' => 'Academic Year Distribution',
                    'data' => $yearCounts,
                    'backgroundColor' => [
                        'rgb(255, 99, 132)',   // 1st Year - Red
                        'rgb(54, 162, 235)',   // 2nd Year - Blue
                        'rgb(255, 206, 86)',   // 3rd Year - Yellow
                        'rgb(75, 192, 192)',   // 4th Year - Green
                    ],
                ],
            ],
            'labels' => [
                'courses' => $courseLabels,
                'years' => $yearLabels,
            ],
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
            ],
            'maintainAspectRatio' => false,
        ];
    }
}
