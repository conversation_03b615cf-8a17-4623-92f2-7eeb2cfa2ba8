<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentEnrollmentResource\Pages;

use App\Filament\Resources\StudentEnrollmentResource;
use App\Filament\Resources\StudentEnrollmentResource\Widgets\AcademicYearStatsWidget;
use App\Filament\Resources\StudentEnrollmentResource\Widgets\EnrollmentStatsWidget;
use App\Filament\Resources\StudentEnrollmentResource\Widgets\StudentEnrollmentStatsWidget;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListStudentEnrollments extends ListRecords
{
    protected static string $resource = StudentEnrollmentResource::class;

    public function getHeaderWidgets(): array
    {
        return [
            StudentEnrollmentStatsWidget::class,
            // EnrollmentStatsWidget::class,
            // AcademicYearStatsWidget::class,
        ];
    }

    public function getTabs(): array
    {
        return self::getResource()::getTabs();
    }

    public function getFooterWidgets(): array
    {
        return [
            AcademicYearStatsWidget::class,
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
