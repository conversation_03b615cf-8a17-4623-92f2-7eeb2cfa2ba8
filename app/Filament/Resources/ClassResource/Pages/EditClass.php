<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassResource\Pages;

use App\Filament\Resources\ClassResource;
use App\Models\Subject;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditClass extends EditRecord
{
    protected static string $resource = ClassResource::class;

    protected function getHeaderActions(): array
    {
        return [Actions\ViewAction::make(), Actions\DeleteAction::make()];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', [
            'record' => $this->record,
        ]);
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // If subject_id is not set but subject_code exists, try to find the correct subject
        if (empty($data['subject_id']) && ! empty($data['subject_code'])) {
            // If course_codes is available, use it to find the correct subject
            if (! empty($data['course_codes'])) {
                $courseIds = is_array($data['course_codes']) ? $data['course_codes'] : json_decode((string) $data['course_codes'], true);
                if (! empty($courseIds)) {
                    // Find subject that matches both the code and is from one of the selected courses
                    $subject = Subject::where('code', $data['subject_code'])
                        ->whereIn('course_id', $courseIds)
                        ->first();

                    if ($subject) {
                        $data['subject_id'] = $subject->id;
                    }
                }
            }

            // If still no subject_id found, try to find any subject with this code
            // but prioritize subjects from enrollment courses
            if (empty($data['subject_id'])) {
                $settingsService = app(\App\Services\GeneralSettingsService::class);
                $enrollmentCourses = $settingsService->getGlobalSettingsModel()->enrollment_courses;

                if (! empty($enrollmentCourses)) {
                    $subject = Subject::where('code', $data['subject_code'])
                        ->whereIn('course_id', $enrollmentCourses)
                        ->first();

                    if ($subject) {
                        $data['subject_id'] = $subject->id;
                    }
                }

                // Last resort: find any subject with this code
                if (empty($data['subject_id'])) {
                    $subject = Subject::where('code', $data['subject_code'])->first();
                    if ($subject) {
                        $data['subject_id'] = $subject->id;
                    }
                }
            }
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Remove tracking fields that shouldn't be saved
        unset($data['_subject_set_by_dropdown']);

        // If subject_id is set, ensure subject_code is also set
        if (! empty($data['subject_id']) && empty($data['subject_code'])) {
            $subject = Subject::find($data['subject_id']);
            if ($subject) {
                $data['subject_code'] = $subject->code;
            }
        }

        // If subject_code is manually entered but no subject_id, try to find matching subject
        if (! empty($data['subject_code']) && empty($data['subject_id'])) {
            $subject = Subject::where('code', $data['subject_code'])->first();
            if ($subject) {
                $data['subject_id'] = $subject->id;
            }
        }

        return $data;
    }
}
