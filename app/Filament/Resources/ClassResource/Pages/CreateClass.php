<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassResource\Pages;

use App\Filament\Resources\ClassResource;
use App\Models\Subject;
use Filament\Resources\Pages\CreateRecord;

final class CreateClass extends CreateRecord
{
    protected static string $resource = ClassResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Remove tracking fields that shouldn't be saved
        unset($data['_subject_set_by_dropdown']);

        // If subject_id is set, ensure subject_code is also set
        if (! empty($data['subject_id']) && empty($data['subject_code'])) {
            $subject = Subject::find($data['subject_id']);
            if ($subject) {
                $data['subject_code'] = $subject->code;
            }
        }

        // If subject_code is manually entered but no subject_id, try to find matching subject
        if (! empty($data['subject_code']) && empty($data['subject_id'])) {
            $subject = Subject::where('code', $data['subject_code'])->first();
            if ($subject) {
                $data['subject_id'] = $subject->id;
            }
        }

        return $data;
    }
}
