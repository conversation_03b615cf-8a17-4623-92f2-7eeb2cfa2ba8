<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassResource\Pages;

use App\Filament\Resources\ClassResource;
use App\Models\Classes;
use App\Services\GeneralSettingsService;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

final class ListClasses extends ListRecords
{
    protected static string $resource = ClassResource::class;

    public function getTabs(): array
    {
        // Get current academic period info for display
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
        $currentSemester = $settingsService->getCurrentSemester();
        $semesterText = $currentSemester === 1 ? '1st Sem' : '2nd Sem';

        return [
            'all' => Tab::make('All Classes')
                ->badge(Classes::currentAcademicPeriod()->count())
                ->icon('heroicon-o-academic-cap')
                ->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod())
                ->label("All Classes ({$currentSchoolYear} - {$semesterText})"),

            'college' => Tab::make('College Classes')
                ->badge(Classes::currentAcademicPeriod()->college()->count())
                ->icon('heroicon-o-building-library')
                ->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod()->college())
                ->label("College ({$currentSchoolYear} - {$semesterText})"),

            'shs' => Tab::make('SHS Classes')
                ->badge(Classes::currentAcademicPeriod()->shs()->count())
                ->icon('heroicon-o-user-group')
                ->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod()->shs())
                ->label("SHS ({$currentSchoolYear} - {$semesterText})"),
        ];
    }

    public function getTitle(): string
    {
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
        $currentSemester = $settingsService->getCurrentSemester();
        $semesterText = $currentSemester === 1 ? '1st Semester' : '2nd Semester';

        return "Classes - {$currentSchoolYear} ({$semesterText})";
    }

    protected function getHeaderActions(): array
    {
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
        $currentSemester = $settingsService->getCurrentSemester();
        $semesterText = $currentSemester === 1 ? '1st Semester' : '2nd Semester';

        return [
            Actions\CreateAction::make()
                ->icon('heroicon-o-plus')
                ->label('New Class'),

            Actions\Action::make('current_period_info')
                ->label("Showing: {$currentSchoolYear} - {$semesterText}")
                ->icon('heroicon-o-information-circle')
                ->color('info')
                ->disabled()
                ->tooltip('Classes are filtered to show only the current academic period. Use the tabs to filter by class type.'),
        ];
    }

    protected function getTableQuery(): Builder
    {
        $query = self::getResource()::getEloquentQuery();

        // Handle table search with proper UUID handling
        if ($this->hasTableSearch() && filled($search = $this->getTableSearch())) {
            $query->where(function ($query) use ($search): void {
                $query->where('subject_code', 'like', "%{$search}%")
                    ->orWhereHas('subject', function ($q) use ($search): void {
                        $q->where('title', 'like', "%{$search}%");
                    })
                    ->orWhereHas('ShsSubject', function ($q) use ($search): void {
                        $q->where('title', 'like', "%{$search}%");
                    })
                    ->orWhereHas('Faculty', function ($q) use ($search): void {
                        // Use explicit joins to avoid UUID casting issues
                        $q->where(function ($subQuery) use ($search): void {
                            $subQuery->whereRaw('LOWER(first_name) LIKE LOWER(?)', ["%{$search}%"])
                                ->orWhereRaw('LOWER(last_name) LIKE LOWER(?)', ["%{$search}%"])
                                ->orWhereRaw('LOWER(CONCAT(first_name, \' \', last_name)) LIKE LOWER(?)', ["%{$search}%"])
                                ->orWhereRaw('LOWER(CONCAT(last_name, \', \', first_name)) LIKE LOWER(?)', ["%{$search}%"]);
                        });
                    })
                    ->orWhere('section', 'like', "%{$search}%")
                    ->orWhere('semester', 'like', "%{$search}%")
                    ->orWhere('school_year', 'like', "%{$search}%");
            });
        }

        return $query;
    }
}
