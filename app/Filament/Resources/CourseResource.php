<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\CourseResource\Pages;
use App\Filament\Resources\CourseResource\RelationManagers\SubjectsRelationManager;
use App\Models\Course;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

final class CourseResource extends Resource
{
    protected static ?string $model = Course::class;


    protected static ?string $navigationGroup = 'Academic Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Course Details')
                    ->tabs([
                        Tabs\Tab::make('Main Info')
                            ->schema([
                                Section::make('Basic Information')
                                    ->description('Enter the core details for the course.')
                                    ->schema([
                                        Forms\Components\TextInput::make('code')
                                            ->required()
                                            ->maxLength(255)
                                            ->unique(ignoreRecord: true)
                                            ->label('Course Code')
                                            ->helperText('Unique code for the course (e.g., BSIT, BSHM).'),
                                        Forms\Components\TextInput::make('title')
                                            ->required()
                                            ->maxLength(255)
                                            ->label('Course Title')
                                            ->helperText('Full title of the course.'),
                                        Forms\Components\Textarea::make('description')
                                            ->required()
                                            ->maxLength(255)
                                            ->label('Description')
                                            ->helperText('Brief description of the course.')
                                            ->columnSpanFull(),
                                    ])->columns(2),
                            ]),

                        Tabs\Tab::make('Additional Info')
                            ->schema([
                                Section::make('Department and Fees')
                                    ->description('Specify department and fee-related information.')
                                    ->schema([
                                        Forms\Components\Select::make('department')
                                            ->required()
                                            ->options([
                                                'IT' => 'Information Technology',
                                                'HM' => 'Hospitality Management',
                                                'BA' => 'Business Administration',
                                                'EDUC' => 'Education',
                                            ])
                                            ->label('Department')
                                            ->helperText('Select the department this course belongs to.'),

                                        Forms\Components\TextInput::make('lec_per_unit')
                                            ->numeric()
                                            ->label('Lecture Fee Per Unit')
                                            ->helperText('Cost per unit for lectures.'),
                                        Forms\Components\TextInput::make('lab_per_unit')
                                            ->numeric()
                                            ->label('Laboratory Fee Per Unit')
                                            ->helperText('Cost per unit for laboratory.'),
                                        Forms\Components\TextInput::make('miscelaneous')
                                            ->numeric()
                                            ->label('Miscellaneous Fee')
                                            ->helperText('Miscellaneous fees for the course.'),
                                        Forms\Components\TextInput::make('remarks')
                                            ->maxLength(255)
                                            ->label('Remarks')
                                            ->helperText('Any additional notes.'),
                                        Forms\Components\TextInput::make('curriculum_year')
                                            ->maxLength(255)
                                            ->label('Curriculum Year')
                                            ->helperText('The curriculum year this course is associated with.'),
                                    ])->columns(2),
                            ]),
                    ])->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->sortable()
                    ->label('Code'),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->label('Title'),
                Tables\Columns\TextColumn::make('department')
                    ->searchable()
                    ->sortable()
                    ->label('Department'),
                Tables\Columns\TextColumn::make('lec_per_unit')
                    ->label('Lec Fee/Unit')
                    ->sortable(),
                Tables\Columns\TextColumn::make('lab_per_unit')
                    ->label('Lab Fee/Unit')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            SubjectsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCourses::route('/'),
            'create' => Pages\CreateCourse::route('/create'),
            'edit' => Pages\EditCourse::route('/{record}/edit'),
            'view' => Pages\ViewCourse::route('/{record}'),
        ];
    }
}
