<?php

declare(strict_types=1);

namespace App\Filament\Resources\ShsStrandResource\Pages;

use App\Filament\Resources\ShsStrandResource;
use Filament\Resources\Pages\CreateRecord;

final class CreateShsStrand extends CreateRecord
{
    protected static string $resource = ShsStrandResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
