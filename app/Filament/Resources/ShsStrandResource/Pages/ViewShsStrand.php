<?php

declare(strict_types=1);

namespace App\Filament\Resources\ShsStrandResource\Pages;

use App\Filament\Resources\ShsStrandResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

final class ViewShsStrand extends ViewRecord
{
    protected static string $resource = ShsStrandResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
