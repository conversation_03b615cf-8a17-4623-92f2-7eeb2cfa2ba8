<?php

declare(strict_types=1);

namespace App\Filament\Resources\ShsStrandResource\Pages;

use App\Filament\Resources\ShsStrandResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListShsStrands extends ListRecords
{
    protected static string $resource = ShsStrandResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
