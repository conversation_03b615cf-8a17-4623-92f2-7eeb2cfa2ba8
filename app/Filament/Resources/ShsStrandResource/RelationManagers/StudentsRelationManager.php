<?php

declare(strict_types=1);

namespace App\Filament\Resources\ShsStrandResource\RelationManagers;

use App\Models\ShsStudent;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

// Import ShsStudent model

final class StudentsRelationManager extends RelationManager
{
    protected static string $relationship = 'students';

    protected static ?string $recordTitleAttribute = 'fullname';

    public function form(Form $form): Form
    {
        // This form is for creating/editing a student WITHIN a specific ShsStrand context.
        // The strand_id will be automatically set by the relationship.
        // The track_id will be derived from the ShsStrand's track_id in mutateFormDataUsing.
        return $form
            ->schema([
                Forms\Components\TextInput::make('student_lrn')
                    ->label('LRN (Learner Reference Number)')
                    ->maxLength(20)
                    ->unique(table: ShsStudent::class, column: 'student_lrn', ignoreRecord: true)
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('fullname')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('grade_level')
                    ->options([
                        'Grade 11' => 'Grade 11',
                        'Grade 12' => 'Grade 12',
                    ])
                    ->required()
                    ->native(false),
                Forms\Components\DatePicker::make('birthdate')
                    ->native(false)
                    ->label('Birthdate'),
                Forms\Components\Select::make('gender')
                    ->options(['Male' => 'Male', 'Female' => 'Female'])
                    ->native(false),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->maxLength(255)
                    ->unique(table: ShsStudent::class, column: 'email', ignoreRecord: true)
                    ->label('Email Address'),
                Forms\Components\TextInput::make('student_contact')
                    ->tel()
                    ->maxLength(20)
                    ->label('Student Contact No.'),
                Forms\Components\Textarea::make('remarks')
                    ->maxLength(65535)
                    ->columnSpanFull(),
                // No need for track_id or strand_id fields here,
                // as they are set contextually.
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            // ->recordTitleAttribute('fullname') // Alternative to static property
            ->columns([
                Tables\Columns\TextColumn::make('student_lrn')
                    ->label('LRN')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fullname')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('grade_level')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('student_contact')
                    ->label('Contact No.')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('grade_level')
                    ->options([
                        'Grade 11' => 'Grade 11',
                        'Grade 12' => 'Grade 12',
                    ])->native(false),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Automatically set track_id from the owner ShsStrand's track.
                        // The strand_id is automatically handled by the Eloquent relationship
                        // when the record is created.
                        $data['track_id'] = $this->getOwnerRecord()->track_id;

                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Ensure track_id remains consistent if it's part of the form,
                        // though typically it wouldn't be editable in this context.
                        $data['track_id'] = $this->getOwnerRecord()->track_id;

                        return $data;
                    }),
                Tables\Actions\DeleteAction::make(),
                // Consider adding a ViewAction that links to the main student resource view page:
                // Tables\Actions\Action::make('view_full_record')
                //     ->label('View Full Record')
                //     ->icon('heroicon-m-arrow-top-right-on-square')
                //     ->url(fn (ShsStudent $record): string => \App\Filament\Resources\ShsStudentResource::getUrl('view', ['record' => $record]))
                //     ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
