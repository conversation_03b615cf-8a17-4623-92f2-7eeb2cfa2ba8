<?php


namespace App\Filament\Resources;

use App\Filament\Actions\ClearanceAction;
use App\Filament\Exports\StudentsExporter;
use App\Filament\Pages\StudentsRevisions;
use App\Filament\Resources\StudentResource\Api\Transformers\StudentTransformer;
use App\Filament\Resources\StudentResource\Pages;
use App\Filament\Resources\StudentResource\RelationManagers;
use App\Filament\Resources\StudentResource\RelationManagers\ClassesRelationManager;
use App\Filament\Resources\StudentResource\RelationManagers\StatementOfAccountRelationManager;
use App\Models\GeneralSetting;
use App\Models\Student as Students;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Carbon\Carbon;
use DiscoveryDesign\FilamentGaze\Forms\Components\GazeBanner;
use Filament\Forms;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Form;
use Filament\Infolists\Components\Fieldset;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\RelationManagers\RelationGroup;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontWeight;
use Filament\Tables;
use Filament\Tables\Actions\ExportBulkAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Mansoor\FilamentVersionable\Table\RevisionsAction;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Ysfkaya\FilamentPhoneInput\Infolists\PhoneEntry;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;

final class StudentResource extends Resource implements HasShieldPermissions
{
    protected static ?string $model = Students::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationGroup = 'Students';

    protected static ?string $navigationLabel = 'College Students';

    protected static int $globalSearchResultsLimit = 5;

    protected static bool $shouldSkipAuthorization = true;

    // protected static ?int $navigationSort = ;

    private static int $defaultTableRecordsPerPageSelect = 10;

    private static array $defaultEagerLoads = [
        'course:id,code',
        'studentContactsInfo:id,personal_contact',
        'personalInfo:id,current_adress,permanent_address',
        'DocumentLocation:id,picture_1x1',
    ];

    public static function getGloballySearchableAttributes(): array
    {
        // if (!auth()->user()->hasPermissionTo('search_students')) {
        //     return [];
        // }
        return ['id', 'last_name', 'first_name'];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        return [
            'id' => $record->id,
            'Full Name' => $record->full_name,
        ];
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view',
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
            'view_BA_students',
            'view_IT_students',
            'view_HM_students',
            'restore',
            'restore_any',
            'force_delete',
            'force_delete_any',
        ];
    }

    public static function getApiTransformer(): string
    {
        return StudentTransformer::class;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->badge()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('full_name')
                    ->searchable(['first_name', 'last_name'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('course.code')->sortable(),
                Tables\Columns\TextColumn::make('gender')->toggleable(
                    isToggledHiddenByDefault: true
                ),
                Tables\Columns\TextColumn::make('academic_year')->toggleable(
                    isToggledHiddenByDefault: true
                ),
                Tables\Columns\TextColumn::make('email')->toggleable(
                    isToggledHiddenByDefault: true
                ),
                Tables\Columns\IconColumn::make('clearances')
                    ->label('Clearance Status')
                    ->getStateUsing(
                        fn (
                            Students $record
                        ): bool => $record->hasCurrentClearance()
                    )
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->tooltip(function (Students $record): string {
                        $settings = GeneralSetting::first();
                        $status = $record->hasCurrentClearance()
                            ? 'Cleared'
                            : 'Not Cleared';

                        return "{$status} for {$settings->getSemester()} {$settings->getSchoolYearString()}";
                    }),
            ])
            ->deferLoading()
            ->defaultPaginationPageOption(5)
            ->persistFiltersInSession()
            ->persistSortInSession()
            ->persistSearchInSession()
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                Tables\Filters\SelectFilter::make('course_id')->relationship(
                    'course',
                    'code'
                ),
                Tables\Filters\SelectFilter::make('academic_year')->options([
                    '1' => '1st Year',
                    '2' => '2nd Year',
                    '3' => '3rd Year',
                    '4' => '4th Year',
                    '5' => 'Graduates',
                ]),
                Tables\Filters\SelectFilter::make('gender')->options([
                    'male' => 'Male',
                    'female' => 'Female',
                ]),
            ])
            ->filtersFormColumns(3)
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                RevisionsAction::make(),
                ClearanceAction::make('manageClearance')->visible(
                    fn (): bool => (bool) GeneralSetting::first()
                        ->enable_clearance_check
                ),
            ])
            ->bulkActions([
                // ExportBulkAction::make()
                //     ->exporter(
                //         StudentsExporter::class,
                //     ),
            ])
            ->paginated([10, 25, 50])
            ->defaultPaginationPageOption(10);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                GazeBanner::make()->hideOnCreate(),
                Forms\Components\Section::make()
                    ->schema(self::getBasicInfoSection())
                    ->columns(2)
                    ->columnSpan([
                        'lg' => fn (?Students $record): int => $record instanceof Students
                            ? 2
                            : 3,
                    ]),
                Forms\Components\Section::make()
                    ->schema(self::getMetadataSection())
                    ->columnSpan(['lg' => 1])
                    ->hidden(
                        fn (?Students $record): bool => ! $record instanceof Students
                    ),
                Forms\Components\Section::make()
                    ->schema(self::getAdditionalInfoSection())
                    ->columns(2)
                    ->columnSpan([
                        'lg' => fn (?Students $record): int => $record instanceof Students
                            ? 2
                            : 3,
                    ])
                    ->collapsed()
                    ->hidden(
                        fn (?Students $record): bool => ! $record instanceof Students
                    )
                    ->lazy(),
            ])
            ->columns(3);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist->schema([
            Section::make('Student Details')
                ->columns([
                    'default' => 1,
                    'md' => 2,
                    'lg' => 3,
                ])
                ->schema([
                    Grid::make([
                        'default' => 1,
                        'md' => 2,
                        'lg' => 3,
                    ])
                        ->columnSpan(['md' => 2])
                        ->schema([
                            TextEntry::make('full_name')->label('Full Name'),
                            TextEntry::make('email')->label('Email'),
                            PhoneEntry::make(
                                'studentContactsInfo.personal_contact'
                            )
                                ->displayFormat(PhoneInputNumberType::NATIONAL)
                                ->label('Phone'),
                            TextEntry::make('birth_date')->label('Birth Date'),
                            ImageEntry::make('DocumentLocation.picture_1x1')
                                ->label('Student Picture')
                                ->circular()
                                ->defaultImageUrl(
                                    'https://via.placeholder.com/150'
                                )
                                ->visibility('private'),
                        ]),
                    Section::make('Student info')
                        ->columnSpan(['md' => 1])
                        ->collapsible()
                        ->schema([
                            TextEntry::make('id')
                                ->badge()
                                ->icon('heroicon-m-user')
                                ->iconColor('warning')
                                ->weight(FontWeight::Bold)
                                ->copyable()
                                ->copyMessage('Copied!')
                                ->label('Student ID'),
                            TextEntry::make('created_at')
                                ->since()
                                ->label('Created at'),

                            TextEntry::make('updated_at')
                                ->since()
                                ->label('Last modified at'),
                            TextEntry::make('Course.code')
                                ->badge()

                                ->label('Course'),
                        ]),

                    Fieldset::make('Address Information')->schema([
                        TextEntry::make('personalInfo.current_adress')->label(
                            'Current Address'
                        ),
                        TextEntry::make(
                            'personalInfo.permanent_address'
                        )->label('Permanent Address'),
                    ]),
                    Tabs::make('Additional Details')
                        ->columnSpan(['md' => 3])
                        ->tabs([
                            Tabs\Tab::make('Academic Information')->schema([
                                TextEntry::make('academic_year')->label(
                                    'Academic Year'
                                ),
                                TextEntry::make('course.code')->label('Course'),
                            ]),
                            Tabs\Tab::make('Parent Information')->schema([
                                TextEntry::make(
                                    'studentParentInfo.fathers_name'
                                )->label('Father Name'),
                                TextEntry::make(
                                    'studentParentInfo.mothers_name'
                                )->label('Mother Name'),
                            ]),
                            Tabs\Tab::make('School Information')->schema([
                                TextEntry::make(
                                    'studentEducationInfo.elementary_school'
                                )->label('Elementary School'),
                                TextEntry::make(
                                    'studentEducationInfo.elementary_graduate_year'
                                )->label('Elementary School Graduation Year'),
                                TextEntry::make(
                                    'studentEducationInfo.elementary_school_address'
                                )->label('Elementary School Address'),
                                TextEntry::make(
                                    'studentEducationInfo.senior_high_name'
                                )->label('Senior High School'),
                                TextEntry::make(
                                    'studentEducationInfo.senior_high_graduate_year'
                                )->label('Senior High School Graduation Year'),
                                TextEntry::make(
                                    'studentEducationInfo.senior_high_address'
                                )->label('Senior High School Address'),
                            ]),
                            Tabs\Tab::make('Contact Information')->schema([
                                TextEntry::make(
                                    'studentContactsInfo.personal_contact'
                                )->label('Phone Number'),
                                TextEntry::make(
                                    'studentContactsInfo.emergency_contact_name'
                                )->label('Emergency Contact Name'),
                                TextEntry::make(
                                    'studentContactsInfo.emergency_contact_phone'
                                )->label('Emergency Contact Phone'),
                                TextEntry::make(
                                    'studentContactsInfo.emergency_contact_address'
                                )->label('Emergency Contact Address'),
                                TextEntry::make(
                                    'studentContactsInfo.facebook_contact'
                                )->label('Facebook Contact'),
                            ]),
                            Tabs\Tab::make('Clearance Status')->schema([
                                TextEntry::make('clearances')
                                    ->label('Current Clearance Status')
                                    ->state(
                                        fn (
                                            $record
                                        ): string => $record->hasCurrentClearance()
                                            ? 'Cleared'
                                            : 'Not Cleared'
                                    )
                                    ->badge()
                                    ->color(
                                        fn (string $state): string => match (
                                            $state
                                        ) {
                                            'Cleared' => 'success',
                                            'Not Cleared' => 'danger',
                                            default => 'gray',
                                        }
                                    ),
                                TextEntry::make(
                                    'getCurrentClearanceRecord.cleared_by'
                                )
                                    ->label('Cleared By')
                                    ->visible(
                                        fn (
                                            $record
                                        ): bool => (bool) $record->hasCurrentClearance()
                                    ),
                                TextEntry::make(
                                    'getCurrentClearanceRecord.cleared_at'
                                )
                                    ->label('Cleared At')
                                    ->dateTime()
                                    ->visible(
                                        fn (
                                            $record
                                        ): bool => (bool) $record->hasCurrentClearance()
                                    ),
                                TextEntry::make(
                                    'getCurrentClearanceRecord.remarks'
                                )
                                    ->label('Remarks')
                                    ->markdown()
                                    ->visible(
                                        fn (
                                            $record
                                        ): bool => (bool) $record->getCurrentClearanceModel()
                                    ),
                                TextEntry::make(
                                    'getCurrentClearanceRecord.academic_year'
                                )
                                    ->label('Academic Year')
                                    ->visible(
                                        fn (
                                            $record
                                        ): bool => (bool) $record->getCurrentClearanceModel()
                                    ),
                                TextEntry::make(
                                    'getCurrentClearanceRecord.formatted_semester'
                                )
                                    ->label('Semester')
                                    ->visible(
                                        fn (
                                            $record
                                        ): bool => (bool) $record->getCurrentClearanceModel()
                                    ),
                            ]),
                            Tabs\Tab::make('Tuition Information')
                                ->schema([
                                    TextEntry::make(
                                        'getCurrentTuitionRecord.formatted_total_tuition'
                                    )
                                        ->label('Total Tuition')
                                        ->placeholder(
                                            'No tuition record found'
                                        ),
                                    TextEntry::make(
                                        'getCurrentTuitionRecord.formatted_total_lectures'
                                    )
                                        ->label('Lecture Fees')
                                        ->placeholder(
                                            'No tuition record found'
                                        ),
                                    TextEntry::make(
                                        'getCurrentTuitionRecord.formatted_total_laboratory'
                                    )
                                        ->label('Laboratory Fees')
                                        ->placeholder(
                                            'No tuition record found'
                                        ),
                                    TextEntry::make(
                                        'getCurrentTuitionRecord.formatted_total_miscelaneous_fees'
                                    )
                                        ->label('Miscellaneous Fees')
                                        ->placeholder(
                                            'No tuition record found'
                                        ),
                                    TextEntry::make(
                                        'getCurrentTuitionRecord.formatted_overall_tuition'
                                    )
                                        ->label('Overall Tuition')
                                        ->placeholder(
                                            'No tuition record found'
                                        ),
                                    TextEntry::make(
                                        'getCurrentTuitionRecord.formatted_downpayment'
                                    )
                                        ->label('Downpayment')
                                        ->placeholder(
                                            'No tuition record found'
                                        ),
                                    TextEntry::make(
                                        'getCurrentTuitionRecord.formatted_total_balance'
                                    )
                                        ->label('Balance')
                                        ->placeholder('No tuition record found')
                                        ->badge()
                                        ->color(function ($record): string {
                                            $tuition = $record->getCurrentTuitionModel();
                                            if (! $tuition) {
                                                return 'gray';
                                            }

                                            return $tuition->total_balance <= 0
                                                ? 'success'
                                                : 'danger';
                                        }),
                                    TextEntry::make(
                                        'getCurrentTuitionRecord.formatted_discount'
                                    )
                                        ->label('Discount')
                                        ->placeholder(
                                            'No tuition record found'
                                        ),
                                    TextEntry::make(
                                        'getCurrentTuitionRecord.payment_status'
                                    )
                                        ->label('Payment Status')
                                        ->placeholder('No tuition record found')
                                        ->badge()
                                        ->color(function ($record): string {
                                            $tuition = $record->getCurrentTuitionModel();
                                            if (! $tuition) {
                                                return 'gray';
                                            }

                                            return $tuition->total_balance <= 0
                                                ? 'success'
                                                : 'warning';
                                        }),
                                    TextEntry::make(
                                        'getCurrentTuitionRecord.formatted_semester'
                                    )
                                        ->label('Semester')
                                        ->placeholder(
                                            'No tuition record found'
                                        ),
                                    TextEntry::make(
                                        'getCurrentTuitionRecord.academic_year'
                                    )
                                        ->label('Academic Year')
                                        ->placeholder(
                                            'No tuition record found'
                                        ),
                                ])
                                ->columns(2),
                            Tabs\Tab::make('Current Enrolled Subjects')->schema(
                                [
                                    \Filament\Infolists\Components\RepeatableEntry::make(
                                        'subjectEnrolled'
                                    )
                                        ->label('Enrolled Subjects')
                                        ->schema([
                                            TextEntry::make(
                                                'subject.code'
                                            )->label('Subject Code'),
                                            TextEntry::make(
                                                'subject.title'
                                            )->label('Subject Title'),
                                            TextEntry::make(
                                                'subject.units'
                                            )->label('Units'),
                                            TextEntry::make(
                                                'class.section'
                                            )->label('Section'),
                                        ])
                                        ->columns(4)
                                        ->columnSpan(2),
                                ]
                            ),
                            Tabs\Tab::make('Document Location')
                                ->columns([
                                    'default' => 1,
                                    'md' => 2,
                                    'lg' => 3,
                                ])
                                ->schema([
                                    ImageEntry::make(
                                        'DocumentLocation.transcript_records'
                                    )
                                        ->label('Transcript Records')

                                        ->defaultImageUrl(
                                            'https://via.placeholder.com/150'
                                        )
                                        ->visibility('private'),
                                    ImageEntry::make(
                                        'DocumentLocation.transfer_credentials'
                                    )
                                        ->label('Transfer Credentials')

                                        ->defaultImageUrl(
                                            'https://via.placeholder.com/150'
                                        )
                                        ->visibility('private'),
                                    ImageEntry::make(
                                        'DocumentLocation.good_moral_cert'
                                    )
                                        ->label('Good Moral Certificate')

                                        ->defaultImageUrl(
                                            'https://via.placeholder.com/150'
                                        )
                                        ->visibility('private'),
                                    ImageEntry::make(
                                        'DocumentLocation.form_137'
                                    )
                                        ->label('Form 137')

                                        ->defaultImageUrl(
                                            'https://via.placeholder.com/150'
                                        )
                                        ->visibility('private'),
                                    ImageEntry::make(
                                        'DocumentLocation.form_138'
                                    )
                                        ->label('Form 138')

                                        ->defaultImageUrl(
                                            'https://via.placeholder.com/150'
                                        )
                                        ->visibility('private'),
                                    ImageEntry::make(
                                        'DocumentLocation.birth_certificate'
                                    )
                                        ->label('Birth Certificate')

                                        ->defaultImageUrl(
                                            'https://via.placeholder.com/150'
                                        )
                                        ->visibility('private'),
                                ]),
                        ]),
                    // Tabs::make('Academic Years')
                    // ->tabs(function ($record) {
                    //     // Assuming $record is the student instance
                    //     $student = Students::with('subjects')->find($record->getKey());
                    //     $subjectsByYear = $student->subjects->groupBy('academic_year');

                    //     return $subjectsByYear->map(function ($subjects, $year) {
                    //         return Tabs\Tab::make("Year {$year}")
                    //             ->schema([
                    //                 Section::make("Subjects for Academic Year {$year}")
                    //                     ->schema($subjects->map(function ($subject) {
                    //                         return [
                    //                             TextEntry::make('title')
                    //                                 ->label('Title')

                    //                             TextEntry::make('code')
                    //                                 ->label('Code')
                    //                                 ->default($subject->code),
                    //                             TextEntry::make('units')
                    //                                 ->label('Units')
                    //                                 ->default($subject->units),
                    //                         ];
                    //                     })->toArray()),
                    //             ]);
                    //     })->toArray();
                    // }),
                ]),
            Section::make('CheckList')
                ->collapsed()
                ->make([
                    ViewEntry::make('CheckList')->view(
                        'infolist.student-checklist-entry'
                    ),
                ]),
        ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationGroup::make('Enrolled Subject', [
                ClassesRelationManager::class,
            ]),
            RelationGroup::make('Academic Information', [
                RelationManagers\EnrolledSubjectsRelationManager::class,
            ]),
            RelationGroup::make('Financial Records', [
                StatementOfAccountRelationManager::class,
            ]),
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStudents::route('/'),
            'create' => Pages\CreateStudents::route('/create'),
            'view' => Pages\ViewStudents::route('/{record}'),
            'edit' => Pages\EditStudents::route('/{record}/edit'),
            'history' => Pages\StudentHistory::route('/{record}/history'),
            'revisions' => StudentsRevisions::route('/{record}/revisions'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return (string) cache()->remember(
            'students_count',
            3600,
            fn () => self::getModel()::count()
        );
    }

    private static function getBasicInfoSection(): array
    {
        return [
            Forms\Components\TextInput::make('id')
                ->label('Student ID')
                ->unique(Students::class, 'id', ignoreRecord: true)
                ->numeric()
                ->required()
                ->rules(['required', 'numeric']),
            // ->disabled(fn (?Students $record) => $record !== null && !auth()->user()->can('update_student_id')),
            Forms\Components\TextInput::make('first_name')
                ->maxLength(50)
                ->required(),
            Forms\Components\TextInput::make('last_name')
                ->maxLength(50)
                ->required(),
            Forms\Components\TextInput::make('middle_name')->maxLength(20),
            self::getGenderSelect(),
            self::getBirthDatePicker(),
            Forms\Components\TextInput::make('age')
                ->readonly()
                ->numeric()
                ->required(),
            Forms\Components\TextInput::make('email')
                ->label('Email address')
                ->email()
                ->maxLength(255),
            Forms\Components\Select::make('course_id')
                ->relationship('course', 'code')
                ->searchable()
                ->preload(),
            Forms\Components\Select::make('academic_year')
                ->options([
                    '1' => '1st Year',
                    '2' => '2nd Year',
                    '3' => '3rd Year',
                    '4' => '4th Year',
                    '5' => 'Graduate',
                ])
                ->required(),

            Forms\Components\Textarea::make('remarks')
                ->label('Remarks')
                ->columnSpanFull(),
        ];
    }

    private static function getGenderSelect(): Forms\Components\Select
    {
        return Forms\Components\Select::make('gender')
            ->options([
                'male' => 'Male',
                'female' => 'Female',
            ])
            ->required();
    }

    private static function getBirthDatePicker(): Forms\Components\DatePicker
    {
        return Forms\Components\DatePicker::make('birth_date')
            ->maxDate('today')
            ->live(debounce: 500)
            ->afterStateUpdated(function ($set, $state): void {
                if ($state) {
                    $age = Carbon::parse($state)->age;
                    $set('age', $age);
                }
            })
            ->required();
    }

    private static function getMetadataSection(): array
    {
        return [
            Placeholder::make('id')
                ->label('Student ID')
                ->content(fn (?Students $record): ?string => $record?->id),
            Placeholder::make('created_at')
                ->label('Created at')
                ->content(
                    fn (
                        Students $record
                    ): ?string => $record->created_at?->diffForHumans()
                ),
            Placeholder::make('Course')
                ->label('Course')
                ->content(
                    fn (Students $record): ?string => $record->Course->code ??
                        null
                ),
        ];
    }

    private static function getAdditionalInfoSection(): array
    {
        return [
            self::getGuardianContactInfo(),
            self::getParentInfo(),
            self::getEducationInfo(),
            self::getAddressInfo(),
            self::getPersonalInfo(),
        ];
    }

    private static function getGuardianContactInfo(): Forms\Components\Fieldset
    {
        return Forms\Components\Fieldset::make('Guardian Contact Informations')
            ->relationship('studentContactsInfo')
            ->schema([
                PhoneInput::make('personal_contact')
                    ->label('Student Contact Number')
                    ->initialCountry('ph'),
                Forms\Components\TextInput::make(
                    'emergency_contact_name'
                )->label('Guardian Name'),
                PhoneInput::make('emergency_contact_phone')
                    ->defaultCountry('PH')
                    ->initialCountry('ph')
                    ->label('Guardian Contact Number'),
                Forms\Components\TextInput::make(
                    'emergency_contact_address'
                )->label('Guardian Address'),
            ]);
    }

    private static function getParentInfo(): Forms\Components\Fieldset
    {
        return Forms\Components\Fieldset::make('Parent Information')
            ->relationship('studentParentInfo')
            ->schema([
                Forms\Components\TextInput::make('fathers_name')->label(
                    "Father's Name"
                ),
                Forms\Components\TextInput::make('mothers_name')->label(
                    "Mother's Name"
                ),
            ]);
    }

    private static function getEducationInfo(): Forms\Components\Fieldset
    {
        return Forms\Components\Fieldset::make('Education Information')
            ->relationship('studentEducationInfo')
            ->schema([
                Forms\Components\TextInput::make('elementary_school')->label(
                    'Elementary School'
                ),
                Forms\Components\TextInput::make(
                    'elementary_graduate_year'
                )->label('Elementary School Graduation Year'),
                Forms\Components\TextInput::make('elementary_school_address')
                    ->columnSpanFull()
                    ->label('Elementary School Address'),
                Forms\Components\TextInput::make(
                    'junior_high_school_name'
                )->label('Junior High School Name'),
                Forms\Components\TextInput::make(
                    'junior_high_graduation_year'
                )->label('Junior High School Graduation Year'),
                Forms\Components\TextInput::make('junior_high_school_address')
                    ->columnSpanFull()
                    ->label('Junior High School Address'),
                Forms\Components\TextInput::make('senior_high_name')->label(
                    'Senior High School'
                ),
                Forms\Components\TextInput::make(
                    'senior_high_graduate_year'
                )->label('Senior High School Graduation Year'),
                Forms\Components\TextInput::make('senior_high_address')
                    ->columnSpanFull()
                    ->label('Senior High School Address'),
            ]);
    }

    private static function getAddressInfo(): Forms\Components\Fieldset
    {
        return Forms\Components\Fieldset::make('Address Information')
            ->relationship('personalInfo')
            ->schema([
                Forms\Components\TextInput::make('current_adress')->label(
                    'Current Address'
                ),
                Forms\Components\TextInput::make('permanent_address')->label(
                    'Permanent Address'
                ),
            ])
            ->columns(1);
    }

    private static function getPersonalInfo(): Forms\Components\Fieldset
    {
        return Forms\Components\Fieldset::make('Personal Information')
            ->relationship('personalInfo')
            ->schema([
                Forms\Components\TextInput::make('birthplace')
                    ->hint('(Municipality / City)')
                    ->label('Birthplace'),
                Forms\Components\TextInput::make('civil_status')->label(
                    'Civil Status'
                ),
                Forms\Components\TextInput::make('citizenship')->label(
                    'Citizenship'
                ),
                Forms\Components\TextInput::make('religion')->label('Religion'),
                Forms\Components\TextInput::make('weight')
                    ->label('Weight')
                    ->numeric(),
                Forms\Components\TextInput::make('height')->label('Height'),
                Forms\Components\TextInput::make('current_adress')->label(
                    'Current Address'
                ),
                Forms\Components\TextInput::make('permanent_address')->label(
                    'Permanent Address'
                ),
            ])
            ->columns(2);
    }
}
