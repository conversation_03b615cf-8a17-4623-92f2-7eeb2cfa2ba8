<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassGradeManagementResource\Pages;

use App\Filament\Resources\ClassGradeManagementResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditClassGradeManagement extends EditRecord
{
    protected static string $resource = ClassGradeManagementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
