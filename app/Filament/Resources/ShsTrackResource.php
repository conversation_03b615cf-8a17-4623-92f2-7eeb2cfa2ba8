<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\ShsTrackResource\Pages;
use App\Filament\Resources\ShsTrackResource\RelationManagers;
use App\Models\ShsTrack;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

final class ShsTrackResource extends Resource
{
    protected static ?string $model = ShsTrack::class;

    protected static ?string $navigationIcon = 'heroicon-o-flag'; // Changed icon

    protected static ?string $navigationGroup = 'Senior High School'; // Grouping navigation items

    protected static ?int $navigationSort = 1; // To order it before ShsStrandResource

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('track_name')
                    ->required()
                    ->maxLength(255)
                    ->label('Track Name'),
                Forms\Components\Textarea::make('description')
                    ->maxLength(65535)
                    ->columnSpanFull()
                    ->label('Description'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('track_name')
                    ->searchable()
                    ->sortable()
                    ->label('Track Name'),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->tooltip(fn (ShsTrack $record): ?string => $record->description) // Show full description on hover
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('strands_count')->counts('strands') // Show count of strands
                    ->label('No. of Strands')
                    ->sortable(),
                Tables\Columns\TextColumn::make('students_count')->counts('students') // Show count of students in this track
                    ->label('No. of Students (Track)')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                // You can add filters here if needed
            ])
            ->actions([
                Tables\Actions\ViewAction::make(), // Added view action
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\StrandsRelationManager::class,
            // If you want to list ALL students associated with this track (directly or via strands),
            // you might need a more complex relation manager or a different approach.
            // For now, StrandsRelationManager will show strands under a track,
            // and ShsStudentResource will handle student listing with filters.
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShsTracks::route('/'),
            'create' => Pages\CreateShsTrack::route('/create'),
            'view' => Pages\ViewShsTrack::route('/{record}'), // Added view page
            'edit' => Pages\EditShsTrack::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string // Show count in navigation
    {
        return (string) self::getModel()::count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'primary';
    }
}
