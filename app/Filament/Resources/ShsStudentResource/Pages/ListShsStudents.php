<?php

declare(strict_types=1);

namespace App\Filament\Resources\ShsStudentResource\Pages;

use App\Filament\Resources\ShsStudentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListShsStudents extends ListRecords
{
    protected static string $resource = ShsStudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
