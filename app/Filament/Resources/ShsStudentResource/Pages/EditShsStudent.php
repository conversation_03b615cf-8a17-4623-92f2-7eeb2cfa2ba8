<?php

declare(strict_types=1);

namespace App\Filament\Resources\ShsStudentResource\Pages;

use App\Filament\Resources\ShsStudentResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditShsStudent extends EditRecord
{
    protected static string $resource = ShsStudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
