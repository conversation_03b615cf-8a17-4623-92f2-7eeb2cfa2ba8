<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassGradeSubmissionResource\Pages;

use App\Filament\Resources\ClassGradeSubmissionResource;
use App\Filament\Resources\GradeApprovalResource;
use Filament\Actions;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Enums\FontWeight;

final class ViewClassGradeSubmission extends ViewRecord
{
    protected static string $resource = ClassGradeSubmissionResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist->schema([
            Section::make('Class Information')->schema([
                Grid::make(3)->schema([
                    TextEntry::make('subject.code')
                        ->label('Subject Code')
                        ->weight(FontWeight::Bold),

                    TextEntry::make('subject.title')->label('Subject Title'),

                    TextEntry::make('section')
                        ->label('Section')
                        ->badge()
                        ->color('primary'),
                ]),

                Grid::make(3)->schema([
                    TextEntry::make('faculty.name')
                        ->label('Faculty')
                        ->icon('heroicon-o-academic-cap'),

                    TextEntry::make('academic_year')->label('Academic Year'),

                    TextEntry::make('semester')
                        ->label('Semester')
                        ->formatStateUsing(
                            fn ($state) => match ($state) {
                                '1' => '1st Semester',
                                '2' => '2nd Semester',
                                default => $state,
                            }
                        ),
                ]),
            ]),

            Section::make('Grade Submission Summary')->schema([
                Grid::make(4)->schema([
                    TextEntry::make('total_students')
                        ->label('Total Students')
                        ->formatStateUsing(
                            fn ($record) => $record->enrollments()->count()
                        )
                        ->icon('heroicon-o-users'),

                    TextEntry::make('finalized_grades')
                        ->label('Finalized Grades')
                        ->formatStateUsing(
                            fn ($record) => $record
                                ->enrollments()
                                ->where('is_grades_finalized', true)
                                ->count()
                        )
                        ->icon('heroicon-o-clipboard-document-list')
                        ->color('warning'),

                    TextEntry::make('verified_grades')
                        ->label('Verified Grades')
                        ->formatStateUsing(
                            fn ($record) => $record
                                ->enrollments()
                                ->where('is_grades_verified', true)
                                ->count()
                        )
                        ->icon('heroicon-o-check-circle')
                        ->color('success'),

                    TextEntry::make('pending_verification')
                        ->label('Pending Verification')
                        ->formatStateUsing(
                            fn ($record) => $record
                                ->enrollments()
                                ->where('is_grades_finalized', true)
                                ->where('is_grades_verified', false)
                                ->count()
                        )
                        ->icon('heroicon-o-clock')
                        ->color('danger'),
                ]),
            ]),

            Section::make('Grade Submission Status')->schema([
                RepeatableEntry::make('enrollments')
                    ->schema([
                        Grid::make(4)->schema([
                            TextEntry::make('student.full_name')
                                ->label('Student')

                                ->columnSpan(1),

                            TextEntry::make('total_average')
                                ->label('Average')
                                ->numeric(decimalPlaces: 2)
                                ->suffix('%')
                                ->weight(FontWeight::Bold)
                                ->color(
                                    fn ($record): string => $record->total_average >= 75
                                        ? 'success'
                                        : 'danger'
                                ),

                            TextEntry::make('submission_status')
                                ->label('Status')
                                ->badge()
                                ->formatStateUsing(
                                    fn ($record): string => $record->is_grades_verified
                                        ? 'Verified'
                                        : ($record->is_grades_finalized
                                            ? 'Finalized'
                                            : 'Draft')
                                )
                                ->color(
                                    fn ($record): string => $record->is_grades_verified
                                        ? 'success'
                                        : ($record->is_grades_finalized
                                            ? 'warning'
                                            : 'gray')
                                ),

                            TextEntry::make('verification_date')
                                ->label('Verified On')
                                ->date('F j, Y')
                                ->formatStateUsing(
                                    fn ($record) => $record->verified_at
                                )
                                ->placeholder('Not verified')
                                ->color(
                                    fn ($record): string => $record->verified_at
                                        ? 'success'
                                        : 'danger'
                                )
                                ->badge(
                                    fn ($record): string => $record->verified_at
                                        ? 'Verified'
                                        : 'Not Verified'
                                ),
                        ]),
                    ])
                    ->columnSpan('full'),
            ]),
        ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('manage_grades')
                ->label('Manage Grade Approvals')
                ->icon('heroicon-o-check')
                ->url(
                    fn (): string => GradeApprovalResource::getUrl('index', [
                        'tableFilters[class_id][value]' => $this->record->id,
                    ])
                ),

            Actions\Action::make('verify_all')
                ->label('Verify All Pending Grades')
                ->icon('heroicon-o-shield-check')
                ->color('success')
                ->requiresConfirmation()
                ->visible(
                    fn () => $this->record
                        ->enrollments()
                        ->where('is_grades_finalized', true)
                        ->where('is_grades_verified', false)
                        ->exists()
                )
                ->action(function (): void {
                    $this->record
                        ->enrollments()
                        ->where('is_grades_finalized', true)
                        ->where('is_grades_verified', false)
                        ->update([
                            'is_grades_verified' => true,
                            'verified_by' => auth()->id(),
                            'verified_at' => now(),
                        ]);

                    Notification::make()
                        ->title('All pending grades have been verified')
                        ->success()
                        ->send();

                }),

            Actions\Action::make('export_grades')
                ->label('Export Grade Report')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->action(function (): void {
                    // Could implement export functionality here
                    Notification::make()
                        ->title('Grade Export is not available right now')
                        ->success()
                        ->send();
                }),
        ];
    }
}
