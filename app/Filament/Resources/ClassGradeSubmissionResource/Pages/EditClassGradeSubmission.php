<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassGradeSubmissionResource\Pages;

use App\Filament\Resources\ClassGradeSubmissionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditClassGradeSubmission extends EditRecord
{
    protected static string $resource = ClassGradeSubmissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
