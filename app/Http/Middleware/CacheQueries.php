<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\DB;

final class CacheQueries
{
    public function handle($request, Closure $next)
    {
        if ($request->is('admin/students*')) {
            DB::enableQueryLog();
        }

        $response = $next($request);

        if ($request->is('admin/students*')) {
            $queries = DB::getQueryLog();
            // Cache expensive queries
            foreach ($queries as $query) {
                if (str_contains((string) $query['sql'], 'COUNT(*)') || str_contains((string) $query['sql'], 'GROUP BY')) {
                    $key = 'query_'.md5($query['sql'].serialize($query['bindings']));
                    cache()->remember($key, 3600, fn () => DB::select($query['sql'], $query['bindings']));
                }
            }
        }

        return $response;
    }
}
