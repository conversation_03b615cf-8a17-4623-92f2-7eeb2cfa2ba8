<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\ClassResource;
use App\Http\Resources\ScheduleResource;
use App\Http\Resources\StudentResource;
use App\Models\Classes;
use App\Models\Course;
use App\Models\Schedule;
use App\Models\Student;
use App\Services\GeneralSettingsService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

/**
 * @tags Classes & Schedules
 */
final class ClassesApiController extends Controller
{
    private GeneralSettingsService $settingsService;

    public function __construct(GeneralSettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    /**
     * Get all available classes for the current school year
     *
     * This endpoint returns all classes available for the current school year and semester,
     * including their schedules, enrolled students, and related information.
     * Results can be filtered by various parameters.
     *
     * @queryParam course_code string Filter by course code (e.g., "BSIT", "BSCS")
     * @queryParam academic_year integer Filter by academic year (1, 2, 3, 4)
     * @queryParam semester integer Filter by semester (1, 2)
     * @queryParam section string Filter by section name
     * @queryParam subject_code string Filter by subject code
     * @queryParam faculty_id string Filter by faculty ID
     * @queryParam classification string Filter by classification (e.g., "Regular", "Irregular")
     * @queryParam grade_level string Filter by grade level (for SHS)
     * @queryParam with_schedules boolean Include schedule information (default: true)
     * @queryParam with_students boolean Include enrolled students (default: false)
     * @queryParam with_faculty boolean Include faculty information (default: true)
     * @queryParam with_subject boolean Include subject information (default: true)
     * @queryParam per_page integer Number of results per page (default: 50, max: 100)
     * @queryParam page integer Page number for pagination
     *
     * @response 200 {
     *   "data": [
     *     {
     *       "id": 1,
     *       "subject_code": "IT101",
     *       "academic_year": "1",
     *       "semester": "1",
     *       "school_year": "2023 - 2024",
     *       "section": "A",
     *       "maximum_slots": 40,
     *       "enrolled_count": 35,
     *       "available_slots": 5,
     *       "subject": {
     *         "code": "IT101",
     *         "title": "Introduction to Computing"
     *       },
     *       "faculty": {
     *         "full_name": "John Doe",
     *         "email": "<EMAIL>"
     *       },
     *       "schedules": [
     *         {
     *           "day_of_week": "Monday",
     *           "time_range": "08:00 AM - 10:00 AM",
     *           "room": {
     *             "name": "Computer Lab 1"
     *           }
     *         }
     *       ]
     *     }
     *   ],
     *   "meta": {
     *     "current_page": 1,
     *     "per_page": 50,
     *     "total": 150
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $cacheKey = 'api_classes_' . md5($request->getQueryString() ?? '');
        
        $result = Cache::remember($cacheKey, 300, function () use ($request) {
            $query = Classes::query()
                ->currentAcademicPeriod()
                ->withCount('class_enrollments');

            // Apply filters
            $this->applyFilters($query, $request);

            // Apply relationships based on request parameters
            $this->applyRelationships($query, $request);

            // Pagination
            $perPage = min((int) $request->get('per_page', 50), 100);
            $classes = $query->paginate($perPage);

            return [
                'data' => ClassResource::collection($classes->items()),
                'meta' => [
                    'current_page' => $classes->currentPage(),
                    'per_page' => $classes->perPage(),
                    'total' => $classes->total(),
                    'last_page' => $classes->lastPage(),
                    'from' => $classes->firstItem(),
                    'to' => $classes->lastItem(),
                ],
                'school_info' => [
                    'current_school_year' => $this->settingsService->getCurrentSchoolYearString(),
                    'current_semester' => $this->settingsService->getCurrentSemester(),
                    'semester_name' => $this->settingsService->getAvailableSemesters()[$this->settingsService->getCurrentSemester()] ?? '',
                ],
            ];
        });

        return response()->json($result);
    }

    /**
     * Get a specific class by ID
     *
     * @urlParam id integer required The ID of the class
     * @queryParam with_schedules boolean Include schedule information (default: true)
     * @queryParam with_students boolean Include enrolled students (default: true)
     * @queryParam with_faculty boolean Include faculty information (default: true)
     * @queryParam with_subject boolean Include subject information (default: true)
     */
    public function show(Request $request, int $id): JsonResponse
    {
        $cacheKey = "api_class_{$id}_" . md5($request->getQueryString() ?? '');
        
        $result = Cache::remember($cacheKey, 300, function () use ($request, $id) {
            $query = Classes::query()
                ->currentAcademicPeriod()
                ->withCount('class_enrollments')
                ->where('id', $id);

            // Apply relationships
            $this->applyRelationships($query, $request, true);

            $class = $query->firstOrFail();

            return [
                'data' => new ClassResource($class),
                'school_info' => [
                    'current_school_year' => $this->settingsService->getCurrentSchoolYearString(),
                    'current_semester' => $this->settingsService->getCurrentSemester(),
                    'semester_name' => $this->settingsService->getAvailableSemesters()[$this->settingsService->getCurrentSemester()] ?? '',
                ],
            ];
        });

        return response()->json($result);
    }

    /**
     * Apply filters to the query based on request parameters
     */
    private function applyFilters(Builder $query, Request $request): void
    {
        if ($request->filled('course_code')) {
            $query->whereJsonContains('course_codes', $request->get('course_code'));
        }

        if ($request->filled('academic_year')) {
            $query->where('academic_year', $request->get('academic_year'));
        }

        if ($request->filled('semester')) {
            $query->where('semester', $request->get('semester'));
        }

        if ($request->filled('section')) {
            $query->where('section', 'like', '%' . $request->get('section') . '%');
        }

        if ($request->filled('subject_code')) {
            $query->where('subject_code', 'like', '%' . $request->get('subject_code') . '%');
        }

        if ($request->filled('faculty_id')) {
            $query->where('faculty_id', $request->get('faculty_id'));
        }

        if ($request->filled('classification')) {
            $query->where('classification', $request->get('classification'));
        }

        if ($request->filled('grade_level')) {
            $query->where('grade_level', $request->get('grade_level'));
        }

        if ($request->filled('room_id')) {
            $query->where('room_id', $request->get('room_id'));
        }
    }

    /**
     * Apply relationships to the query based on request parameters
     */
    private function applyRelationships(Builder $query, Request $request, bool $includeStudentsByDefault = false): void
    {
        $relationships = [];

        if ($request->boolean('with_schedules', true)) {
            $relationships[] = 'Schedule.room';
        }

        if ($request->boolean('with_students', $includeStudentsByDefault)) {
            $relationships[] = 'enrollments.student.course';
        }

        if ($request->boolean('with_faculty', true)) {
            $relationships[] = 'Faculty';
        }

        if ($request->boolean('with_subject', true)) {
            $relationships[] = 'SubjectById';
            $relationships[] = 'SubjectByCode.course';
        }

        $relationships[] = 'Room';
        $relationships[] = 'ShsTrack';
        $relationships[] = 'ShsStrand';

        if (!empty($relationships)) {
            $query->with($relationships);
        }
    }

    /**
     * Get all schedules for the current school year
     *
     * This endpoint returns all schedules for classes in the current school year and semester.
     *
     * @queryParam day_of_week string Filter by day of the week (Monday, Tuesday, etc.)
     * @queryParam room_id integer Filter by room ID
     * @queryParam class_id integer Filter by class ID
     * @queryParam start_time string Filter by start time (HH:MM format)
     * @queryParam end_time string Filter by end time (HH:MM format)
     * @queryParam per_page integer Number of results per page (default: 50, max: 100)
     */
    public function schedules(Request $request): JsonResponse
    {
        $cacheKey = 'api_schedules_' . md5($request->getQueryString() ?? '');

        $result = Cache::remember($cacheKey, 300, function () use ($request) {
            $query = Schedule::query()
                ->currentAcademicPeriod()
                ->with(['room', 'class.SubjectById', 'class.SubjectByCode', 'class.Faculty']);

            // Apply filters
            if ($request->filled('day_of_week')) {
                $query->where('day_of_week', $request->get('day_of_week'));
            }

            if ($request->filled('room_id')) {
                $query->where('room_id', $request->get('room_id'));
            }

            if ($request->filled('class_id')) {
                $query->where('class_id', $request->get('class_id'));
            }

            if ($request->filled('start_time')) {
                $query->whereTime('start_time', '>=', $request->get('start_time'));
            }

            if ($request->filled('end_time')) {
                $query->whereTime('end_time', '<=', $request->get('end_time'));
            }

            // Pagination
            $perPage = min((int) $request->get('per_page', 50), 100);
            $schedules = $query->paginate($perPage);

            return [
                'data' => ScheduleResource::collection($schedules->items()),
                'meta' => [
                    'current_page' => $schedules->currentPage(),
                    'per_page' => $schedules->perPage(),
                    'total' => $schedules->total(),
                    'last_page' => $schedules->lastPage(),
                    'from' => $schedules->firstItem(),
                    'to' => $schedules->lastItem(),
                ],
                'school_info' => [
                    'current_school_year' => $this->settingsService->getCurrentSchoolYearString(),
                    'current_semester' => $this->settingsService->getCurrentSemester(),
                    'semester_name' => $this->settingsService->getAvailableSemesters()[$this->settingsService->getCurrentSemester()] ?? '',
                ],
            ];
        });

        return response()->json($result);
    }

    /**
     * Get all enrolled students for the current school year
     *
     * This endpoint returns all students enrolled in classes for the current school year and semester.
     *
     * @queryParam class_id integer Filter by class ID
     * @queryParam course_id integer Filter by course ID
     * @queryParam academic_year integer Filter by academic year
     * @queryParam status string Filter by student status
     * @queryParam search string Search by student name or ID
     * @queryParam per_page integer Number of results per page (default: 50, max: 100)
     */
    public function students(Request $request): JsonResponse
    {
        $cacheKey = 'api_enrolled_students_' . md5($request->getQueryString() ?? '');

        $result = Cache::remember($cacheKey, 300, function () use ($request) {
            $query = Student::query()
                ->whereHas('ClassEnrollments.class', function (Builder $classQuery) {
                    $classQuery->currentAcademicPeriod();
                })
                ->with(['course', 'ClassEnrollments.class.SubjectById', 'ClassEnrollments.class.SubjectByCode']);

            // Apply filters
            if ($request->filled('class_id')) {
                $query->whereHas('ClassEnrollments', function (Builder $enrollmentQuery) use ($request) {
                    $enrollmentQuery->where('class_id', $request->get('class_id'));
                });
            }

            if ($request->filled('course_id')) {
                $query->where('course_id', $request->get('course_id'));
            }

            if ($request->filled('academic_year')) {
                $query->where('academic_year', $request->get('academic_year'));
            }

            if ($request->filled('status')) {
                $query->where('status', $request->get('status'));
            }

            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function (Builder $searchQuery) use ($search) {
                    $searchQuery->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%")
                        ->orWhere('middle_name', 'like', "%{$search}%")
                        ->orWhere('student_id', 'like', "%{$search}%");
                });
            }

            // Pagination
            $perPage = min((int) $request->get('per_page', 50), 100);
            $students = $query->paginate($perPage);

            return [
                'data' => StudentResource::collection($students->items()),
                'meta' => [
                    'current_page' => $students->currentPage(),
                    'per_page' => $students->perPage(),
                    'total' => $students->total(),
                    'last_page' => $students->lastPage(),
                    'from' => $students->firstItem(),
                    'to' => $students->lastItem(),
                ],
                'school_info' => [
                    'current_school_year' => $this->settingsService->getCurrentSchoolYearString(),
                    'current_semester' => $this->settingsService->getCurrentSemester(),
                    'semester_name' => $this->settingsService->getAvailableSemesters()[$this->settingsService->getCurrentSemester()] ?? '',
                ],
            ];
        });

        return response()->json($result);
    }

    /**
     * Get course schedules formatted for website display
     *
     * This endpoint returns schedules for specific courses and year levels, formatted
     * for easy display on your school website with proper day abbreviations and time formatting.
     *
     * @group Course Schedules
     *
     * @queryParam course_code string required The course code to filter by (e.g., "BSIT (2024 - 2025)", "BSBA (2018 - 2019)")
     * @queryParam academic_year integer Filter by academic year (1, 2, 3, 4)
     * @queryParam semester integer Filter by semester (1, 2). If not provided, uses current semester
     * @queryParam school_year string Filter by school year (e.g., "2024 - 2025"). If not provided, uses current school year
     * @queryParam per_page integer Number of results per page (default: 100, max: 500)
     *
     * @response 200 {
     *   "data": [
     *     {
     *       "code": "GE-1",
     *       "title": "Understanding the Self",
     *       "schedule": "08:00 AM - 10:00 AM MWF",
     *       "room": "Room 501",
     *       "section": "A",
     *       "faculty": "John Doe",
     *       "units": 3,
     *       "academic_year": 1,
     *       "semester": 1,
     *       "raw_schedules": [
     *         {
     *           "day_of_week": "Monday",
     *           "start_time": "08:00",
     *           "end_time": "10:00",
     *           "room": "Room 501"
     *         }
     *       ]
     *     }
     *   ],
     *   "meta": {
     *     "current_page": 1,
     *     "per_page": 100,
     *     "total": 25
     *   },
     *   "course_info": {
     *     "course_code": "BSIT (2024 - 2025)",
     *     "course_title": "Bachelor of Science in Information Technology",
     *     "academic_year": 1,
     *     "semester": 1
     *   },
     *   "school_info": {
     *     "current_school_year": "2025 - 2026",
     *     "current_semester": 1,
     *     "semester_name": "1st Semester"
     *   }
     * }
     */
    public function courseSchedules(Request $request): JsonResponse
    {
        $request->validate([
            'course_code' => 'required|string',
            'academic_year' => 'nullable|integer|min:1|max:4',
            'semester' => 'nullable|integer|in:1,2',
            'school_year' => 'nullable|string',
            'per_page' => 'nullable|integer|min:1|max:500',
        ]);

        $courseCode = $request->get('course_code');
        $academicYear = $request->get('academic_year');
        $semester = $request->get('semester', $this->settingsService->getCurrentSemester());
        $schoolYear = $request->get('school_year', $this->settingsService->getCurrentSchoolYearString());

        $cacheKey = 'api_course_schedules_' . md5($request->getQueryString() ?? '');

        $result = Cache::remember($cacheKey, 300, function () use ($request, $courseCode, $academicYear, $semester, $schoolYear) {
            // Find the course by code
            $course = Course::where('code', $courseCode)->first();

            if (!$course) {
                return [
                    'data' => [],
                    'meta' => [
                        'current_page' => 1,
                        'per_page' => 100,
                        'total' => 0,
                        'last_page' => 1,
                        'from' => null,
                        'to' => null,
                    ],
                    'course_info' => [
                        'course_code' => $courseCode,
                        'course_title' => 'Course not found',
                        'academic_year' => $academicYear,
                        'semester' => $semester,
                    ],
                    'school_info' => [
                        'current_school_year' => $this->settingsService->getCurrentSchoolYearString(),
                        'current_semester' => $this->settingsService->getCurrentSemester(),
                        'semester_name' => $this->settingsService->getAvailableSemesters()[$this->settingsService->getCurrentSemester()] ?? '',
                    ],
                ];
            }

            // Build the query for classes
            $query = Classes::query()
                ->where('school_year', $schoolYear)
                ->where('semester', $semester)
                ->whereJsonContains('course_codes', (string) $course->id)
                ->with([
                    'Schedule.room',
                    'SubjectById',
                    'SubjectByCode',
                    'Faculty',
                    'Room'
                ]);

            // Filter by academic year if provided
            if ($academicYear) {
                $query->where('academic_year', $academicYear);
            }

            // Pagination
            $perPage = min((int) $request->get('per_page', 100), 500);
            $classes = $query->paginate($perPage);

            // Format the data for website display
            $formattedData = $classes->getCollection()->map(function ($class) {
                $subject = $class->SubjectById ?: $class->SubjectByCode;
                $schedules = $class->Schedule;

                // Group schedules by time and format
                $scheduleGroups = $this->groupSchedulesByTime($schedules);
                $formattedSchedule = $this->formatScheduleForDisplay($scheduleGroups);

                return [
                    'code' => $class->subject_code,
                    'title' => $subject?->title ?? 'N/A',
                    'schedule' => $formattedSchedule,
                    'room' => $class->Room?->name ?? 'N/A',
                    'section' => $class->section ?? 'N/A',
                    'faculty' => $class->Faculty?->full_name ?? 'N/A',
                    'units' => $subject?->units ?? 0,
                    'academic_year' => $class->academic_year,
                    'semester' => $class->semester,
                    'classification' => $class->classification,
                    'maximum_slots' => $class->maximum_slots,
                    'raw_schedules' => $schedules->map(function ($schedule) {
                        return [
                            'day_of_week' => $schedule->day_of_week,
                            'start_time' => $schedule->start_time?->format('H:i'),
                            'end_time' => $schedule->end_time?->format('H:i'),
                            'formatted_time_range' => $schedule->time_range,
                            'room' => $schedule->room?->name ?? 'N/A',
                        ];
                    })->toArray(),
                ];
            });

            return [
                'data' => $formattedData->toArray(),
                'meta' => [
                    'current_page' => $classes->currentPage(),
                    'per_page' => $classes->perPage(),
                    'total' => $classes->total(),
                    'last_page' => $classes->lastPage(),
                    'from' => $classes->firstItem(),
                    'to' => $classes->lastItem(),
                ],
                'course_info' => [
                    'course_code' => $course->code,
                    'course_title' => $course->title,
                    'course_department' => $course->department,
                    'academic_year' => $academicYear,
                    'semester' => $semester,
                ],
                'school_info' => [
                    'current_school_year' => $this->settingsService->getCurrentSchoolYearString(),
                    'current_semester' => $this->settingsService->getCurrentSemester(),
                    'semester_name' => $this->settingsService->getAvailableSemesters()[$this->settingsService->getCurrentSemester()] ?? '',
                ],
            ];
        });

        return response()->json($result);
    }

    /**
     * Group schedules by time range to combine same-time different-day schedules
     */
    private function groupSchedulesByTime($schedules): array
    {
        $groups = [];

        foreach ($schedules as $schedule) {
            $timeKey = $schedule->start_time?->format('H:i') . '-' . $schedule->end_time?->format('H:i');
            $roomKey = $schedule->room?->name ?? 'N/A';
            $groupKey = $timeKey . '|' . $roomKey;

            if (!isset($groups[$groupKey])) {
                $groups[$groupKey] = [
                    'start_time' => $schedule->start_time,
                    'end_time' => $schedule->end_time,
                    'room' => $roomKey,
                    'days' => [],
                ];
            }

            $groups[$groupKey]['days'][] = $schedule->day_of_week;
        }

        return $groups;
    }

    /**
     * Format schedule groups for display with day abbreviations
     */
    private function formatScheduleForDisplay(array $scheduleGroups): string
    {
        if (empty($scheduleGroups)) {
            return 'No schedule';
        }

        $formattedSchedules = [];

        foreach ($scheduleGroups as $group) {
            $timeRange = $group['start_time']?->format('h:i A') . ' - ' . $group['end_time']?->format('h:i A');
            $dayAbbreviations = $this->getDayAbbreviations($group['days']);

            $formattedSchedules[] = $timeRange . ' ' . $dayAbbreviations;
        }

        return implode(', ', $formattedSchedules);
    }

    /**
     * Convert day names to abbreviations (MWF, TTH, S, etc.)
     */
    private function getDayAbbreviations(array $days): string
    {
        $dayMap = [
            'Monday' => 'M',
            'Tuesday' => 'T',
            'Wednesday' => 'W',
            'Thursday' => 'TH',
            'Friday' => 'F',
            'Saturday' => 'S',
            'Sunday' => 'SU',
        ];

        // Sort days by week order
        $weekOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        $sortedDays = array_intersect($weekOrder, $days);

        // Convert to abbreviations
        $abbreviations = array_map(fn($day) => $dayMap[$day] ?? $day, $sortedDays);

        // Handle special cases for common patterns
        $abbrevString = implode('', $abbreviations);

        // Replace common patterns
        $patterns = [
            'MTWTHF' => 'MTWTHF',
            'MWTHF' => 'MWTHF',
            'MWF' => 'MWF',
            'TTH' => 'TTH',
            'MW' => 'MW',
            'TH' => 'TH',
            'S' => 'S',
            'SU' => 'SU',
        ];

        return $patterns[$abbrevString] ?? $abbrevString;
    }
}
