<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Classes
 */
class ClassResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'subject_code' => $this->subject_code,
            'subject_id' => $this->subject_id,
            'faculty_id' => $this->faculty_id,
            'academic_year' => $this->academic_year,
            'semester' => $this->semester,
            'school_year' => $this->school_year,
            'course_codes' => $this->course_codes,
            'section' => $this->section,
            'room_id' => $this->room_id,
            'classification' => $this->classification,
            'maximum_slots' => $this->maximum_slots,
            'grade_level' => $this->grade_level,
            'shs_track_id' => $this->shs_track_id,
            'shs_strand_id' => $this->shs_strand_id,
            
            // Computed attributes
            'faculty_full_name' => $this->faculty_full_name,
            'enrolled_count' => $this->whenCounted('class_enrollments'),
            'available_slots' => $this->maximum_slots ? ($this->maximum_slots - ($this->class_enrollments_count ?? 0)) : null,
            
            // Relationships
            'subject' => $this->whenLoaded('SubjectById', function () {
                return new SubjectResource($this->SubjectById);
            }) ?: $this->whenLoaded('SubjectByCode', function () {
                return new SubjectResource($this->SubjectByCode);
            }),
            
            'faculty' => $this->whenLoaded('Faculty', function () {
                return [
                    'id' => $this->Faculty->id,
                    'full_name' => $this->Faculty->full_name,
                    'email' => $this->Faculty->email,
                    'department' => $this->Faculty->department,
                ];
            }),
            
            'room' => $this->whenLoaded('Room', function () {
                return [
                    'id' => $this->Room->id,
                    'name' => $this->Room->name,
                    'class_code' => $this->Room->class_code,
                ];
            }),
            
            'schedules' => ScheduleResource::collection($this->whenLoaded('Schedule')),
            
            'enrolled_students' => StudentResource::collection($this->whenLoaded('enrollments.student')),
            
            'shs_track' => $this->whenLoaded('ShsTrack', function () {
                return [
                    'id' => $this->ShsTrack->id,
                    'name' => $this->ShsTrack->name ?? 'N/A',
                ];
            }),
            
            'shs_strand' => $this->whenLoaded('ShsStrand', function () {
                return [
                    'id' => $this->ShsStrand->id,
                    'name' => $this->ShsStrand->name ?? 'N/A',
                ];
            }),
        ];
    }
}
