<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Subject
 */
class SubjectResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'title' => $this->title,
            'classification' => $this->classification,
            'units' => $this->units,
            'lecture' => $this->lecture,
            'laboratory' => $this->laboratory,
            'pre_requisite' => $this->pre_riquisite,
            'academic_year' => $this->academic_year,
            'semester' => $this->semester,
            'group' => $this->group,
            'is_credited' => $this->is_credited,
            'course' => $this->whenLoaded('course', function () {
                return [
                    'id' => $this->course->id,
                    'code' => $this->course->code,
                    'title' => $this->course->title,
                    'department' => $this->course->department,
                ];
            }),
        ];
    }
}
