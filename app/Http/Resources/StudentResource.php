<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Student
 */
class StudentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'student_id' => $this->student_id,
            'full_name' => $this->full_name,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'middle_name' => $this->middle_name,
            'email' => $this->email,
            'gender' => $this->gender,
            'age' => $this->age,
            'course_id' => $this->course_id,
            'academic_year' => $this->academic_year,
            'status' => $this->status,
            'profile_url' => $this->profile_url,
            'student_picture' => $this->student_picture,
            'course' => $this->whenLoaded('course', function () {
                return [
                    'id' => $this->course->id,
                    'code' => $this->course->code,
                    'title' => $this->course->title,
                    'department' => $this->course->department,
                ];
            }),
        ];
    }
}
