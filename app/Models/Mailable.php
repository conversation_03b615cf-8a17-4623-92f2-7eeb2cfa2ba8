<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Mailable
 *
 * @property int $id
 * @property int $mail_id
 * @property string $mailable_type
 * @property int $mailable_id
 * @property Mail $mail
 */
final class Mailable extends Model
{
    public $timestamps = false;

    protected $table = 'mailables';

    protected $casts = [
        'mail_id' => 'int',
        'mailable_id' => 'int',
    ];

    protected $fillable = [
        'mail_id',
        'mailable_type',
        'mailable_id',
    ];

    public function mail()
    {
        return $this->belongsTo(Mail::class);
    }
}
