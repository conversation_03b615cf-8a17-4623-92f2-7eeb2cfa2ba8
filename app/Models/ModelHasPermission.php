<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ModelHasPermission
 *
 * @property float $permission_id
 * @property string $model_type
 * @property float $model_id
 */
final class ModelHasPermission extends Model
{
    public $incrementing = false;

    public $timestamps = false;

    protected $table = 'model_has_permissions';

    protected $casts = [
        'permission_id' => 'float',
        'model_id' => 'float',
    ];
}
