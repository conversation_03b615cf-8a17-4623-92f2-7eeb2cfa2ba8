<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class UptimeCheck
 *
 * @property int $id
 * @property string $look_for_string
 * @property string $status
 * @property string|null $check_failure_reason
 * @property int $check_times_failed_in_a_row
 * @property Carbon|null $status_last_change_date
 * @property Carbon|null $last_check_date
 * @property Carbon|null $check_failed_event_fired_on_date
 * @property int|null $request_duration_ms
 * @property string $check_method
 * @property string|null $check_payload
 * @property string|null $check_additional_headers
 * @property string|null $check_response_checker
 * @property int $site_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class UptimeCheck extends Model
{
    protected $table = 'uptime_checks';

    protected $casts = [
        'check_times_failed_in_a_row' => 'int',
        'status_last_change_date' => 'datetime',
        'last_check_date' => 'datetime',
        'check_failed_event_fired_on_date' => 'datetime',
        'request_duration_ms' => 'int',
        'site_id' => 'int',
    ];

    protected $fillable = [
        'look_for_string',
        'status',
        'check_failure_reason',
        'check_times_failed_in_a_row',
        'status_last_change_date',
        'last_check_date',
        'check_failed_event_fired_on_date',
        'request_duration_ms',
        'check_method',
        'check_payload',
        'check_additional_headers',
        'check_response_checker',
        'site_id',
    ];
}
