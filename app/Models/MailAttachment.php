<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class MailAttachment
 *
 * @property int $id
 * @property int $mail_id
 * @property string $disk
 * @property string $uuid
 * @property string $filename
 * @property string $mime
 * @property bool $inline
 * @property int $size
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Mail $mail
 */
final class MailAttachment extends Model
{
    protected $table = 'mail_attachments';

    protected $casts = [
        'mail_id' => 'int',
        'inline' => 'bool',
        'size' => 'int',
    ];

    protected $fillable = [
        'mail_id',
        'disk',
        'uuid',
        'filename',
        'mime',
        'inline',
        'size',
    ];

    public function mail()
    {
        return $this->belongsTo(Mail::class);
    }
}
