<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Team
 *
 * @property int $id
 * @property int $user_id
 * @property string $name
 * @property bool $personal_team
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Team extends Model
{
    protected $table = 'teams';

    protected $casts = [
        'user_id' => 'int',
        'personal_team' => 'bool',
    ];

    protected $fillable = [
        'user_id',
        'name',
        'personal_team',
    ];
}
