<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Setting
 *
 * @property int $id
 * @property string $group
 * @property string $name
 * @property bool $locked
 * @property string $payload
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Setting extends Model
{
    protected $table = 'settings';

    protected $casts = [
        'locked' => 'bool',
    ];

    protected $fillable = [
        'group',
        'name',
        'locked',
        'payload',
    ];
}
