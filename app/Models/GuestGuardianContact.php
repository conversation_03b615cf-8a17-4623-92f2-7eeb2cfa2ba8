<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class GuestGuardianContact
 *
 * @property int $id
 * @property string|null $emergencycontactname
 * @property int|null $emergencycontactphone
 * @property string|null $emergencycontactaddress
 */
final class GuestGuardianContact extends Model
{
    public $timestamps = false;

    protected $table = 'guest_guardian_contact';

    protected $casts = [
        'emergencycontactphone' => 'int',
    ];

    protected $fillable = [
        'emergencycontactname',
        'emergencycontactphone',
        'emergencycontactaddress',
    ];
}
