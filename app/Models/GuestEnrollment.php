<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class GuestEnrollment
 *
 * @property int $id
 * @property int|null $selected_course
 * @property int|null $academic_year
 * @property int|null $selected_semester
 * @property int|null $geust_education_id
 * @property string|null $special_skills
 * @property int|null $guest_parents_id
 * @property int|null $guest_guardian_id
 * @property int|null $guest_documents_id
 * @property int|null $guest_tuition_id
 * @property Carbon $created_at
 * @property Carbon|null $updated_at
 * @property int|null $student_id
 * @property string|null $guest_email
 * @property string|null $status
 * @property int|null $selected_subjects
 * @property string|null $deleted_at
 * @property string|null $type
 * @property int|null $guest_personal_info_id
 * @property int|null $downpayment
 * @property string|null $school_year
 * @property string|null $semester
 */
final class GuestEnrollment extends Model
{
    use SoftDeletes;

    protected $table = 'guest_enrollments';

    protected $casts = [
        'selected_course' => 'int',
        'academic_year' => 'int',
        'selected_semester' => 'int',
        'geust_education_id' => 'int',
        'guest_parents_id' => 'int',
        'guest_guardian_id' => 'int',
        'guest_documents_id' => 'int',
        'guest_tuition_id' => 'int',
        'student_id' => 'int',
        'selected_subjects' => 'int',
        'guest_personal_info_id' => 'int',
        'downpayment' => 'int',
    ];

    protected $fillable = [
        'selected_course',
        'academic_year',
        'selected_semester',
        'geust_education_id',
        'special_skills',
        'guest_parents_id',
        'guest_guardian_id',
        'guest_documents_id',
        'guest_tuition_id',
        'student_id',
        'guest_email',
        'status',
        'selected_subjects',
        'type',
        'guest_personal_info_id',
        'downpayment',
        'school_year',
        'semester',
    ];
}
