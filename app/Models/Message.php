<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Message
 *
 * @property int $id
 * @property float $user_id
 * @property float $ticket_id
 * @property string $message
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Message extends Model
{
    protected $table = 'messages';

    protected $casts = [
        'user_id' => 'float',
        'ticket_id' => 'float',
    ];

    protected $fillable = [
        'user_id',
        'ticket_id',
        'message',
    ];
}
