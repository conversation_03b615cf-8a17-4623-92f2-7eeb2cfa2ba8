<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FblogPostFblogTag
 *
 * @property int $id
 * @property int $post_id
 * @property int $tag_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property FblogPost $fblog_post
 * @property FblogTag $fblog_tag
 */
final class FblogPostFblogTag extends Model
{
    protected $table = 'fblog_post_fblog_tag';

    protected $casts = [
        'post_id' => 'int',
        'tag_id' => 'int',
    ];

    protected $fillable = [
        'post_id',
        'tag_id',
    ];

    public function fblog_post()
    {
        return $this->belongsTo(FblogPost::class, 'post_id');
    }

    public function fblog_tag()
    {
        return $this->belongsTo(FblogTag::class, 'tag_id');
    }
}
