<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AdminTransaction
 *
 * @property int $id
 * @property int $admin_id
 * @property int $transaction_id
 * @property string $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property User $user
 * @property Transaction $transaction
 */
final class AdminTransaction extends Model
{
    protected $table = 'admin_transactions';

    protected $casts = [
        'admin_id' => 'int',
        'transaction_id' => 'int',
    ];

    protected $fillable = [
        'admin_id',
        'transaction_id',
        'status',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }
}
