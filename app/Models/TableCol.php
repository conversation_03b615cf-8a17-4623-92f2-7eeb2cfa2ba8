<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class TableCol
 *
 * @property int $id
 * @property int $table_id
 * @property string $name
 * @property string|null $type
 * @property int|null $length
 * @property string|null $default
 * @property string|null $comment
 * @property string|null $foreign_table
 * @property string|null $foreign_col
 * @property string|null $foreign_model
 * @property bool|null $nullable
 * @property bool|null $index
 * @property bool|null $auto_increment
 * @property bool|null $primary
 * @property bool|null $unique
 * @property bool|null $unsigned
 * @property bool|null $foreign
 * @property bool|null $foreign_on_delete_cascade
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class TableCol extends Model
{
    protected $table = 'table_cols';

    protected $casts = [
        'table_id' => 'int',
        'length' => 'int',
        'nullable' => 'bool',
        'index' => 'bool',
        'auto_increment' => 'bool',
        'primary' => 'bool',
        'unique' => 'bool',
        'unsigned' => 'bool',
        'foreign' => 'bool',
        'foreign_on_delete_cascade' => 'bool',
    ];

    protected $fillable = [
        'table_id',
        'name',
        'type',
        'length',
        'default',
        'comment',
        'foreign_table',
        'foreign_col',
        'foreign_model',
        'nullable',
        'index',
        'auto_increment',
        'primary',
        'unique',
        'unsigned',
        'foreign',
        'foreign_on_delete_cascade',
    ];
}
