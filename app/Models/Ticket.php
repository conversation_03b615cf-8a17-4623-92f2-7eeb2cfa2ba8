<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Ticket
 *
 * @property int $id
 * @property string|null $uuid
 * @property float $user_id
 * @property string $title
 * @property string|null $message
 * @property string $priority
 * @property string $status
 * @property bool $is_resolved
 * @property bool $is_locked
 * @property float|null $assigned_to
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Ticket extends Model
{
    protected $table = 'tickets';

    protected $casts = [
        'user_id' => 'float',
        'is_resolved' => 'bool',
        'is_locked' => 'bool',
        'assigned_to' => 'float',
    ];

    protected $fillable = [
        'uuid',
        'user_id',
        'title',
        'message',
        'priority',
        'status',
        'is_resolved',
        'is_locked',
        'assigned_to',
    ];
}
