<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class DocumentLocation
 *
 * @property int $id
 * @property string|null $birth_certificate
 * @property string|null $form_138
 * @property string|null $form_137
 * @property string|null $good_moral_cert
 * @property string|null $transfer_credentials
 * @property string|null $transcript_records
 * @property string|null $picture_1x1
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class DocumentLocation extends Model
{
    public $timestamps = false;

    protected $table = 'document_locations';

    protected $fillable = [
        'birth_certificate',
        'form_138',
        'form_137',
        'good_moral_cert',
        'transfer_credentials',
        'transcript_records',
        'picture_1x1',
    ];
}
