<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FilamentEmailLog
 *
 * @property int $id
 * @property string|null $from
 * @property string|null $to
 * @property string|null $cc
 * @property string|null $bcc
 * @property string|null $subject
 * @property string|null $text_body
 * @property string|null $html_body
 * @property string|null $raw_body
 * @property string|null $sent_debug_info
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class FilamentEmailLog extends Model
{
    protected $table = 'filament_email_log';

    protected $fillable = [
        'from',
        'to',
        'cc',
        'bcc',
        'subject',
        'text_body',
        'html_body',
        'raw_body',
        'sent_debug_info',
    ];
}
