<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Seo
 *
 * @property int $id
 * @property string $model_type
 * @property float $model_id
 * @property string|null $description
 * @property string|null $title
 * @property string|null $image
 * @property string|null $author
 * @property string|null $robots
 * @property string|null $canonical_url
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Seo extends Model
{
    protected $table = 'seo';

    protected $casts = [
        'model_id' => 'float',
    ];

    protected $fillable = [
        'model_type',
        'model_id',
        'description',
        'title',
        'image',
        'author',
        'robots',
        'canonical_url',
    ];
}
