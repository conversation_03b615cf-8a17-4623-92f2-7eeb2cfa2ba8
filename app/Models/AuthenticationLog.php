<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AuthenticationLog
 *
 * @property int $id
 * @property string $authenticatable_type
 * @property float $authenticatable_id
 * @property string|null $ip_address
 * @property string|null $user_agent
 * @property Carbon|null $login_at
 * @property bool $login_successful
 * @property Carbon|null $logout_at
 * @property bool $cleared_by_user
 * @property string|null $location
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class AuthenticationLog extends Model
{
    protected $table = 'authentication_log';

    protected $casts = [
        'authenticatable_id' => 'float',
        'login_at' => 'datetime',
        'login_successful' => 'bool',
        'logout_at' => 'datetime',
        'cleared_by_user' => 'bool',
    ];

    protected $fillable = [
        'authenticatable_type',
        'authenticatable_id',
        'ip_address',
        'user_agent',
        'login_at',
        'login_successful',
        'logout_at',
        'cleared_by_user',
        'location',
    ];
}
