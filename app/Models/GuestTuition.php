<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class GuestTuition
 *
 * @property int $id
 * @property int|null $totaltuition
 * @property int|null $downpayment
 * @property int|null $totalbalance
 * @property string|null $payment_method
 * @property Carbon|null $due_date
 * @property string|null $invoice_uuid
 * @property int|null $total_lecture
 * @property int|null $total_laboratory
 * @property int|null $miscellaneous
 * @property int|null $overall_total
 * @property string|null $discount
 */
final class GuestTuition extends Model
{
    public $timestamps = false;

    protected $table = 'guest_tuition';

    protected $casts = [
        'totaltuition' => 'int',
        'downpayment' => 'int',
        'totalbalance' => 'int',
        'due_date' => 'datetime',
        'total_lecture' => 'int',
        'total_laboratory' => 'int',
        'miscellaneous' => 'int',
        'overall_total' => 'int',
    ];

    protected $fillable = [
        'totaltuition',
        'downpayment',
        'totalbalance',
        'payment_method',
        'due_date',
        'invoice_uuid',
        'total_lecture',
        'total_laboratory',
        'miscellaneous',
        'overall_total',
        'discount',
    ];
}
