<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ServerMetric
 *
 * @property int $id
 * @property int $cpu_load
 * @property int $memory_usage
 * @property string $disk_usage
 * @property int $site_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class ServerMetric extends Model
{
    protected $table = 'server_metrics';

    protected $casts = [
        'cpu_load' => 'int',
        'memory_usage' => 'int',
        'site_id' => 'int',
    ];

    protected $fillable = [
        'cpu_load',
        'memory_usage',
        'disk_usage',
        'site_id',
    ];
}
