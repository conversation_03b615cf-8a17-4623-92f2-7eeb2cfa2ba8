<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FblogCategory
 *
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Collection|FblogPost[] $fblog_posts
 */
final class FblogCategory extends Model
{
    protected $table = 'fblog_categories';

    protected $fillable = [
        'name',
        'slug',
    ];

    public function fblog_posts()
    {
        return $this->belongsToMany(FblogPost::class, 'fblog_category_fblog_post', 'category_id', 'post_id')
            ->withPivot('id')
            ->withTimestamps();
    }
}
