<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Attendance
 *
 * @property int $id
 * @property int $class_enrollment_id
 * @property int $student_id
 * @property Carbon $date
 * @property string $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Attendance extends Model
{
    protected $table = 'attendances';

    protected $casts = [
        'class_enrollment_id' => 'int',
        'student_id' => 'int',
        'date' => 'datetime',
    ];

    protected $fillable = [
        'class_enrollment_id',
        'student_id',
        'date',
        'status',
    ];
}
