<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class OldUserEmail
 *
 * @property int $id
 * @property string $user_type
 * @property float $user_id
 * @property string $email
 * @property string $token
 * @property Carbon|null $created_at
 */
final class OldUserEmail extends Model
{
    public $timestamps = false;

    protected $table = 'old_user_emails';

    protected $casts = [
        'user_id' => 'float',
    ];

    protected $hidden = [
        'token',
    ];

    protected $fillable = [
        'user_type',
        'user_id',
        'email',
        'token',
    ];
}
