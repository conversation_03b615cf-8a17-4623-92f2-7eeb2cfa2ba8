<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FblogSetting
 *
 * @property int $id
 * @property string|null $title
 * @property string|null $description
 * @property string|null $logo
 * @property string|null $favicon
 * @property string|null $organization_name
 * @property string|null $google_console_code
 * @property string|null $google_analytic_code
 * @property string|null $google_adsense_code
 * @property string|null $quick_links
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class FblogSetting extends Model
{
    protected $table = 'fblog_settings';

    protected $fillable = [
        'title',
        'description',
        'logo',
        'favicon',
        'organization_name',
        'google_console_code',
        'google_analytic_code',
        'google_adsense_code',
        'quick_links',
    ];
}
