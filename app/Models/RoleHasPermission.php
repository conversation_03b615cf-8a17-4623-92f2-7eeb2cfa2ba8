<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class RoleHasPermission
 *
 * @property float $permission_id
 * @property float $role_id
 */
final class RoleHasPermission extends Model
{
    public $incrementing = false;

    public $timestamps = false;

    protected $table = 'role_has_permissions';

    protected $casts = [
        'permission_id' => 'float',
        'role_id' => 'float',
    ];
}
