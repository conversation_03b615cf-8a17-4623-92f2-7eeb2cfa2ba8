<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class UserActivity
 *
 * @property int $id
 * @property int|null $user_id
 * @property string $url
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class UserActivity extends Model
{
    protected $table = 'user_activities';

    protected $casts = [
        'user_id' => 'int',
    ];

    protected $fillable = [
        'user_id',
        'url',
    ];
}
