<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Category
 *
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property bool $is_visible
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Category extends Model
{
    protected $table = 'categories';

    protected $casts = [
        'is_visible' => 'bool',
    ];

    protected $fillable = [
        'name',
        'slug',
        'is_visible',
    ];
}
