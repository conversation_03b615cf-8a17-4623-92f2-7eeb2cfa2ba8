<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class StudentParentsInfo
 *
 * @property int $id
 * @property string|null $fathers_name
 * @property string|null $mothers_name
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class StudentParentsInfo extends Model
{
    protected $table = 'student_parents_info';

    protected $fillable = [
        'fathers_name',
        'mothers_name',
    ];
}
