<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Room
 *
 * @property int $id
 * @property string $name
 * @property string $class_code
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Room extends Model
{
    protected $table = 'rooms';

    protected $fillable = [
        'name',
        'class_code',
    ];

    public function classes()
    {
        return $this->hasMany(Classes::class);
    }

    public function schedules()
    {
        return $this->hasMany(Schedule::class, 'room_id', 'id');
    }

    public function getSchedulesAttribute()
    {
        return $this->schedules()->get();
    }
}
