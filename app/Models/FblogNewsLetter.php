<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FblogNewsLetter
 *
 * @property int $id
 * @property string $email
 * @property bool $subscribed
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class FblogNewsLetter extends Model
{
    protected $table = 'fblog_news_letters';

    protected $casts = [
        'subscribed' => 'bool',
    ];

    protected $fillable = [
        'email',
        'subscribed',
    ];
}
