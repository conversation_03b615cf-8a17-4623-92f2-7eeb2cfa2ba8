<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class StudentTransaction
 *
 * @property int $id
 * @property int $student_id
 * @property int $transaction_id
 * @property string $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int|null $amount
 * @property Student $student
 * @property Transaction $transaction
 */
final class StudentTransaction extends Model
{
    protected $table = 'student_transactions';

    protected $fillable = [
        'student_id',
        'transaction_id',
        'amount',
        'status',
    ];

    protected $casts = [
        'student_id' => 'integer',
        'transaction_id' => 'integer',
        'amount' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class, 'transaction_id')
            ->withDefault();
    }
}
