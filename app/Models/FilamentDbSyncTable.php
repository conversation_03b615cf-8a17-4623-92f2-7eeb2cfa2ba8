<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FilamentDbSyncTable
 *
 * @property int $id
 * @property string|null $model
 * @property string|null $model_id
 * @property string|null $action
 * @property string|null $data
 * @property string $status
 * @property Carbon|null $completed_at
 * @property Carbon|null $failed_at
 * @property string|null $failed_reason
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class FilamentDbSyncTable extends Model
{
    protected $table = 'filament_db_sync_table';

    protected $casts = [
        'completed_at' => 'datetime',
        'failed_at' => 'datetime',
    ];

    protected $fillable = [
        'model',
        'model_id',
        'action',
        'data',
        'status',
        'completed_at',
        'failed_at',
        'failed_reason',
    ];
}
