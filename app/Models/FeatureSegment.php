<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FeatureSegment
 *
 * @property int $id
 * @property string $feature
 * @property string $scope
 * @property string $values
 * @property bool $active
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class FeatureSegment extends Model
{
    protected $table = 'feature_segments';

    protected $casts = [
        'active' => 'bool',
    ];

    protected $fillable = [
        'feature',
        'scope',
        'values',
        'active',
    ];
}
