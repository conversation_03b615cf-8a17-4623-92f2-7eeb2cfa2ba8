<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ModelHasRole
 *
 * @property float $role_id
 * @property string $model_type
 * @property float $model_id
 */
final class ModelHasRole extends Model
{
    public $incrementing = false;

    public $timestamps = false;

    protected $table = 'model_has_roles';

    protected $casts = [
        'role_id' => 'float',
        'model_id' => 'float',
    ];
}
