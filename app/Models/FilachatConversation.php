<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FilachatConversation
 *
 * @property int $id
 * @property float $senderable_id
 * @property string $senderable_type
 * @property float $receiverable_id
 * @property string $receiverable_type
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class FilachatConversation extends Model
{
    protected $table = 'filachat_conversations';

    protected $casts = [
        'senderable_id' => 'float',
        'receiverable_id' => 'float',
    ];

    protected $fillable = [
        'senderable_id',
        'senderable_type',
        'receiverable_id',
        'receiverable_type',
    ];
}
