<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Resource
 *
 * @property int $id
 * @property string $resourceable_type
 * @property int $resourceable_id
 * @property string $type
 * @property string $file_path
 * @property string $file_name
 * @property string|null $mime_type
 * @property string $disk
 * @property int|null $file_size
 * @property string|null $metadata
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Resource extends Model
{
    protected $table = 'resources';

    protected $casts = [
        'resourceable_id' => 'int',
        'file_size' => 'int',
        'metadata' => 'array',
    ];

    protected $fillable = [
        'resourceable_type',
        'resourceable_id',
        'type',
        'file_path',
        'file_name',
        'mime_type',
        'disk',
        'file_size',
        'metadata',
    ];
}
