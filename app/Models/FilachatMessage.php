<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class FilachatMessage
 *
 * @property int $id
 * @property float $filachat_conversation_id
 * @property string|null $message
 * @property string|null $attachments
 * @property string|null $original_attachment_file_names
 * @property string|null $reactions
 * @property bool $is_starred
 * @property string|null $metadata
 * @property float|null $reply_to_message_id
 * @property float $senderable_id
 * @property string $senderable_type
 * @property float $receiverable_id
 * @property string $receiverable_type
 * @property Carbon|null $last_read_at
 * @property Carbon|null $edited_at
 * @property Carbon|null $sender_deleted_at
 * @property Carbon|null $receiver_deleted_at
 * @property string|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class FilachatMessage extends Model
{
    use SoftDeletes;

    protected $table = 'filachat_messages';

    protected $casts = [
        'filachat_conversation_id' => 'float',
        'is_starred' => 'bool',
        'reply_to_message_id' => 'float',
        'senderable_id' => 'float',
        'receiverable_id' => 'float',
        'last_read_at' => 'datetime',
        'edited_at' => 'datetime',
        'sender_deleted_at' => 'datetime',
        'receiver_deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'filachat_conversation_id',
        'message',
        'attachments',
        'original_attachment_file_names',
        'reactions',
        'is_starred',
        'metadata',
        'reply_to_message_id',
        'senderable_id',
        'senderable_type',
        'receiverable_id',
        'receiverable_type',
        'last_read_at',
        'edited_at',
        'sender_deleted_at',
        'receiver_deleted_at',
    ];
}
