<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class PulseValue
 *
 * @property int $id
 * @property int $timestamp
 * @property string $type
 * @property string $key
 * @property string $key_hash
 * @property string $value
 */
final class PulseValue extends Model
{
    public $timestamps = false;

    protected $table = 'pulse_values';

    protected $casts = [
        'timestamp' => 'int',
    ];

    protected $fillable = [
        'timestamp',
        'type',
        'key',
        'key_hash',
        'value',
    ];
}
