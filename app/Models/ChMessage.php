<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ChMessage
 *
 * @property string $id
 * @property int $from_id
 * @property int $to_id
 * @property string|null $body
 * @property string|null $attachment
 * @property bool $seen
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class ChMessage extends Model
{
    public $incrementing = false;

    protected $table = 'ch_messages';

    protected $casts = [
        'from_id' => 'int',
        'to_id' => 'int',
        'seen' => 'bool',
    ];

    protected $fillable = [
        'from_id',
        'to_id',
        'body',
        'attachment',
        'seen',
    ];
}
