<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PrivateBetaInvitation
 *
 * @property int $id
 * @property string $email
 * @property string $status
 * @property string $access_code
 * @property Carbon|null $expire_at
 * @property int $num_requests
 * @property Carbon|null $last_access_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class PrivateBetaInvitation extends Model
{
    protected $table = 'private_beta_invitations';

    protected $casts = [
        'expire_at' => 'datetime',
        'num_requests' => 'int',
        'last_access_at' => 'datetime',
    ];

    protected $fillable = [
        'email',
        'status',
        'access_code',
        'expire_at',
        'num_requests',
        'last_access_at',
    ];
}
