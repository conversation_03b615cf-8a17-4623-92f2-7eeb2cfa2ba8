<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class BulkAction
 *
 * @property int $id
 * @property string|null $name
 * @property string $type
 * @property string $identifier
 * @property string $status
 * @property string $job
 * @property int|null $user_id
 * @property int|null $total_records
 * @property string|null $data
 * @property string|null $message
 * @property Carbon|null $dismissed_at
 * @property Carbon|null $started_at
 * @property Carbon|null $failed_at
 * @property Carbon|null $finished_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property User|null $user
 * @property Collection|BulkActionRecord[] $bulk_action_records
 */
final class BulkAction extends Model
{
    protected $table = 'bulk_actions';

    protected $casts = [
        'user_id' => 'int',
        'total_records' => 'int',
        'dismissed_at' => 'datetime',
        'started_at' => 'datetime',
        'failed_at' => 'datetime',
        'finished_at' => 'datetime',
    ];

    protected $fillable = [
        'name',
        'type',
        'identifier',
        'status',
        'job',
        'user_id',
        'total_records',
        'data',
        'message',
        'dismissed_at',
        'started_at',
        'failed_at',
        'finished_at',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function bulk_action_records()
    {
        return $this->hasMany(BulkActionRecord::class);
    }
}
