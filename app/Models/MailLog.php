<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class MailLog
 *
 * @property int $id
 * @property string|null $from
 * @property string|null $to
 * @property string|null $cc
 * @property string|null $bcc
 * @property string $subject
 * @property string $body
 * @property string|null $headers
 * @property string|null $attachments
 * @property string|null $message_id
 * @property string|null $status
 * @property string|null $data
 * @property Carbon|null $opened
 * @property Carbon|null $delivered
 * @property Carbon|null $complaint
 * @property Carbon|null $bounced
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class MailLog extends Model
{
    protected $table = 'mail_logs';

    protected $casts = [
        'opened' => 'datetime',
        'delivered' => 'datetime',
        'complaint' => 'datetime',
        'bounced' => 'datetime',
    ];

    protected $fillable = [
        'from',
        'to',
        'cc',
        'bcc',
        'subject',
        'body',
        'headers',
        'attachments',
        'message_id',
        'status',
        'data',
        'opened',
        'delivered',
        'complaint',
        'bounced',
    ];
}
