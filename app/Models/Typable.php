<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Typable
 *
 * @property float $type_id
 * @property float $typables_id
 * @property string $typables_type
 */
final class Typable extends Model
{
    public $incrementing = false;

    public $timestamps = false;

    protected $table = 'typables';

    protected $casts = [
        'type_id' => 'float',
        'typables_id' => 'float',
    ];

    protected $fillable = [
        'type_id',
        'typables_id',
        'typables_type',
    ];
}
