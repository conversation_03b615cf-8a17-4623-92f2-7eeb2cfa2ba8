<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class LabelTicket
 *
 * @property float $label_id
 * @property float $ticket_id
 */
final class LabelTicket extends Model
{
    public $incrementing = false;

    public $timestamps = false;

    protected $table = 'label_ticket';

    protected $casts = [
        'label_id' => 'float',
        'ticket_id' => 'float',
    ];

    protected $fillable = [
        'label_id',
        'ticket_id',
    ];
}
