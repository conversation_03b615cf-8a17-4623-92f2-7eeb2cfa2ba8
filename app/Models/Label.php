<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Label
 *
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property bool $is_visible
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Label extends Model
{
    protected $table = 'labels';

    protected $casts = [
        'is_visible' => 'bool',
    ];

    protected $fillable = [
        'name',
        'slug',
        'is_visible',
    ];
}
