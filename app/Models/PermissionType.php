<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class PermissionType
 *
 * @property int $id
 * @property int|null $permission_array
 */
final class PermissionType extends Model
{
    public $timestamps = false;

    protected $table = 'permission_type';

    protected $casts = [
        'permission_array' => 'int',
    ];

    protected $fillable = [
        'permission_array',
    ];
}
