<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class PulseEntry
 *
 * @property int $id
 * @property int $timestamp
 * @property string $type
 * @property string $key
 * @property string $key_hash
 * @property int|null $value
 */
final class PulseEntry extends Model
{
    public $timestamps = false;

    protected $table = 'pulse_entries';

    protected $casts = [
        'timestamp' => 'int',
        'value' => 'int',
    ];

    protected $fillable = [
        'timestamp',
        'type',
        'key',
        'key_hash',
        'value',
    ];
}
