<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ClassPost
 *
 * @property int $id
 * @property int $user_id
 * @property int $class_id
 * @property string $content
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property User $user
 * @property Class $class
 */
final class ClassPost extends Model
{
    protected $table = 'class_posts';

    protected $casts = [
        'user_id' => 'int',
        'class_id' => 'int',
    ];

    protected $fillable = [
        'user_id',
        'class_id',
        'content',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function class()
    {
        return $this->belongsTo(Classes::class);
    }
}
