<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class RouteStatistic
 *
 * @property int $id
 * @property int|null $user_id
 * @property int|null $team_id
 * @property string|null $method
 * @property string|null $route
 * @property int|null $status
 * @property string|null $ip
 * @property Carbon $date
 * @property int $counter
 * @property User|null $user
 */
final class RouteStatistic extends Model
{
    public $timestamps = false;

    protected $table = 'route_statistics';

    protected $casts = [
        'user_id' => 'int',
        'team_id' => 'int',
        'status' => 'int',
        'date' => 'datetime',
        'counter' => 'int',
    ];

    protected $fillable = [
        'user_id',
        'team_id',
        'method',
        'route',
        'status',
        'ip',
        'date',
        'counter',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
