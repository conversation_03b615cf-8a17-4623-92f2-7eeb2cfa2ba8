<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class StudentContact
 *
 * @property int $id
 * @property string|null $emergency_contact_name
 * @property string|null $emergency_contact_phone
 * @property string|null $emergency_contact_address
 * @property string|null $facebook_contact
 * @property string|null $personal_contact
 */
final class StudentContact extends Model
{
    public $timestamps = false;

    protected $table = 'student_contacts';

    protected $fillable = [
        'emergency_contact_name',
        'emergency_contact_phone',
        'emergency_contact_address',
        'facebook_contact',
        'personal_contact',
    ];
}
