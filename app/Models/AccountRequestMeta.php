<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AccountRequestMeta
 *
 * @property int $id
 * @property int|null $user_id
 * @property float|null $model_id
 * @property string|null $model_type
 * @property float $account_request_id
 * @property string $key
 * @property string|null $value
 * @property bool|null $is_approved
 * @property Carbon|null $is_approved_at
 * @property bool|null $is_rejected
 * @property Carbon|null $is_rejected_at
 * @property string|null $rejected_reason
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class AccountRequestMeta extends Model
{
    protected $table = 'account_request_metas';

    protected $casts = [
        'user_id' => 'int',
        'model_id' => 'float',
        'account_request_id' => 'float',
        'is_approved' => 'bool',
        'is_approved_at' => 'datetime',
        'is_rejected' => 'bool',
        'is_rejected_at' => 'datetime',
    ];

    protected $fillable = [
        'user_id',
        'model_id',
        'model_type',
        'account_request_id',
        'key',
        'value',
        'is_approved',
        'is_approved_at',
        'is_rejected',
        'is_rejected_at',
        'rejected_reason',
    ];
}
