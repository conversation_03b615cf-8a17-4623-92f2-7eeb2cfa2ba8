<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class StrandSubject
 *
 * @property int $id
 * @property string $code
 * @property string $title
 * @property string|null $description
 * @property string $grade_year
 * @property string $semester
 * @property int $strand_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class StrandSubject extends Model
{
    protected $table = 'strand_subjects';

    protected $casts = [
        'strand_id' => 'int',
    ];

    protected $fillable = [
        'code',
        'title',
        'description',
        'grade_year',
        'semester',
        'strand_id',
    ];

    /**
     * Get the strand that this subject belongs to.
     */
    public function strand()
    {
        return $this->belongsTo(ShsStrand::class, 'strand_id');
    }
}
