<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Feedback
 *
 * @property int $id
 * @property string $type
 * @property string $message
 * @property string|null $user_info
 * @property bool $reviewed
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Feedback extends Model
{
    protected $table = 'feedbacks';

    protected $casts = [
        'reviewed' => 'bool',
    ];

    protected $fillable = [
        'type',
        'message',
        'user_info',
        'reviewed',
    ];
}
