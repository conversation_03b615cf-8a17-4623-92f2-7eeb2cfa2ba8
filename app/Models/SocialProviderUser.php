<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class SocialProviderUser
 *
 * @property int $user_id
 * @property string $provider_slug
 * @property string $provider_user_id
 * @property string|null $nickname
 * @property string|null $name
 * @property string|null $email
 * @property string|null $avatar
 * @property string|null $provider_data
 * @property string $token
 * @property string|null $refresh_token
 * @property Carbon|null $token_expires_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class SocialProviderUser extends Model
{
    public $incrementing = false;

    protected $table = 'social_provider_user';

    protected $casts = [
        'user_id' => 'int',
        'token_expires_at' => 'datetime',
    ];

    protected $hidden = [
        'token',
        'refresh_token',
    ];

    protected $fillable = [
        'provider_user_id',
        'nickname',
        'name',
        'email',
        'avatar',
        'provider_data',
        'token',
        'refresh_token',
        'token_expires_at',
    ];
}
