<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FblogSeoDetail
 *
 * @property int $id
 * @property int $post_id
 * @property string $title
 * @property string|null $keywords
 * @property string $description
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property FblogPost $fblog_post
 */
final class FblogSeoDetail extends Model
{
    protected $table = 'fblog_seo_details';

    protected $casts = [
        'post_id' => 'int',
    ];

    protected $fillable = [
        'post_id',
        'title',
        'keywords',
        'description',
    ];

    public function fblog_post()
    {
        return $this->belongsTo(FblogPost::class, 'post_id');
    }
}
