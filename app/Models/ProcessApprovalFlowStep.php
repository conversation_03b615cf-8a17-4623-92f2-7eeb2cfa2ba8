<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ProcessApprovalFlowStep
 *
 * @property int $id
 * @property float $process_approval_flow_id
 * @property float $role_id
 * @property string|null $permissions
 * @property int|null $order
 * @property USER-DEFINED $action
 * @property int $active
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class ProcessApprovalFlowStep extends Model
{
    protected $table = 'process_approval_flow_steps';

    protected $casts = [
        'process_approval_flow_id' => 'float',
        'role_id' => 'float',
        'order' => 'int',
        'action' => 'USER-DEFINED',
        'active' => 'int',
    ];

    protected $fillable = [
        'process_approval_flow_id',
        'role_id',
        'permissions',
        'order',
        'action',
        'active',
    ];
}
