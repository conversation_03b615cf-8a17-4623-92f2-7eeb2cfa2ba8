<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FblogPost
 *
 * @property int $id
 * @property string $title
 * @property string $slug
 * @property string|null $sub_title
 * @property string $body
 * @property USER-DEFINED $status
 * @property Carbon|null $published_at
 * @property Carbon|null $scheduled_for
 * @property string $cover_photo_path
 * @property string $photo_alt_text
 * @property int $user_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property User $user
 * @property Collection|FblogTag[] $fblog_tags
 * @property Collection|FblogComment[] $fblog_comments
 * @property Collection|FblogSeoDetail[] $fblog_seo_details
 * @property Collection|FblogCategory[] $fblog_categories
 */
final class FblogPost extends Model
{
    protected $table = 'fblog_posts';

    protected $casts = [
        'status' => 'USER-DEFINED',
        'published_at' => 'datetime',
        'scheduled_for' => 'datetime',
        'user_id' => 'int',
    ];

    protected $fillable = [
        'title',
        'slug',
        'sub_title',
        'body',
        'status',
        'published_at',
        'scheduled_for',
        'cover_photo_path',
        'photo_alt_text',
        'user_id',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function fblog_tags()
    {
        return $this->belongsToMany(FblogTag::class, 'fblog_post_fblog_tag', 'post_id', 'tag_id')
            ->withPivot('id')
            ->withTimestamps();
    }

    public function fblog_comments()
    {
        return $this->hasMany(FblogComment::class, 'post_id');
    }

    public function fblog_seo_details()
    {
        return $this->hasMany(FblogSeoDetail::class, 'post_id');
    }

    public function fblog_categories()
    {
        return $this->belongsToMany(FblogCategory::class, 'fblog_category_fblog_post', 'post_id', 'category_id')
            ->withPivot('id')
            ->withTimestamps();
    }
}
