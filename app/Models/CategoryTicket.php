<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class CategoryTicket
 *
 * @property float $category_id
 * @property float $ticket_id
 */
final class CategoryTicket extends Model
{
    public $incrementing = false;

    public $timestamps = false;

    protected $table = 'category_ticket';

    protected $casts = [
        'category_id' => 'float',
        'ticket_id' => 'float',
    ];

    protected $fillable = [
        'category_id',
        'ticket_id',
    ];
}
