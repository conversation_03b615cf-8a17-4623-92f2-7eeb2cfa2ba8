<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class SslCertificateCheck
 *
 * @property int $id
 * @property string $status
 * @property string|null $issuer
 * @property Carbon|null $expiration_date
 * @property string|null $check_failure_reason
 * @property int $site_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class SslCertificateCheck extends Model
{
    protected $table = 'ssl_certificate_checks';

    protected $casts = [
        'expiration_date' => 'datetime',
        'site_id' => 'int',
    ];

    protected $fillable = [
        'status',
        'issuer',
        'expiration_date',
        'check_failure_reason',
        'site_id',
    ];
}
