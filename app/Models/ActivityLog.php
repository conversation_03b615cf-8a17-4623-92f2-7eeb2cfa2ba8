<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ActivityLog
 *
 * @property int $id
 * @property string|null $log_name
 * @property string $description
 * @property string|null $subject_type
 * @property string|null $event
 * @property float|null $subject_id
 * @property string|null $causer_type
 * @property float|null $causer_id
 * @property string|null $properties
 * @property int|null $batch_uuid
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class ActivityLog extends Model
{
    protected $table = 'activity_log';

    protected $casts = [
        'subject_id' => 'float',
        'causer_id' => 'float',
        'batch_uuid' => 'int',
    ];

    protected $fillable = [
        'log_name',
        'description',
        'subject_type',
        'event',
        'subject_id',
        'causer_type',
        'causer_id',
        'properties',
        'batch_uuid',
    ];
}
