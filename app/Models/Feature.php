<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Feature
 *
 * @property int $id
 * @property string $name
 * @property string $scope
 * @property string $value
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Feature extends Model
{
    protected $table = 'features';

    protected $fillable = [
        'name',
        'scope',
        'value',
    ];
}
