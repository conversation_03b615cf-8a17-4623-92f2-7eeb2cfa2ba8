<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Announcement
 *
 * @property int $id
 * @property string $title
 * @property string $content
 * @property string $slug
 * @property string $status
 * @property int $user_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property User $user
 */
final class Announcement extends Model
{
    protected $table = 'announcements';

    protected $casts = [
        'user_id' => 'int',
    ];

    protected $fillable = [
        'title',
        'content',
        'slug',
        'status',
        'user_id',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
