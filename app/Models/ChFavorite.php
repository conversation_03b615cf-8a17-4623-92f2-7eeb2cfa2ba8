<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ChFavorite
 *
 * @property string $id
 * @property int $user_id
 * @property int $favorite_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class ChFavorite extends Model
{
    public $incrementing = false;

    protected $table = 'ch_favorites';

    protected $casts = [
        'user_id' => 'int',
        'favorite_id' => 'int',
    ];

    protected $fillable = [
        'user_id',
        'favorite_id',
    ];
}
