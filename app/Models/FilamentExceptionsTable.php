<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FilamentExceptionsTable
 *
 * @property int $id
 * @property string $type
 * @property string $code
 * @property string $message
 * @property string $file
 * @property int $line
 * @property string $trace
 * @property string $method
 * @property string $path
 * @property string $query
 * @property string $body
 * @property string $cookies
 * @property string $headers
 * @property string $ip
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class FilamentExceptionsTable extends Model
{
    protected $table = 'filament_exceptions_table';

    protected $casts = [
        'line' => 'int',
    ];

    protected $fillable = [
        'type',
        'code',
        'message',
        'file',
        'line',
        'trace',
        'method',
        'path',
        'query',
        'body',
        'cookies',
        'headers',
        'ip',
    ];
}
