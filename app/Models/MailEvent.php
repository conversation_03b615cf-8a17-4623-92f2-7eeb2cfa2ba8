<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class MailEvent
 *
 * @property int $id
 * @property int $mail_id
 * @property string $type
 * @property string|null $ip_address
 * @property string|null $hostname
 * @property string|null $platform
 * @property string|null $os
 * @property string|null $browser
 * @property string|null $user_agent
 * @property string|null $city
 * @property string|null $country_code
 * @property string|null $link
 * @property string|null $tag
 * @property string|null $payload
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $occurred_at
 * @property Mail $mail
 */
final class MailEvent extends Model
{
    protected $table = 'mail_events';

    protected $casts = [
        'mail_id' => 'int',
        'occurred_at' => 'datetime',
    ];

    protected $fillable = [
        'mail_id',
        'type',
        'ip_address',
        'hostname',
        'platform',
        'os',
        'browser',
        'user_agent',
        'city',
        'country_code',
        'link',
        'tag',
        'payload',
        'occurred_at',
    ];

    public function mail()
    {
        return $this->belongsTo(Mail::class);
    }
}
