<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AccountRequest
 *
 * @property int $id
 * @property float $account_id
 * @property int|null $user_id
 * @property string|null $type
 * @property string|null $status
 * @property bool|null $is_approved
 * @property Carbon|null $is_approved_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class AccountRequest extends Model
{
    protected $table = 'account_requests';

    protected $casts = [
        'account_id' => 'float',
        'user_id' => 'int',
        'is_approved' => 'bool',
        'is_approved_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'user_id',
        'type',
        'status',
        'is_approved',
        'is_approved_at',
    ];
}
