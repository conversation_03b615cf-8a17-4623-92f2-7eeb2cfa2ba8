<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AuthenticatorApp
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $name
 * @property string|null $secret
 * @property Carbon|null $last_used_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property User $user
 */
final class AuthenticatorApp extends Model
{
    protected $table = 'authenticator_apps';

    protected $casts = [
        'user_id' => 'int',
        'last_used_at' => 'datetime',
    ];

    protected $hidden = [
        'secret',
    ];

    protected $fillable = [
        'user_id',
        'name',
        'secret',
        'last_used_at',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
