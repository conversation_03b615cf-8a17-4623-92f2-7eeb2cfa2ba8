<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Jobs\SendAssessmentNotificationJob;
use App\Models\StudentEnrollment;
use App\Services\BrowsershotService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Queue;
use ReflectionClass;

final class DebugAssessmentPdfCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:assessment-pdf {enrollment_id? : The enrollment ID to test} {--queue : Run via queue instead of sync} {--no-cleanup : Keep generated files for inspection}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug assessment PDF generation to replicate production issues';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Assessment PDF Generation Debug Tool');
        $this->line('=====================================');
        $this->newLine();

        $enrollmentId = $this->argument('enrollment_id');
        $useQueue = $this->option('queue');
        $noCleanup = $this->option('no-cleanup');

        // Step 1: Environment Check
        $this->checkEnvironment();

        // Step 2: Path Detection
        $this->checkPathDetection();

        // Step 3: Test with real or dummy data
        if ($enrollmentId) {
            $this->testWithRealEnrollment($enrollmentId, $useQueue, $noCleanup);
        } else {
            $this->testWithDummyData($useQueue, $noCleanup);
        }
    }

    /**
     * Check environment configuration
     */
    private function checkEnvironment(): void
    {
        $this->info('1. Environment Configuration Check');
        $this->line('----------------------------------');

        $critical = [
            'CHROME_PATH' => env('CHROME_PATH'),
            'NODE_BINARY_PATH' => env('NODE_BINARY_PATH'),
            'NPM_BINARY_PATH' => env('NPM_BINARY_PATH'),
        ];

        $optional = [
            'BROWSERSHOT_NO_SANDBOX' => env('BROWSERSHOT_NO_SANDBOX', 'default'),
            'BROWSERSHOT_TIMEOUT' => env('BROWSERSHOT_TIMEOUT', 'default'),
            'BROWSERSHOT_TEMP_DIRECTORY' => env('BROWSERSHOT_TEMP_DIRECTORY', 'default'),
            'QUEUE_CONNECTION' => env('QUEUE_CONNECTION', 'default'),
        ];

        $this->line('Critical Environment Variables:');
        foreach ($critical as $key => $value) {
            $status = $value ? '✅' : '❌';
            $displayValue = $value ?: 'NOT SET';
            $this->line("  {$key}: {$status} {$displayValue}");
        }

        $this->newLine();
        $this->line('Optional Environment Variables:');
        foreach ($optional as $key => $value) {
            $status = $value !== 'default' ? '✅' : '⚠️';
            $displayValue = $value !== 'default' ? $value : 'Using default';
            $this->line("  {$key}: {$status} {$displayValue}");
        }

        $this->newLine();
    }

    /**
     * Check path detection
     */
    private function checkPathDetection(): void
    {
        $this->info('2. Binary Path Detection');
        $this->line('------------------------');

        try {
            $config = config('browsershot', []);

            // Use reflection to access private methods
            $reflection = new ReflectionClass(BrowsershotService::class);

            $detectChrome = $reflection->getMethod('detectChromePath');
            $detectChrome->setAccessible(true);
            $chromePath = $detectChrome->invoke(null, $config);

            $detectNode = $reflection->getMethod('detectNodePath');
            $detectNode->setAccessible(true);
            $nodePath = $detectNode->invoke(null, $config);

            $detectNpm = $reflection->getMethod('detectNpmPath');
            $detectNpm->setAccessible(true);
            $npmPath = $detectNpm->invoke(null, $config);

            $this->line('Detected Paths:');
            $this->line('  Chrome: '.($chromePath ?: 'NOT FOUND'));
            $this->line('  Node: '.($nodePath ?: 'NOT FOUND'));
            $this->line('  NPM: '.($npmPath ?: 'NOT FOUND'));

            // Test each binary
            $this->newLine();
            $this->line('Binary Testing:');

            if ($chromePath) {
                $this->testBinary('Chrome', $chromePath, '--version');
            }

            if ($nodePath) {
                $this->testBinary('Node', $nodePath, '--version');
            }

            if ($npmPath) {
                $this->testBinary('NPM', $npmPath, '--version');
            }

        } catch (Exception $e) {
            $this->error("Path detection failed: {$e->getMessage()}");
        }

        $this->newLine();
    }

    /**
     * Test a binary
     */
    private function testBinary(string $name, $path, string $versionFlag): void
    {
        try {
            $version = mb_trim(shell_exec("{$path} {$versionFlag} 2>/dev/null") ?: '');
            if ($version !== '' && $version !== '0') {
                $this->line("  {$name}: ✅ {$version}");
            } else {
                $this->line("  {$name}: ❌ No version output");
            }
        } catch (Exception $e) {
            $this->line("  {$name}: ❌ Error: {$e->getMessage()}");
        }
    }

    /**
     * Test with real enrollment
     */
    private function testWithRealEnrollment($enrollmentId, $useQueue, $noCleanup): void
    {
        $this->info("3. Testing with Real Enrollment (ID: {$enrollmentId})");
        $this->line('---------------------------------------------------');

        try {
            $enrollment = StudentEnrollment::findOrFail($enrollmentId);

            $this->line('Found enrollment:');
            $this->line("  ID: {$enrollment->id}");
            $this->line("  Student: {$enrollment->student->first_name} {$enrollment->student->last_name}");
            $this->line("  Email: {$enrollment->student->email}");

            if ($useQueue) {
                $this->testViaQueue($enrollment);
            } else {
                $this->testViaSyncExecution($enrollment, $noCleanup);
            }

        } catch (Exception $e) {
            $this->error("Failed to find enrollment: {$e->getMessage()}");
        }
    }

    /**
     * Test with dummy data
     */
    private function testWithDummyData($useQueue, $noCleanup): void
    {
        $this->info('3. Testing with Dummy Data');
        $this->line('---------------------------');

        if ($useQueue) {
            $this->warn('Queue testing with dummy data not implemented. Use a real enrollment ID.');
        } else {
            $this->generateDummyPdf($noCleanup);
        }
    }

    /**
     * Test via queue
     */
    private function testViaQueue($enrollment): void
    {
        $this->line('Dispatching job to queue...');

        $jobId = uniqid('debug_assessment_', true);
        $job = new SendAssessmentNotificationJob($enrollment, $jobId);

        Queue::push($job);

        $this->info("Job dispatched with ID: {$jobId}");
        $this->newLine();
        $this->info('Monitor the job execution:');
        $this->line('1. Check queue worker logs');
        $this->line('2. Check application logs: tail -f storage/logs/laravel.log | grep "'.$jobId.'"');
        $this->line('3. Check supervisor logs if using supervisor');
        $this->newLine();
        $this->warn('Make sure your queue worker is running:');
        $this->line('php artisan queue:work --timeout=300');
    }

    /**
     * Test via synchronous execution
     */
    private function testViaSyncExecution($enrollment, $noCleanup): void
    {
        $this->line('Running synchronous PDF generation test...');

        try {
            // Replicate the exact job logic
            $generalSettings = \App\Models\GeneralSetting::first();

            $data = [
                'student' => $enrollment,
                'subjects' => $enrollment->SubjectsEnrolled,
                'school_year' => mb_convert_encoding(
                    $generalSettings->getSchoolYearString() ?? '',
                    'UTF-8',
                    'auto'
                ),
                'semester' => mb_convert_encoding(
                    $generalSettings->getSemester() ?? '',
                    'UTF-8',
                    'auto'
                ),
                'tuition' => $enrollment->studentTuition,
            ];

            // Generate filename like the job does
            $randomChars = mb_substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 10);
            $assessmentFilename = "debug-assmt-{$enrollment->id}-{$randomChars}.pdf";

            // Ensure directories exist
            $privateDir = storage_path('app/private');
            if (! \Illuminate\Support\Facades\File::exists($privateDir)) {
                \Illuminate\Support\Facades\File::makeDirectory($privateDir, 0755, true);
                $this->line('Created private directory');
            }

            $assessmentPath = $privateDir.DIRECTORY_SEPARATOR.$assessmentFilename;

            $this->line("Output path: {$assessmentPath}");

            // Render HTML
            $this->line('Rendering HTML view...');
            $html = view('pdf.assesment-form', $data)->render();
            $this->line('HTML length: '.number_format(mb_strlen($html)).' characters');

            // Generate PDF with same options as job
            $pdfOptions = [
                'format' => 'A4',
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
                'print_background' => true,
                'landscape' => true,
                'wait_until_network_idle' => true,
                'timeout' => 120,
            ];

            $this->line('Generating PDF with BrowsershotService...');
            $this->line('Options: '.json_encode($pdfOptions, JSON_PRETTY_PRINT));

            $success = BrowsershotService::generatePdf($html, $assessmentPath, $pdfOptions);

            if ($success && file_exists($assessmentPath)) {
                $fileSize = filesize($assessmentPath);
                $this->info('✅ PDF generated successfully!');
                $this->line("  Path: {$assessmentPath}");
                $this->line('  Size: '.number_format($fileSize).' bytes');

                if (! $noCleanup) {
                    unlink($assessmentPath);
                    $this->line('  Test file cleaned up');
                } else {
                    $this->line('  File kept for inspection (--no-cleanup flag used)');
                }
            } else {
                $this->error('❌ PDF generation failed');
                $this->line('  Success flag: '.($success ? 'true' : 'false'));
                $this->line('  File exists: '.(file_exists($assessmentPath) ? 'true' : 'false'));

                if (file_exists($assessmentPath)) {
                    $size = filesize($assessmentPath);
                    $this->line('  File size: '.number_format($size).' bytes');
                }
            }

        } catch (Exception $e) {
            $this->error('❌ Exception during PDF generation:');
            $this->line("  Error: {$e->getMessage()}");
            $this->line("  File: {$e->getFile()}");
            $this->line("  Line: {$e->getLine()}");

            if ($this->getOutput()->isVerbose()) {
                $this->line("  Trace: {$e->getTraceAsString()}");
            }
        }
    }

    /**
     * Generate dummy PDF
     */
    private function generateDummyPdf($noCleanup): void
    {
        $this->line('Generating dummy assessment PDF...');

        try {
            $html = '
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>Debug Assessment</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .info { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
                        .debug { background: #fff3cd; padding: 10px; margin: 10px 0; border-left: 4px solid #ffc107; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>DEBUG: Assessment Form</h1>
                        <p>Generated: '.now()->format('Y-m-d H:i:s').'</p>
                    </div>

                    <div class="info">
                        <h2>Environment Information</h2>
                        <p><strong>Chrome Path:</strong> '.(env('CHROME_PATH') ?: 'NOT SET').'</p>
                        <p><strong>Node Path:</strong> '.(env('NODE_BINARY_PATH') ?: 'NOT SET').'</p>
                        <p><strong>NPM Path:</strong> '.(env('NPM_BINARY_PATH') ?: 'NOT SET').'</p>
                        <p><strong>Process ID:</strong> '.getmypid().'</p>
                        <p><strong>User:</strong> '.get_current_user().'</p>
                        <p><strong>Working Directory:</strong> '.getcwd().'</p>
                    </div>

                    <div class="debug">
                        <h2>Test Student Information</h2>
                        <p><strong>Name:</strong> Debug Test Student</p>
                        <p><strong>Student ID:</strong> DEBUG-001</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>School Year:</strong> 2024-2025</p>
                        <p><strong>Semester:</strong> 1st Semester</p>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>Subject Code</th>
                                <th>Subject Name</th>
                                <th>Units</th>
                                <th>Schedule</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>DEBUG-101</td>
                                <td>Debug Subject 1</td>
                                <td>3</td>
                                <td>MWF 9:00-10:00 AM</td>
                            </tr>
                            <tr>
                                <td>DEBUG-102</td>
                                <td>Debug Subject 2</td>
                                <td>3</td>
                                <td>TTH 2:00-3:30 PM</td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="info">
                        <h2>Generation Details</h2>
                        <p>This PDF was generated to debug assessment notification issues.</p>
                        <p>If you can see this PDF, the browsershot service is working correctly.</p>
                    </div>
                </body>
                </html>
            ';

            $testFilename = 'debug-dummy-assessment-'.uniqid().'.pdf';
            $testPath = storage_path("app/{$testFilename}");

            $pdfOptions = [
                'format' => 'A4',
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
                'print_background' => true,
                'landscape' => true,
                'wait_until_network_idle' => true,
                'timeout' => 120,
            ];

            $this->line("Output path: {$testPath}");
            $this->line('HTML length: '.number_format(mb_strlen($html)).' characters');

            $success = BrowsershotService::generatePdf($html, $testPath, $pdfOptions);

            if ($success && file_exists($testPath)) {
                $fileSize = filesize($testPath);
                $this->info('✅ Dummy PDF generated successfully!');
                $this->line("  Path: {$testPath}");
                $this->line('  Size: '.number_format($fileSize).' bytes');

                if (! $noCleanup) {
                    unlink($testPath);
                    $this->line('  Test file cleaned up');
                } else {
                    $this->line('  File kept for inspection (--no-cleanup flag used)');
                }
            } else {
                $this->error('❌ Dummy PDF generation failed');
            }

        } catch (Exception $e) {
            $this->error('❌ Exception during dummy PDF generation:');
            $this->line("  Error: {$e->getMessage()}");
        }
    }
}
