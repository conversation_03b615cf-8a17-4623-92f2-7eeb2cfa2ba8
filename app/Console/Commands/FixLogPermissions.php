<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

final class FixLogPermissions extends Command
{
    protected $signature = 'fix:log-permissions';

    protected $description = 'Fix Laravel log file permissions immediately';

    public function handle(): int
    {
        $this->info('🔧 Fixing Laravel log permissions...');

        $logDir = storage_path('logs');
        $logFile = storage_path('logs/laravel.log');

        try {
            // Ensure logs directory exists
            if (! is_dir($logDir)) {
                mkdir($logDir, 0775, true);
                $this->info("✅ Created logs directory: {$logDir}");
            }

            // Set directory permissions
            chmod($logDir, 0775);
            $this->info('✅ Set logs directory permissions: 775');

            // Handle the log file
            if (! file_exists($logFile)) {
                // Create the log file
                touch($logFile);
                $this->info("✅ Created log file: {$logFile}");
            }

            // Set file permissions (make it world writable to ensure it works)
            chmod($logFile, 0666);
            $this->info('✅ Set log file permissions: 666');

            // Try to change ownership if running as root
            if (function_exists('posix_getuid') && posix_getuid() === 0) {
                exec("chown www-data:www-data {$logDir}");
                exec("chown www-data:www-data {$logFile}");
                $this->info('✅ Changed ownership to www-data');
            }

            // Test writing to the log file
            $testMessage = '['.date('Y-m-d H:i:s')."] Log permissions fixed successfully\n";
            file_put_contents($logFile, $testMessage, FILE_APPEND | LOCK_EX);
            $this->info('✅ Log file write test successful');

            $this->newLine();
            $this->info('🎉 Log permissions fixed! You can now use Filament actions.');
            $this->info('💡 If you still have issues, restart your web server.');

        } catch (Exception $e) {
            $this->error('❌ Failed to fix log permissions: '.$e->getMessage());

            $this->newLine();
            $this->error('Manual fix required:');
            $this->line('Run these commands as root:');
            $this->line("  mkdir -p {$logDir}");
            $this->line("  touch {$logFile}");
            $this->line("  chmod 775 {$logDir}");
            $this->line("  chmod 666 {$logFile}");
            $this->line('  chown -R www-data:www-data '.storage_path());

            return 1;
        }

        return 0;
    }
}
