<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Course;
use Illuminate\Console\Command;

final class GenerateClassesTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:classes-test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the generate classes command with available courses';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Available courses for class generation:');

        $courses = Course::with('subjects')->get();

        if ($courses->isEmpty()) {
            $this->error('No courses found in the database.');

            return 1;
        }

        $this->table(
            ['ID', 'Code', 'Title', 'Department', 'Subject Count'],
            $courses->map(fn ($course): array => [
                'id' => $course->id,
                'code' => $course->code,
                'title' => $course->title,
                'department' => $course->department,
                'subject_count' => $course->subjects->count(),
            ])
        );

        $courseId = $this->ask('Enter the course ID you want to generate classes for');

        if (! $courseId || ! is_numeric($courseId)) {
            $this->error('Invalid course ID.');

            return 1;
        }

        $section = $this->ask('Enter the section for the classes (default: A)', 'A');
        $maxSlots = $this->ask('Enter the maximum slots for the classes (default: 50)', 50);

        $yearLevels = $this->ask('Enter the year levels to generate (comma-separated, e.g., 1,2,3,4) or leave empty for all', '');

        // Build the command string
        $command = "generate:classes {$courseId} --section={$section} --max-slots={$maxSlots}";

        if (! empty($yearLevels)) {
            $command .= " --year-levels={$yearLevels}";
        }

        // Confirm before proceeding
        if (! $this->confirm("Are you sure you want to run: php artisan {$command}?", true)) {
            $this->info('Command cancelled.');

            return 0;
        }

        // Call the actual command
        $this->info("Executing: php artisan {$command}");
        $this->call('generate:classes', [
            'course_id' => $courseId,
            '--section' => $section,
            '--max-slots' => $maxSlots,
            '--year-levels' => $yearLevels,
        ]);

        return 0;
    }
}
