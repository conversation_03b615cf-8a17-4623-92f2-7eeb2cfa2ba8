<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;

final class FindChromePath extends Command
{
    protected $signature = 'find:chrome-path';

    protected $description = 'Find the path of Chrome or Chromium';

    public function handle(): void
    {
        $this->info('Searching for Chrome/Chromium binaries...');

        // Check environment variable first
        $chromePath = env('CHROME_PATH');
        if ($chromePath && file_exists($chromePath)) {
            $this->info("Chrome path from environment: $chromePath");

            return;
        }

        // Check Nix profile paths first (common in actual containers)
        $nixProfilePaths = [
            '/root/.nix-profile/bin/chromium',
            '/root/.nix-profile/bin/chromium-browser',
            '/nix/var/nix/profiles/default/bin/chromium',
            '/nix/var/nix/profiles/default/bin/chromium-browser',
        ];

        $this->info('Checking Nix profile paths...');
        foreach ($nixProfilePaths as $path) {
            if (file_exists($path)) {
                $this->info("Chromium found in Nix profile: $path");
                if (is_executable($path)) {
                    $this->info('✓ Binary is executable');
                } else {
                    $this->warn('⚠ Binary found but not executable');
                }
            }
        }

        // Check using which command
        $browsers = [
            'chromium',
            'chromium-browser',
            'google-chrome-stable',
            'google-chrome',
            'chrome',
        ];

        $this->info('Checking PATH for Chrome/Chromium...');
        foreach ($browsers as $browser) {
            $path = shell_exec("which $browser 2>/dev/null");
            if ($path) {
                $path = mb_trim($path);
                $this->info("$browser found at: $path");

                // Test if the binary is executable
                if (is_executable($path)) {
                    $this->info('✓ Binary is executable');
                } else {
                    $this->warn('⚠ Binary found but not executable');
                }
            }
        }

        // Check common Nix store paths
        $this->info('Checking Nix store for chromium...');
        if (is_dir('/nix/store')) {
            $nixStorePath = shell_exec(
                'ls /nix/store 2>/dev/null | grep chromium | head -3'
            );
            if ($nixStorePath) {
                $paths = array_filter(explode("\n", mb_trim($nixStorePath)));
                foreach ($paths as $nixDir) {
                    $fullPath = "/nix/store/$nixDir/bin/chromium";
                    if (file_exists($fullPath)) {
                        $this->info("Chromium found in Nix store: $fullPath");
                        if (is_executable($fullPath)) {
                            $this->info('✓ Binary is executable');
                        } else {
                            $this->warn('⚠ Binary found but not executable');
                        }
                    }
                }
            }
        } else {
            $this->info(
                'Nix store directory not found - not a Nix environment'
            );
        }

        // Check environment variables used by Browsershot
        $this->info("\nEnvironment variables:");
        $this->info('CHROME_PATH: '.(env('CHROME_PATH') ?: 'not set'));
        $this->info(
            'NODE_BINARY_PATH: '.(env('NODE_BINARY_PATH') ?: 'not set')
        );
        $this->info(
            'NPM_BINARY_PATH: '.(env('NPM_BINARY_PATH') ?: 'not set')
        );

        // Check Node.js availability
        $this->info("\nChecking Node.js availability:");

        // Check Nix profile first
        $nixNodePaths = [
            '/root/.nix-profile/bin/node',
            '/nix/var/nix/profiles/default/bin/node',
        ];

        $nodeFound = false;
        foreach ($nixNodePaths as $path) {
            if (file_exists($path) && is_executable($path)) {
                $this->info("Node.js found in Nix profile: $path");
                $nodeFound = true;
                break;
            }
        }

        if (! $nodeFound) {
            $nodePath = shell_exec('which node 2>/dev/null');
            if ($nodePath) {
                $this->info('Node.js found at: '.mb_trim($nodePath));
                $nodeFound = true;
            }
        }

        if (! $nodeFound) {
            $this->error('Node.js not found in PATH or Nix profiles');
        }

        // Check NPM availability
        $this->info('Checking NPM availability:');

        $nixNpmPaths = [
            '/root/.nix-profile/bin/npm',
            '/nix/var/nix/profiles/default/bin/npm',
        ];

        $npmFound = false;
        foreach ($nixNpmPaths as $path) {
            if (file_exists($path) && is_executable($path)) {
                $this->info("NPM found in Nix profile: $path");
                $npmFound = true;
                break;
            }
        }

        if (! $npmFound) {
            $npmPath = shell_exec('which npm 2>/dev/null');
            if ($npmPath) {
                $this->info('NPM found at: '.mb_trim($npmPath));
                $npmFound = true;
            }
        }

        if (! $npmFound) {
            $this->error('NPM not found in PATH or Nix profiles');
        }

        $this->info(
            "\nFor Nix/Nixpacks environment, ensure your nixpacks.toml includes:"
        );
        $this->info('nixPkgs = ["chromium", "nodejs_20", "nodePackages.npm"]');

        $this->info(
            "\nNote: In Nix containers, binaries are typically located at:"
        );
        $this->info('  - /root/.nix-profile/bin/');
        $this->info('  - /nix/var/nix/profiles/default/bin/');
        $this->info('  - /nix/store/[hash]/bin/');
    }
}
