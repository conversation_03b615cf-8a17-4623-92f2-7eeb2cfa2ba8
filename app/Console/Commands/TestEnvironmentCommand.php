<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;

final class TestEnvironmentCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:environment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test environment variables and binary paths in container';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Container Environment Test');
        $this->info('============================');
        $this->newLine();

        // Test environment variables
        $this->info('📋 Environment Variables:');
        $this->line('-------------------------');

        $envVars = [
            'CHROME_PATH',
            'NODE_BINARY_PATH',
            'NPM_BINARY_PATH',
            'PATH',
            'HOME',
            'PWD',
        ];

        foreach ($envVars as $var) {
            $value = (env($var) ?: getenv($var)) ?: 'NOT SET';
            $this->line("  {$var}: {$value}");
        }

        $this->newLine();

        // Test binary locations
        $this->info('🔧 Binary Locations:');
        $this->line('--------------------');

        $binaries = ['chromium', 'chromium-browser', 'node', 'npm', 'php'];

        foreach ($binaries as $binary) {
            $path = shell_exec("which {$binary} 2>/dev/null");
            if ($path) {
                $path = mb_trim($path);
                $executable = is_executable($path) ? '✅' : '❌';
                $this->line("  {$executable} {$binary}: {$path}");
            } else {
                $this->line("  ❌ {$binary}: NOT FOUND");
            }
        }

        $this->newLine();

        // Test common Nix paths
        $this->info('📁 Nix Profile Paths:');
        $this->line('---------------------');

        $nixPaths = [
            '/root/.nix-profile/bin',
            '/nix/var/nix/profiles/default/bin',
            '/nix/store',
        ];

        foreach ($nixPaths as $path) {
            if (is_dir($path)) {
                $this->line("  ✅ {$path}: EXISTS");
                if ($path !== '/nix/store') {
                    $files = glob("{$path}/*");
                    $relevantFiles = array_filter($files, function ($file): bool {
                        $basename = basename($file);

                        return in_array($basename, ['chromium', 'chromium-browser', 'node', 'npm']);
                    });

                    foreach ($relevantFiles as $file) {
                        $basename = basename($file);
                        $executable = is_executable($file) ? '✅' : '❌';
                        $this->line("    {$executable} {$basename}: {$file}");
                    }
                }
            } else {
                $this->line("  ❌ {$path}: NOT FOUND");
            }
        }

        $this->newLine();

        // Test file permissions
        $this->info('🔐 Directory Permissions:');
        $this->line('-------------------------');

        $dirs = [
            storage_path(),
            storage_path('app'),
            storage_path('logs'),
            '/tmp',
        ];

        foreach ($dirs as $dir) {
            if (is_dir($dir)) {
                $writable = is_writable($dir) ? '✅' : '❌';
                $this->line("  {$writable} {$dir}: ".(is_writable($dir) ? 'WRITABLE' : 'NOT WRITABLE'));
            } else {
                $this->line("  ❌ {$dir}: NOT FOUND");
            }
        }

        $this->newLine();

        // Quick Browsershot test
        $this->info('🖨️  Quick Browsershot Test:');
        $this->line('---------------------------');

        try {
            if (class_exists(\Spatie\Browsershot\Browsershot::class)) {
                $this->line('  ✅ Browsershot class: LOADED');

                // Try to create a simple instance
                $browsershot = \Spatie\Browsershot\Browsershot::html('<h1>Test</h1>');
                $this->line('  ✅ Browsershot instance: CREATED');

                // Test with environment paths if set
                $chromePath = env('CHROME_PATH') ?: getenv('CHROME_PATH');
                $nodePath = env('NODE_BINARY_PATH') ?: getenv('NODE_BINARY_PATH');

                if ($chromePath && file_exists($chromePath)) {
                    $browsershot->setChromePath($chromePath);
                    $this->line("  ✅ Chrome path set: {$chromePath}");
                } else {
                    $this->line('  ⚠️  Chrome path: NOT SET OR INVALID');
                }

                if ($nodePath && file_exists($nodePath)) {
                    $browsershot->setNodeBinary($nodePath);
                    $this->line("  ✅ Node path set: {$nodePath}");
                } else {
                    $this->line('  ⚠️  Node path: NOT SET OR INVALID');
                }

            } else {
                $this->line('  ❌ Browsershot class: NOT FOUND');
            }
        } catch (Exception $e) {
            $this->line("  ❌ Browsershot test failed: {$e->getMessage()}");
        }

        $this->newLine();

        // Summary
        $this->info('📊 Summary:');
        $this->line('-----------');
        $this->line('  Run this command after deployment to verify your environment');
        $this->line('  Check that all required binaries are found and executable');
        $this->line('  Verify environment variables are properly set');
        $this->line('  Ensure directories have correct permissions');

        $this->newLine();
        $this->info('💡 Next Steps:');
        $this->line('  1. Fix any issues shown above');
        $this->line('  2. Run: php artisan find:chrome-path');
        $this->line('  3. Run: php artisan test:browsershot');
        $this->line('  4. Run: php artisan verify:browsershot');

        return 0;
    }
}
