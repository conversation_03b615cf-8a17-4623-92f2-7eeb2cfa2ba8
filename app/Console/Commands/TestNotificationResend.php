<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\StudentEnrollment;
use App\Notifications\MigrateToStudent;
use App\Services\BrowsershotService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

final class TestNotificationResend extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:notification-resend {enrollment_id? : Student enrollment ID to test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test notification resend functionality with debugging';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Testing Notification Resend Functionality...');
        $this->newLine();

        // Get enrollment ID
        $enrollmentId = $this->argument('enrollment_id');

        if (! $enrollmentId) {
            $enrollment = StudentEnrollment::first();
            if (! $enrollment) {
                $this->error('No student enrollments found in database');

                return 1;
            }
            $enrollmentId = $enrollment->id;
            $this->info("Using first enrollment found: ID {$enrollmentId}");
        } else {
            $enrollment = StudentEnrollment::find($enrollmentId);
            if (! $enrollment) {
                $this->error("Student enrollment with ID {$enrollmentId} not found");

                return 1;
            }
        }

        $this->info("Testing notification resend for enrollment: {$enrollment->id}");
        $this->info("Student: {$enrollment->student_name}");
        $this->newLine();

        // Test 1: Environment Check
        $this->info('📋 Step 1: Environment Check');
        $this->checkEnvironment();
        $this->newLine();

        // Test 2: BrowsershotService Test
        $this->info('🧪 Step 2: BrowsershotService Test');
        if (! $this->testBrowsershot()) {
            $this->error('BrowsershotService test failed - stopping here');

            return 1;
        }
        $this->newLine();

        // Test 3: PDF Generation Test
        $this->info('📄 Step 3: PDF Generation Test');
        if (! $this->testPdfGeneration($enrollment)) {
            $this->error('PDF generation test failed - stopping here');

            return 1;
        }
        $this->newLine();

        // Test 4: Notification Test
        $this->info('📧 Step 4: Notification Creation Test');
        if (! $this->testNotificationCreation($enrollment)) {
            $this->error('Notification creation test failed');

            return 1;
        }
        $this->newLine();

        $this->info('✅ All tests passed! Notification resend should work.');

        return 0;
    }

    /**
     * Check environment configuration
     */
    private function checkEnvironment(): void
    {
        $envVars = [
            'CHROME_PATH',
            'NODE_BINARY_PATH',
            'BROWSERSHOT_NO_SANDBOX',
            'BROWSERSHOT_DISABLE_WEB_SECURITY',
            'BROWSERSHOT_TIMEOUT',
        ];

        foreach ($envVars as $var) {
            $value = env($var);
            if ($value) {
                $this->line("  ✅ {$var}: {$value}");
            } else {
                $this->line("  ❌ {$var}: not set");
            }
        }

        // Check Chrome arguments from config
        $config = config('browsershot.default_options.chrome_args', []);
        $this->line('  📋 Chrome arguments count: '.count($config));

        // Check for double-escaped arguments
        $doubleEscaped = array_filter($config, fn ($arg): bool => mb_strpos((string) $arg, '----') === 0);

        if ($doubleEscaped !== []) {
            $this->line('  ⚠️  Found double-escaped arguments: '.implode(', ', array_slice($doubleEscaped, 0, 3)));
        } else {
            $this->line('  ✅ Chrome arguments look clean');
        }
    }

    /**
     * Test BrowsershotService basic functionality
     */
    private function testBrowsershot(): bool
    {
        try {
            $html = '<html><body><h1>Notification Test</h1><p>Generated at: '.now().'</p></body></html>';
            $testPath = storage_path('app/notification-test.pdf');

            $this->line('  Generating test PDF...');

            $success = BrowsershotService::generatePdf($html, $testPath, [
                'format' => 'A4',
                'print_background' => true,
                'timeout' => 60,
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
            ]);

            if ($success && file_exists($testPath)) {
                $fileSize = filesize($testPath);
                $this->line("  ✅ PDF generated successfully ({$fileSize} bytes)");
                unlink($testPath);

                return true;
            }
            $this->line('  ❌ PDF generation failed');

            return false;

        } catch (Exception $e) {
            $this->line('  ❌ BrowsershotService error: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Test PDF generation similar to notification
     */
    private function testPdfGeneration($enrollment): bool
    {
        try {
            $this->line('  Preparing notification data...');

            $generalSettings = \App\Models\GeneralSetting::first();

            $data = [
                'student' => $enrollment,
                'subjects' => $enrollment->SubjectsEnrolled,
                'school_year' => mb_convert_encoding(
                    $generalSettings->getSchoolYearString() ?? '',
                    'UTF-8',
                    'auto'
                ),
                'semester' => mb_convert_encoding(
                    $generalSettings->getSemester() ?? '',
                    'UTF-8',
                    'auto'
                ),
                'tuition' => $enrollment->studentTuition,
            ];

            $this->line('  Rendering HTML view...');
            $html = view('pdf.assesment-form', $data)->render();

            $testPath = storage_path('app/notification-assessment-test.pdf');

            $this->line('  Generating assessment PDF...');
            $success = BrowsershotService::generatePdf($html, $testPath, [
                'format' => 'A4',
                'landscape' => true,
                'print_background' => true,
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
                'timeout' => 180,
                'wait_until_network_idle' => true,
            ]);

            if ($success && file_exists($testPath)) {
                $fileSize = filesize($testPath);
                $this->line("  ✅ Assessment PDF generated successfully ({$fileSize} bytes)");
                unlink($testPath);

                return true;
            }
            $this->line('  ❌ Assessment PDF generation failed');

            return false;

        } catch (Exception $e) {
            $this->line('  ❌ PDF generation error: '.$e->getMessage());
            Log::error('Notification PDF test failed', [
                'enrollment_id' => $enrollment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Test notification creation without sending
     */
    private function testNotificationCreation(object $enrollment): bool
    {
        try {
            $this->line('  Creating MigrateToStudent notification...');

            $notification = new MigrateToStudent($enrollment);

            $this->line('  ✅ Notification created successfully');

            // Test the notification data preparation
            $this->line('  Testing notification data...');
            $notificationData = $notification->toArray($enrollment);

            if (isset($notificationData['assessment'])) {
                $this->line('  ✅ Assessment data prepared');
            } else {
                $this->line('  ⚠️  Assessment data not found in notification');
            }

            return true;

        } catch (Exception $e) {
            $this->line('  ❌ Notification creation error: '.$e->getMessage());
            Log::error('Notification creation test failed', [
                'enrollment_id' => $enrollment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }
}
