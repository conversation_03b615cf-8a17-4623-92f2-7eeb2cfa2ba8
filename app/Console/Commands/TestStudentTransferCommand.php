<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Jobs\BulkMoveStudentsToSectionJob;
use App\Jobs\MoveStudentToSectionJob;
use App\Models\ClassEnrollment;
use App\Models\Classes;
use App\Services\StudentSectionTransferService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * Command for testing student section transfer functionality
 */
final class TestStudentTransferCommand extends Command
{
    protected $signature = 'test:student-transfer 
                            {action : Action to perform (list-classes, list-enrollments, test-single, test-bulk, test-service)}
                            {--class-id= : Class ID for filtering}
                            {--student-id= : Student ID for testing}
                            {--target-class-id= : Target class ID for transfers}
                            {--limit=10 : Limit for bulk operations}';

    protected $description = 'Test student section transfer functionality';

    public function handle(): int
    {
        $action = $this->argument('action');

        return match ($action) {
            'list-classes' => $this->listClasses(),
            'list-enrollments' => $this->listEnrollments(),
            'test-single' => $this->testSingleTransfer(),
            'test-bulk' => $this->testBulkTransfer(),
            'test-service' => $this->testService(),
            default => $this->showHelp(),
        };
    }

    /**
     * List available classes for testing
     */
    private function listClasses(): int
    {
        $this->info('Available Classes for Testing:');
        $this->line('');

        $classes = Classes::with(['class_enrollments'])
            ->orderBy('subject_code')
            ->orderBy('section')
            ->get();

        $headers = ['ID', 'Subject Code', 'Section', 'School Year', 'Semester', 'Enrolled Students', 'Max Slots'];
        $rows = [];

        foreach ($classes as $class) {
            $enrollmentCount = $class->class_enrollments->count();
            $rows[] = [
                $class->id,
                $class->subject_code,
                $class->section,
                $class->school_year,
                $class->semester,
                $enrollmentCount,
                $class->maximum_slots ?? 'Unlimited'
            ];
        }

        $this->table($headers, $rows);

        return 0;
    }

    /**
     * List class enrollments for testing
     */
    private function listEnrollments(): int
    {
        $classId = $this->option('class-id');
        
        if (!$classId) {
            $this->error('Please provide --class-id option');
            return 1;
        }

        $class = Classes::find($classId);
        if (!$class) {
            $this->error("Class not found: {$classId}");
            return 1;
        }

        $this->info("Enrollments for Class: {$class->subject_code} - Section {$class->section}");
        $this->line('');

        $enrollments = ClassEnrollment::with(['student'])
            ->where('class_id', $classId)
            ->get();

        if ($enrollments->isEmpty()) {
            $this->warn('No enrollments found for this class');
            return 0;
        }

        $headers = ['Enrollment ID', 'Student ID', 'Student Name', 'Status', 'Created At'];
        $rows = [];

        foreach ($enrollments as $enrollment) {
            $rows[] = [
                $enrollment->id,
                $enrollment->student_id,
                $enrollment->student?->full_name ?? 'Unknown',
                $enrollment->status ? 'Active' : 'Inactive',
                $enrollment->created_at?->format('Y-m-d H:i:s') ?? 'N/A'
            ];
        }

        $this->table($headers, $rows);

        return 0;
    }

    /**
     * Test single student transfer
     */
    private function testSingleTransfer(): int
    {
        $studentId = $this->option('student-id');
        $targetClassId = $this->option('target-class-id');

        if (!$studentId || !$targetClassId) {
            $this->error('Please provide --student-id and --target-class-id options');
            return 1;
        }

        // Find the class enrollment
        $classEnrollment = ClassEnrollment::with(['student', 'class'])
            ->where('student_id', $studentId)
            ->first();

        if (!$classEnrollment) {
            $this->error("No class enrollment found for student: {$studentId}");
            return 1;
        }

        $this->info('Testing Single Student Transfer:');
        $this->line("Student: {$classEnrollment->student?->full_name}");
        $this->line("From Class: {$classEnrollment->class?->subject_code} - Section {$classEnrollment->class?->section}");
        
        $targetClass = Classes::find($targetClassId);
        if ($targetClass) {
            $this->line("To Class: {$targetClass->subject_code} - Section {$targetClass->section}");
        }

        if ($this->confirm('Dispatch background job for this transfer?')) {
            MoveStudentToSectionJob::dispatch(
                $classEnrollment->id,
                (int) $targetClassId,
                1 // Test user ID
            );

            $this->info('✅ Single transfer job dispatched successfully!');
            $this->line('Check the queue worker and database notifications for results.');
        }

        return 0;
    }

    /**
     * Test bulk student transfer
     */
    private function testBulkTransfer(): int
    {
        $classId = $this->option('class-id');
        $targetClassId = $this->option('target-class-id');
        $limit = (int) $this->option('limit');

        if (!$classId || !$targetClassId) {
            $this->error('Please provide --class-id and --target-class-id options');
            return 1;
        }

        $enrollments = ClassEnrollment::with(['student', 'class'])
            ->where('class_id', $classId)
            ->limit($limit)
            ->get();

        if ($enrollments->isEmpty()) {
            $this->error("No enrollments found for class: {$classId}");
            return 1;
        }

        $this->info('Testing Bulk Student Transfer:');
        $this->line("Students to transfer: {$enrollments->count()}");
        $this->line("From Class ID: {$classId}");
        $this->line("To Class ID: {$targetClassId}");

        $this->line('');
        $this->info('Students:');
        foreach ($enrollments as $enrollment) {
            $this->line("- {$enrollment->student?->full_name} (ID: {$enrollment->student_id})");
        }

        if ($this->confirm('Dispatch background job for bulk transfer?')) {
            $enrollmentIds = $enrollments->pluck('id')->toArray();
            
            BulkMoveStudentsToSectionJob::dispatch(
                $enrollmentIds,
                (int) $targetClassId,
                1 // Test user ID
            );

            $this->info('✅ Bulk transfer job dispatched successfully!');
            $this->line('Check the queue worker and database notifications for results.');
        }

        return 0;
    }

    /**
     * Test the service directly (synchronous)
     */
    private function testService(): int
    {
        $studentId = $this->option('student-id');
        $targetClassId = $this->option('target-class-id');

        if (!$studentId || !$targetClassId) {
            $this->error('Please provide --student-id and --target-class-id options');
            return 1;
        }

        $classEnrollment = ClassEnrollment::with(['student', 'class'])
            ->where('student_id', $studentId)
            ->first();

        if (!$classEnrollment) {
            $this->error("No class enrollment found for student: {$studentId}");
            return 1;
        }

        $this->info('Testing Service Directly (Synchronous):');
        $this->line("Student: {$classEnrollment->student?->full_name}");

        try {
            $transferService = app(StudentSectionTransferService::class);
            $result = $transferService->transferStudent($classEnrollment, (int) $targetClassId);

            $this->info('✅ Transfer completed successfully!');
            $this->line('');
            $this->info('Transfer Details:');
            $this->line("Student: {$result['student_name']}");
            $this->line("From Section: {$result['old_section']}");
            $this->line("To Section: {$result['new_section']}");
            $this->line("Subject: {$result['subject_code']}");
            $this->line("Subject Enrollment Updated: " . ($result['subject_enrollment_updated'] ? 'Yes' : 'No'));

        } catch (\Exception $e) {
            $this->error('❌ Transfer failed: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Show help information
     */
    private function showHelp(): int
    {
        $this->info('Student Transfer Testing Command');
        $this->line('');
        $this->info('Available Actions:');
        $this->line('  list-classes        - List all available classes');
        $this->line('  list-enrollments    - List enrollments for a specific class (requires --class-id)');
        $this->line('  test-single         - Test single student transfer (requires --student-id and --target-class-id)');
        $this->line('  test-bulk           - Test bulk student transfer (requires --class-id and --target-class-id)');
        $this->line('  test-service        - Test service directly without jobs (requires --student-id and --target-class-id)');
        $this->line('');
        $this->info('Examples:');
        $this->line('  php artisan test:student-transfer list-classes');
        $this->line('  php artisan test:student-transfer list-enrollments --class-id=1');
        $this->line('  php artisan test:student-transfer test-single --student-id=123 --target-class-id=2');
        $this->line('  php artisan test:student-transfer test-bulk --class-id=1 --target-class-id=2 --limit=5');
        $this->line('  php artisan test:student-transfer test-service --student-id=123 --target-class-id=2');

        return 0;
    }
}
