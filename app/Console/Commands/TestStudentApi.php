<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;

final class TestStudentApi extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:student-api';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tests the Student API endpoints for authenticated and guest access.';

    // Configuration
    private string $baseUrl = 'http://localhost:8000/api/admin/students';

    private string $apiToken = '26|UJawlRpDCpKo5sFSD52qlCqEKxBSBNuTDKc4tfbF2e0f26ee'; // Replace if token changes

    private string $testStudentId = '123'; // Use a valid student ID for detail/update/delete tests

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting Student API tests...');

        $endpoints = [
            'GET_LIST' => ['method' => 'GET', 'path' => ''],
            'GET_DETAIL' => ['method' => 'GET', 'path' => '/'.$this->testStudentId],
            // POST needs data, we'll send empty for basic auth check
            'POST_CREATE' => ['method' => 'POST', 'path' => '', 'data' => []],
            // PUT needs data, we'll send empty for basic auth check
            'PUT_UPDATE' => ['method' => 'PUT', 'path' => '/'.$this->testStudentId, 'data' => []],
            'DELETE' => ['method' => 'DELETE', 'path' => '/'.$this->testStudentId],
        ];

        foreach ($endpoints as $name => $details) {
            $this->testEndpoint($name, $details);
        }

        $this->info('Student API tests completed.');

        return Command::SUCCESS;
    }

    private function testEndpoint(string $name, array $details): void
    {
        $this->line("\n--- Testing Endpoint: {$name} ({$details['method']} {$this->baseUrl}{$details['path']}) ---");

        // --- Test Authenticated Access ---
        $this->info('Testing Authenticated Access...');
        try {
            $response = Http::withToken($this->apiToken)
                ->acceptJson()
                ->{$details['method']}($this->baseUrl.$details['path'], $details['data'] ?? []);

            // Define acceptable success/validation status codes
            $successCodes = [200, 201, 204]; // OK, Created, No Content (for DELETE)
            $validationErrorCode = 422; // Unprocessable Entity (for POST/PUT with invalid data)

            if ($response->successful() || in_array($response->status(), $successCodes)) {
                $this->info("[AUTH OK] Status: {$response->status()} - Request successful as expected.");
            } elseif ($response->status() === $validationErrorCode && in_array($details['method'], ['POST', 'PUT'])) {
                $this->info("[AUTH OK] Status: {$response->status()} - Received expected validation error for {$details['method']}.");
            } else {
                $this->error("[AUTH FAILED] Status: {$response->status()} - Unexpected status code.");
                $this->comment('Response Body: '.$response->body());
            }

        } catch (RequestException $e) {
            $this->error('[AUTH FAILED] Request Exception: '.$e->getMessage());
            if ($e->response) {
                $this->comment('Response Body: '.$e->response->body());
            }
        } catch (Exception $e) {
            $this->error('[AUTH FAILED] General Exception: '.$e->getMessage());
        }

        // --- Test Guest Access ---
        $this->info('Testing Guest Access...');
        try {
            $response = Http::acceptJson()
                ->{$details['method']}($this->baseUrl.$details['path'], $details['data'] ?? []);

            if ($response->status() === 401) {
                $this->info('[GUEST OK] Status: 401 - Received expected Unauthorized error.');
            } else {
                $this->error("[GUEST FAILED] Status: {$response->status()} - Expected 401 Unauthorized, but received {$response->status()}.");
                $this->comment('Response Body: '.$response->body());
            }
        } catch (RequestException $e) {
            // A 401 might throw a RequestException depending on Laravel version/config
            if ($e->response && $e->response->status() === 401) {
                $this->info('[GUEST OK] Status: 401 - Received expected Unauthorized error via exception.');
            } else {
                $this->error('[GUEST FAILED] Request Exception: '.$e->getMessage());
                if ($e->response) {
                    $this->comment('Response Body: '.$e->response->body());
                }
            }
        } catch (Exception $e) {
            $this->error('[GUEST FAILED] General Exception: '.$e->getMessage());
        }
    }
}
