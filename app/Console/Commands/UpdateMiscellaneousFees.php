<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\StudentEnrollment;
use App\Services\GeneralSettingsService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

final class UpdateMiscellaneousFees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tuition:update-miscellaneous-fees
                            {--dry-run : Show what would be updated without making changes}
                            {--semester= : Specific semester to update (default: current)}
                            {--school-year= : Specific school year to update (default: current)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update miscellaneous fees in student tuition records to match their enrollment course fees';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');
        $settingsService = app(GeneralSettingsService::class);

        // Get target semester and school year
        $targetSemester = $this->option('semester') ?? $settingsService->getCurrentSemester();
        $targetSchoolYear = $this->option('school-year') ?? $settingsService->getCurrentSchoolYearString();

        $this->info('Updating miscellaneous fees for:');
        $this->info("School Year: {$targetSchoolYear}");
        $this->info("Semester: {$targetSemester}");
        $this->info('Mode: '.($isDryRun ? 'DRY RUN (no changes will be made)' : 'LIVE UPDATE'));
        $this->newLine();

        // Get enrollments with their tuition records and courses
        $enrollments = StudentEnrollment::with(['course', 'studentTuition', 'student'])
            ->where('school_year', $targetSchoolYear)
            ->where('semester', $targetSemester)
            ->whereHas('studentTuition') // Only enrollments with tuition records
            ->get();

        if ($enrollments->isEmpty()) {
            $this->warn('No enrollments found for the specified period.');

            return 0;
        }

        $this->info("Found {$enrollments->count()} enrollments to process.");
        $this->newLine();

        $updatedCount = 0;
        $errorCount = 0;
        $skippedCount = 0;

        $progressBar = $this->output->createProgressBar($enrollments->count());
        $progressBar->start();

        foreach ($enrollments as $enrollment) {
            try {
                $tuition = $enrollment->studentTuition;
                $course = $enrollment->course;
                $student = $enrollment->student;

                if (! $course) {
                    $this->newLine();
                    $this->warn("Skipping enrollment ID {$enrollment->id}: No course found");
                    $skippedCount++;
                    $progressBar->advance();

                    continue;
                }

                if (! $tuition) {
                    $this->newLine();
                    $this->warn("Skipping enrollment ID {$enrollment->id}: No tuition record found");
                    $skippedCount++;
                    $progressBar->advance();

                    continue;
                }

                $currentMiscFee = $tuition->total_miscelaneous_fees;
                $correctMiscFee = $course->getMiscellaneousFee();

                // Check if update is needed
                if ($currentMiscFee === $correctMiscFee) {
                    $skippedCount++;
                    $progressBar->advance();

                    continue;
                }

                if ($isDryRun) {
                    $this->newLine();
                    $this->line("Would update: {$student->full_name} (ID: {$student->id})");
                    $this->line("  Course: {$course->code}");
                    $this->line("  Current Misc Fee: ₱{$currentMiscFee}");
                    $this->line("  Correct Misc Fee: ₱{$correctMiscFee}");
                    $this->line('  Difference: ₱'.($correctMiscFee - $currentMiscFee));
                } else {
                    // Calculate new totals
                    $oldOverallTuition = $tuition->overall_tuition;
                    $newOverallTuition = $tuition->total_tuition + $correctMiscFee;
                    $newBalance = $newOverallTuition - $tuition->downpayment;

                    // Update the tuition record
                    DB::transaction(function () use ($tuition, $correctMiscFee, $newOverallTuition, $newBalance): void {
                        $tuition->update([
                            'total_miscelaneous_fees' => $correctMiscFee,
                            'overall_tuition' => $newOverallTuition,
                            'total_balance' => $newBalance,
                        ]);
                    });
                }

                $updatedCount++;
                $progressBar->advance();

            } catch (Exception $e) {
                $this->newLine();
                $this->error("Error processing enrollment ID {$enrollment->id}: ".$e->getMessage());
                $errorCount++;
                $progressBar->advance();
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        // Summary
        $this->info('Update Summary:');
        $this->info("Total processed: {$enrollments->count()}");
        $this->info("Updated: {$updatedCount}");
        $this->info("Skipped (no change needed): {$skippedCount}");
        $this->info("Errors: {$errorCount}");

        if ($isDryRun) {
            $this->newLine();
            $this->comment('This was a dry run. No changes were made to the database.');
            $this->comment('Run without --dry-run to apply the changes.');
        } else {
            $this->newLine();
            $this->info('Miscellaneous fees have been successfully updated!');
        }

        return 0;
    }
}
