<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Jobs\SendAssessmentNotificationJob;
use App\Models\StudentEnrollment;
use App\Services\EnrollmentService;
use Exception;
use Illuminate\Console\Command;

final class TestAssessmentResend extends Command
{
    protected $signature = 'test:assessment-resend {enrollment-id} {--sync : Run synchronously instead of queued}';

    protected $description = 'Test the assessment resend functionality';

    public function handle(): int
    {
        $enrollmentId = $this->argument('enrollment-id');
        $runSync = $this->option('sync');

        $this->info("Testing assessment resend for enrollment ID: $enrollmentId");
        $this->newLine();

        // Find the enrollment
        $enrollment = StudentEnrollment::withTrashed()->find($enrollmentId);
        if (! $enrollment) {
            $this->error("Enrollment not found: $enrollmentId");

            return 1;
        }

        $this->info('Found enrollment:');
        $this->line('- Student: '.$enrollment->student?->full_name);
        $this->line('- Email: '.$enrollment->student?->email);
        $this->line('- Status: '.$enrollment->status);
        $this->line('- Deleted: '.($enrollment->trashed() ? 'Yes' : 'No'));
        $this->newLine();

        if ($runSync) {
            $this->info('Running synchronously (not queued)...');
            $this->testSynchronous($enrollment);
        } else {
            $this->info('Testing via EnrollmentService (queued)...');
            $this->testViaService($enrollment);
        }

        return 0;
    }

    private function testSynchronous(StudentEnrollment $enrollment): void
    {
        try {
            // Test binary detection like the job does
            $chromePath = $this->findWorkingBinary([
                '/root/.nix-profile/bin/chromium',
                '/nix/var/nix/profiles/default/bin/chromium',
            ]);
            $nodePath = $this->findWorkingBinary([
                '/root/.nix-profile/bin/node',
                '/nix/var/nix/profiles/default/bin/node',
            ]);
            $npmPath = $this->findWorkingBinary([
                '/root/.nix-profile/bin/npm',
                '/nix/var/nix/profiles/default/bin/npm',
            ]);

            if ($chromePath !== null && $chromePath !== '' && $chromePath !== '0') {
                putenv("CHROME_PATH=$chromePath");
                $_ENV['CHROME_PATH'] = $chromePath;
            }
            if ($nodePath !== null && $nodePath !== '' && $nodePath !== '0') {
                putenv("NODE_BINARY_PATH=$nodePath");
                $_ENV['NODE_BINARY_PATH'] = $nodePath;
            }
            if ($npmPath !== null && $npmPath !== '' && $npmPath !== '0') {
                putenv("NPM_BINARY_PATH=$npmPath");
                $_ENV['NPM_BINARY_PATH'] = $npmPath;
            }

            $this->line('Environment variables set:');
            $this->line('- CHROME_PATH: '.env('CHROME_PATH'));
            $this->line('- NODE_BINARY_PATH: '.env('NODE_BINARY_PATH'));
            $this->line('- NPM_BINARY_PATH: '.env('NPM_BINARY_PATH'));
            $this->newLine();

            // Create and run the job
            $job = new SendAssessmentNotificationJob($enrollment, 'test-'.time());

            $this->info('Executing job handle method...');
            $job->handle();

            $this->info('✅ Job completed successfully');

        } catch (Exception $e) {
            $this->error('❌ Job failed: '.$e->getMessage());
            $this->line('Stack trace:');
            $this->line($e->getTraceAsString());
        }
    }

    private function testViaService(StudentEnrollment $enrollment): void
    {
        try {
            $service = new EnrollmentService;
            $result = $service->resendAssessmentNotification($enrollment);

            if ($result['success']) {
                $this->info('✅ Service call successful');
                $this->line('Job ID: '.$result['job_id']);
                $this->line('Student: '.$result['student_name']);
                $this->line('Message: '.$result['message']);

                $this->newLine();
                $this->info('Check the queue worker logs for job execution details.');
                $this->info('You can also check Laravel logs at storage/logs/laravel.log');
            } else {
                $this->error('❌ Service call failed');
                $this->line('Message: '.$result['message']);
            }

        } catch (Exception $e) {
            $this->error('❌ Service call failed with exception: '.$e->getMessage());
            $this->line('Stack trace:');
            $this->line($e->getTraceAsString());
        }
    }

    /**
     * Find the first working binary from a list of paths
     */
    private function findWorkingBinary(array $paths): ?string
    {
        foreach ($paths as $path) {
            if (file_exists($path) && is_executable($path)) {
                // Test that the binary actually works
                $testOutput = shell_exec("$path --version 2>/dev/null");
                if ($testOutput) {
                    return $path;
                }
            }
        }

        return null;
    }
}
