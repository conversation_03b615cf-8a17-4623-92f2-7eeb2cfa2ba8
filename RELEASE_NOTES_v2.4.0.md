# Release Notes - DccpAdminV2 v2.4.0

**Release Date:** July 3, 2025  
**Previous Version:** v2.3.0 (commit 274792c)  
**Current Version:** v2.4.0 (commit 67e586f)  
**Total Commits:** 14 commits since v2.3.0

---

## 🚀 Major Features & Enhancements

### 📊 Faculty Management System
- **NEW**: Comprehensive faculty class assignment management
- **NEW**: Auto-assignment functionality for unassigned classes
- **NEW**: Manual assignment interface with filtering capabilities
- **NEW**: Faculty relation manager for viewing assigned classes
- **NEW**: Assignment statistics and schedule display improvements
- Enhanced UI for displaying class schedules and assignment data

### 🎓 SHS (Senior High School) Support
- **NEW**: Added SHS support to classes table with new columns:
  - `shs_track_id` - Track identification for SHS students
  - `shs_strand_id` - Strand identification for SHS students  
  - `grade_level` - Grade level support for SHS
- **NEW**: Migration to update existing classes classification to 'college'
- **NEW**: Enhanced class management to support both College and SHS students

### 💬 Chat Integration
- **NEW**: Filament-Chatify integration with floating chat widget
- **NEW**: Real-time messaging with Pusher integration
- **NEW**: Message count badge notifications
- **NEW**: RTL/LTR layout support based on user locale
- **NEW**: Enhanced chat interface with modals, favorites, and shared photos

### 📈 Monitoring & Performance
- **NEW**: Laravel Nightwatch monitoring integration
- **NEW**: Laravel Horizon for queue management
- **NEW**: Enhanced logging with Nightwatch channel
- **NEW**: Request sampling rate configuration
- **NEW**: Queue job monitoring and management

### 🗓️ Timetable Enhancements
- **NEW**: Timetable conflict detection modal with detailed analysis
- **NEW**: Color-coded schedule entries for better visualization
- **NEW**: Export functionality for timetable data
- **NEW**: Enhanced conflict analysis displays
- **NEW**: 12-hour time format display improvements

### 📚 Subject Management Improvements
- **NEW**: Subject selection improvements in class creation
- **NEW**: Migration for `subject_id` column in classes table
- **NEW**: Enhanced subject-class relationship management

### 📋 Student History & Versioning
- **NEW**: Student history page with tabbed organization
- **NEW**: Academic History and Financial Transactions tabs
- **NEW**: Versionable configuration for tracking changes
- **NEW**: Versions table with soft deletes support
- **NEW**: Comprehensive data display organized by school year/semester

---

## 🔧 Infrastructure & DevOps

### 🐳 Docker & CI/CD Improvements
- **UPDATED**: Migrated from GHCR to Docker Hub for image hosting
- **NEW**: Automated Browsershot PDF generation testing in CI pipeline
- **UPDATED**: Enhanced Dockerfile with Node.js 22 and latest npm
- **UPDATED**: Improved build process with better caching strategies
- **NEW**: Automated image tagging and publishing workflow

### 🔒 Security & Validation
- **NEW**: Schedule overlap validation for class schedules
- **NEW**: Room availability checks with custom validation rules
- **NEW**: Enhanced conflict detection with user notifications
- **NEW**: Improved form validation with detailed error messages

### 📦 Package Management & Dependencies
- **NEW**: Filament database sync integration (`teguh02/filament-db-sync`)
- **NEW**: Backup plugin integration (`shuvroroy/filament-spatie-laravel-backup`)
- **NEW**: Versionable package (`mansoor/filament-versionable`)
- **UPDATED**: Laravel, Filament, and other core dependencies to latest versions
- **UPDATED**: PHP extensions and runtime dependencies

---

## 🛠️ Technical Improvements

### 🗄️ Database & Configuration
- **UPDATED**: PostgreSQL and Redis connection configurations
- **NEW**: Database sync configuration with excluded guest models
- **NEW**: Horizon configuration for queue management
- **NEW**: Backup destination configuration
- **UPDATED**: Environment configuration for production deployment

### 🎨 UI/UX Enhancements
- **IMPROVED**: Class creation form with better validation
- **IMPROVED**: Timetable views with enhanced time formatting
- **IMPROVED**: EditClass redirect behavior (now redirects to view page)
- **NEW**: Enhanced Filament stubs for improved resource management
- **NEW**: Loading states and visual hierarchy improvements

### 🧪 Testing & Quality Assurance
- **NEW**: Comprehensive tests for timetable color coding
- **NEW**: Conflict detection testing suite
- **NEW**: Feature tests for timetable enhancements
- **NEW**: Schedule positioning and loading state tests
- **IMPROVED**: Code consistency and formatting across test files

---

## 🔄 Breaking Changes

⚠️ **Important**: This release includes database schema changes that require migration:

1. **Classes Table**: New SHS-related columns added
2. **Versions Table**: New table for tracking model changes
3. **Classification Updates**: Existing classes will be marked as 'college' type

**Migration Required**: Run `php artisan migrate` after deployment

---

## 📋 Deployment Notes

### Docker Hub Migration
- Images are now hosted on Docker Hub instead of GitHub Container Registry
- Update your deployment scripts to pull from: `your_username/dccpadminv2:latest`
- CI/CD pipeline now includes Browsershot testing before deployment

### Environment Variables
New environment variables added:
```env
NIGHTWATCH_REQUEST_SAMPLE_RATE=0.1
NIGHTWATCH_TOKEN=your_token_here
SYNC_HOST=your_sync_host
SYNC_TOKEN=your_sync_token
```

### System Requirements
- Node.js 22.16.0 (included in Docker image)
- PostgreSQL (recommended version 15+)
- Redis (recommended version 7+)
- Chrome/Chromium for PDF generation

---

## 🐛 Bug Fixes

- Fixed CI workflow image name passing
- Corrected Docker build process for production environment
- Improved browser path configuration for PDF generation
- Enhanced error handling in class assignment processes
- Fixed various formatting and consistency issues

---

## 👥 Contributors

- **Louis** (@yukazakiri) - Lead Developer

---

## 🔗 Links

- **Repository**: https://github.com/yukazakiri/DccpAdminV2
- **Docker Hub**: https://hub.docker.com/r/your_username/dccpadminv2
- **Previous Release**: v2.3.0 (274792c)

---

*For technical support or questions about this release, please open an issue on the GitHub repository.*
