#!/bin/bash

# DCCP Admin V2 - Installation Verification Script
# Verifies that the installation was successful and all components are working

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_URL="http://localhost:8000"
ADMIN_URL="$APP_URL/admin"
API_URL="$APP_URL/api"
DOCS_URL="$APP_URL/docs"
KB_URL="$APP_URL/kb"

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
FAILED_TESTS=()

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
    ((TESTS_PASSED++))
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
    ((TESTS_FAILED++))
    FAILED_TESTS+=("$1")
}

# Test functions
test_docker_running() {
    log_info "Checking Docker services..."
    
    if docker-compose ps | grep -q "Up"; then
        log_success "Docker services are running"
    else
        log_error "Docker services are not running"
        return 1
    fi
}

test_application_response() {
    log_info "Testing application response..."
    
    if curl -s -o /dev/null -w "%{http_code}" "$APP_URL" | grep -q "200\|302"; then
        log_success "Application is responding"
    else
        log_error "Application is not responding at $APP_URL"
        return 1
    fi
}

test_admin_panel() {
    log_info "Testing admin panel access..."
    
    if curl -s -o /dev/null -w "%{http_code}" "$ADMIN_URL" | grep -q "200\|302"; then
        log_success "Admin panel is accessible"
    else
        log_error "Admin panel is not accessible at $ADMIN_URL"
        return 1
    fi
}

test_database_connection() {
    log_info "Testing database connection..."
    
    if docker-compose exec -T app php artisan migrate:status >/dev/null 2>&1; then
        log_success "Database connection is working"
    else
        log_error "Database connection failed"
        return 1
    fi
}

test_redis_connection() {
    log_info "Testing Redis connection..."
    
    if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
        log_success "Redis connection is working"
    else
        log_error "Redis connection failed"
        return 1
    fi
}

test_queue_system() {
    log_info "Testing queue system..."
    
    if docker-compose exec -T app php artisan queue:work --once --timeout=5 >/dev/null 2>&1; then
        log_success "Queue system is working"
    else
        log_warning "Queue system test inconclusive (no jobs to process)"
    fi
}

test_file_permissions() {
    log_info "Testing file permissions..."
    
    if docker-compose exec -T app test -w /app/storage; then
        log_success "Storage directory is writable"
    else
        log_error "Storage directory is not writable"
        return 1
    fi
}

test_environment_config() {
    log_info "Testing environment configuration..."
    
    if docker-compose exec -T app php artisan config:show app.name | grep -q "DCCP"; then
        log_success "Environment configuration is loaded"
    else
        log_error "Environment configuration is not properly loaded"
        return 1
    fi
}

test_api_documentation() {
    log_info "Testing API documentation..."
    
    if curl -s -o /dev/null -w "%{http_code}" "$DOCS_URL" | grep -q "200"; then
        log_success "API documentation is accessible"
    else
        log_warning "API documentation may not be accessible at $DOCS_URL"
    fi
}

test_knowledge_base() {
    log_info "Testing knowledge base..."
    
    if curl -s -o /dev/null -w "%{http_code}" "$KB_URL" | grep -q "200\|302"; then
        log_success "Knowledge base is accessible"
    else
        log_warning "Knowledge base may not be accessible at $KB_URL"
    fi
}

test_ssl_certificate() {
    log_info "Testing SSL certificate (if HTTPS)..."
    
    if [[ "$APP_URL" == https* ]]; then
        if curl -s -k -o /dev/null -w "%{http_code}" "$APP_URL" | grep -q "200\|302"; then
            log_success "HTTPS is working"
        else
            log_error "HTTPS is not working properly"
            return 1
        fi
    else
        log_warning "Application is running on HTTP (not HTTPS)"
    fi
}

test_performance() {
    log_info "Testing application performance..."
    
    response_time=$(curl -s -o /dev/null -w "%{time_total}" "$APP_URL")
    if (( $(echo "$response_time < 5.0" | bc -l) )); then
        log_success "Application response time is good (${response_time}s)"
    else
        log_warning "Application response time is slow (${response_time}s)"
    fi
}

# System information
show_system_info() {
    echo
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${BLUE}📊 System Information${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    echo -e "${BLUE}🐳 Docker Version:${NC} $(docker --version)"
    echo -e "${BLUE}🐙 Docker Compose Version:${NC} $(docker-compose --version)"
    echo -e "${BLUE}💾 Available Disk Space:${NC} $(df -h . | awk 'NR==2{print $4}')"
    echo -e "${BLUE}🧠 Available Memory:${NC} $(free -h | awk 'NR==2{print $7}')"
    
    echo
    echo -e "${BLUE}📦 Container Status:${NC}"
    docker-compose ps
    
    echo
    echo -e "${BLUE}🔗 Service URLs:${NC}"
    echo -e "   Application: $APP_URL"
    echo -e "   Admin Panel: $ADMIN_URL"
    echo -e "   API Docs: $DOCS_URL"
    echo -e "   Knowledge Base: $KB_URL"
}

# Show test results
show_results() {
    echo
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${BLUE}📋 Verification Results${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    echo -e "${GREEN}✅ Tests Passed: $TESTS_PASSED${NC}"
    echo -e "${RED}❌ Tests Failed: $TESTS_FAILED${NC}"
    
    if [ $TESTS_FAILED -gt 0 ]; then
        echo
        echo -e "${RED}Failed Tests:${NC}"
        for test in "${FAILED_TESTS[@]}"; do
            echo -e "   • $test"
        done
        
        echo
        echo -e "${YELLOW}🔧 Troubleshooting Tips:${NC}"
        echo "1. Check Docker logs: docker-compose logs -f"
        echo "2. Verify environment file: cat .env"
        echo "3. Restart services: docker-compose restart"
        echo "4. Check disk space: df -h"
        echo "5. Review documentation: $KB_URL"
    else
        echo
        echo -e "${GREEN}🎉 All tests passed! Your DCCP Admin V2 installation is working correctly.${NC}"
    fi
    
    echo
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# Main verification function
main() {
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${GREEN}🔍 DCCP Admin V2 - Installation Verification${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    
    log_info "Starting verification process..."
    echo
    
    # Run all tests
    test_docker_running || true
    test_application_response || true
    test_admin_panel || true
    test_database_connection || true
    test_redis_connection || true
    test_queue_system || true
    test_file_permissions || true
    test_environment_config || true
    test_api_documentation || true
    test_knowledge_base || true
    test_ssl_certificate || true
    test_performance || true
    
    show_system_info
    show_results
    
    # Exit with appropriate code
    if [ $TESTS_FAILED -gt 0 ]; then
        exit 1
    else
        exit 0
    fi
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    log_error "docker-compose.yml not found. Please run this script from the DCCP Admin V2 directory."
    exit 1
fi

# Run main function
main "$@"
