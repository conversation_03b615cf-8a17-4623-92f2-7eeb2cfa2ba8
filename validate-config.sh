#!/bin/bash

# DCCP Admin V2 - Configuration Validation Script
# This script validates your environment setup before deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
ERRORS=0
WARNINGS=0
SUCCESS=0

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
    SUCCESS=$((SUCCESS + 1))
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
    WARNINGS=$((WARNINGS + 1))
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
    ERRORS=$((ERRORS + 1))
}

check_file_exists() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ]; then
        log_success "$description exists"
        return 0
    else
        log_error "$description not found at $file"
        return 1
    fi
}

check_env_var() {
    local var_name="$1"
    local description="$2"
    local required="$3"
    
    if [ -f .env ]; then
        local value=$(grep "^${var_name}=" .env 2>/dev/null | cut -d '=' -f2- | tr -d '"' | tr -d "'")
        
        if [ -n "$value" ] && [ "$value" != "" ]; then
            log_success "$description is set"
            return 0
        else
            if [ "$required" = "true" ]; then
                log_error "$description is not set in .env"
                return 1
            else
                log_warning "$description is not set (optional)"
                return 0
            fi
        fi
    else
        log_error ".env file not found"
        return 1
    fi
}

check_directory_permissions() {
    local dir="$1"
    local description="$2"
    
    if [ -d "$dir" ]; then
        if [ -w "$dir" ]; then
            log_success "$description is writable"
            return 0
        else
            log_warning "$description exists but may not be writable"
            return 0
        fi
    else
        log_warning "$description directory does not exist (will be created)"
        return 0
    fi
}

check_docker() {
    if command -v docker &> /dev/null; then
        log_success "Docker is installed"
        
        if docker info &> /dev/null; then
            log_success "Docker daemon is running"
        else
            log_error "Docker daemon is not running"
        fi
    else
        log_error "Docker is not installed"
    fi
    
    if command -v docker-compose &> /dev/null; then
        log_success "Docker Compose is installed"
    else
        log_error "Docker Compose is not installed"
    fi
}

check_external_services() {
    if [ -f .env ]; then
        local db_host=$(grep "^DB_HOST=" .env 2>/dev/null | cut -d '=' -f2 | tr -d '"' | tr -d "'")
        local db_port=$(grep "^DB_PORT=" .env 2>/dev/null | cut -d '=' -f2 | tr -d '"' | tr -d "'")
        local redis_host=$(grep "^REDIS_HOST=" .env 2>/dev/null | cut -d '=' -f2 | tr -d '"' | tr -d "'")
        local redis_port=$(grep "^REDIS_PORT=" .env 2>/dev/null | cut -d '=' -f2 | tr -d '"' | tr -d "'")
        
        if [ -n "$db_host" ] && [ -n "$db_port" ] && [ "$db_host" != "postgres" ]; then
            if command -v nc &> /dev/null; then
                if nc -z "$db_host" "$db_port" 2>/dev/null; then
                    log_success "External PostgreSQL is accessible at $db_host:$db_port"
                else
                    log_warning "Cannot connect to PostgreSQL at $db_host:$db_port"
                fi
            else
                log_warning "netcat not available, cannot test PostgreSQL connectivity"
            fi
        fi
        
        if [ -n "$redis_host" ] && [ -n "$redis_port" ] && [ "$redis_host" != "redis" ]; then
            if command -v nc &> /dev/null; then
                if nc -z "$redis_host" "$redis_port" 2>/dev/null; then
                    log_success "External Redis is accessible at $redis_host:$redis_port"
                else
                    log_warning "Cannot connect to Redis at $redis_host:$redis_port"
                fi
            else
                log_warning "netcat not available, cannot test Redis connectivity"
            fi
        fi
    fi
}

check_app_key_format() {
    if [ -f .env ]; then
        local app_key=$(grep "^APP_KEY=" .env 2>/dev/null | cut -d '=' -f2 | tr -d '"' | tr -d "'")
        
        if [ -n "$app_key" ]; then
            if [[ "$app_key" =~ ^base64: ]]; then
                log_success "APP_KEY format is correct"
            else
                log_warning "APP_KEY should start with 'base64:'"
            fi
        fi
    fi
}

validate_email_config() {
    if [ -f .env ]; then
        local mail_from=$(grep "^MAIL_FROM_ADDRESS=" .env 2>/dev/null | cut -d '=' -f2 | tr -d '"' | tr -d "'")
        
        if [ -n "$mail_from" ] && [[ "$mail_from" =~ ^[^@]+@[^@]+\.[^@]+$ ]]; then
            log_success "Mail from address format is valid"
        elif [ -n "$mail_from" ]; then
            log_warning "Mail from address format may be invalid: $mail_from"
        fi
    fi
}

# Main validation
echo "DCCP Admin V2 - Configuration Validation"
echo "========================================"
echo ""

log_info "Checking required files..."
check_file_exists "Dockerfile" "Dockerfile"
check_file_exists "docker-compose.prod.yml" "Production Docker Compose file"
check_file_exists "docker-entrypoint.sh" "Docker entrypoint script"
check_file_exists "composer.json" "Composer configuration"
check_file_exists "package.json" "NPM configuration"

echo ""
log_info "Checking environment configuration..."
check_file_exists ".env" "Environment file"

if [ -f .env ]; then
    check_env_var "APP_NAME" "Application name" "false"
    check_env_var "APP_KEY" "Application key" "true"
    check_env_var "APP_URL" "Application URL" "true"
    check_env_var "DB_HOST" "Database host" "true"
    check_env_var "DB_DATABASE" "Database name" "true"
    check_env_var "DB_USERNAME" "Database username" "true"
    check_env_var "DB_PASSWORD" "Database password" "true"
    check_env_var "REDIS_HOST" "Redis host" "true"
    check_env_var "MAIL_FROM_ADDRESS" "Mail from address" "false"
    
    check_app_key_format
    validate_email_config
fi

echo ""
log_info "Checking Docker setup..."
check_docker

echo ""
log_info "Checking directory permissions..."
check_directory_permissions "storage" "Storage directory"
check_directory_permissions "bootstrap/cache" "Bootstrap cache directory"
check_directory_permissions "public" "Public directory"

echo ""
log_info "Checking external service connectivity..."
check_external_services

echo ""
echo "Validation Summary"
echo "=================="
echo -e "${GREEN}Successful checks: $SUCCESS${NC}"
echo -e "${YELLOW}Warnings: $WARNINGS${NC}"
echo -e "${RED}Errors: $ERRORS${NC}"

echo ""
if [ $ERRORS -eq 0 ]; then
    if [ $WARNINGS -eq 0 ]; then
        log_success "Configuration validation passed! Ready for deployment."
        echo ""
        echo "Next steps:"
        echo "1. Run: ./deploy.sh deploy"
        echo "2. Access your application at: http://localhost:8000"
    else
        log_warning "Configuration validation passed with warnings."
        echo ""
        echo "You can proceed with deployment, but consider addressing the warnings."
        echo "Run: ./deploy.sh deploy"
    fi
    exit 0
else
    log_error "Configuration validation failed!"
    echo ""
    echo "Please fix the errors above before proceeding with deployment."
    echo ""
    echo "Common fixes:"
    echo "- Copy .env.production to .env and configure it"
    echo "- Generate APP_KEY with: make key-generate"
    echo "- Install Docker and Docker Compose"
    echo "- Ensure external services are accessible"
    exit 1
fi