<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\StudentEnrollment;
use App\Models\User;
use App\Models\Course;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\StudentEnrollment>
 */
class StudentEnrollmentFactory extends Factory
{
    protected $model = StudentEnrollment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            "student_id" => User::factory(),
            "course_id" => Course::factory(),
            "status" => "Pending",
            "semester" => fake()->numberBetween(1, 2),
            "academic_year" => fake()->numberBetween(1, 4),
            "school_year" => fake()->year() . "-" . (fake()->year() + 1),
            "downpayment" => fake()->randomFloat(2, 1000, 5000),
            "remarks" => fake()->optional()->sentence(),
        ];
    }

    /**
     * Indicate that the enrollment is verified by cashier.
     */
    public function verifiedByCashier(): static
    {
        return $this->state(
            fn(array $attributes) => [
                "status" => "Verified By Cashier",
            ]
        );
    }

    /**
     * Indicate that the enrollment is verified by department head.
     */
    public function verifiedByDeptHead(): static
    {
        return $this->state(
            fn(array $attributes) => [
                "status" => "Verified By Dept Head",
            ]
        );
    }

    /**
     * Indicate that the enrollment is pending.
     */
    public function pending(): static
    {
        return $this->state(
            fn(array $attributes) => [
                "status" => "Pending",
            ]
        );
    }
}
