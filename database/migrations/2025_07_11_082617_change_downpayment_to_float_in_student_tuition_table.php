<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('student_tuition', function (Blueprint $table): void {
            // Change downpayment column from integer to float
            $table->float('downpayment')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('student_tuition', function (Blueprint $table): void {
            // Revert downpayment column back to integer
            $table->integer('downpayment')->nullable()->change();
        });
    }
};
