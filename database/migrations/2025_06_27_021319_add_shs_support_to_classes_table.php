<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('classes', function (Blueprint $table): void {
            // Add SHS-related columns
            $table->foreignId('shs_track_id')->nullable()->constrained('shs_tracks')->onDelete('set null');
            $table->foreignId('shs_strand_id')->nullable()->constrained('shs_strands')->onDelete('set null');
            $table->string('grade_level')->nullable(); // For SHS: "Grade 11", "Grade 12"

            // Ensure classification column exists and has proper default
            if (! Schema::hasColumn('classes', 'classification')) {
                $table->string('classification')->default('college')->after('room_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('classes', function (Blueprint $table): void {
            // Drop foreign key constraints first
            $table->dropForeign(['shs_track_id']);
            $table->dropForeign(['shs_strand_id']);

            // Drop the columns
            $table->dropColumn(['shs_track_id', 'shs_strand_id', 'grade_level']);
        });
    }
};
