<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subject_enrollments', function (Blueprint $table): void {
            // Add columns to store the actual enrolled units
            $table->unsignedInteger('enrolled_lecture_units')->nullable()->after('laboratory_fee');
            $table->unsignedInteger('enrolled_laboratory_units')->nullable()->after('enrolled_lecture_units');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subject_enrollments', function (Blueprint $table): void {
            $table->dropColumn('enrolled_lecture_units');
            $table->dropColumn('enrolled_laboratory_units');
        });
    }
};
