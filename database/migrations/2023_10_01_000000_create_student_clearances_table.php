<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_clearances', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('student_id')->constrained('students')->onDelete('cascade');
            $table->string('academic_year');
            $table->unsignedTinyInteger('semester');
            $table->boolean('is_cleared')->default(false);
            $table->text('remarks')->nullable();
            $table->string('cleared_by')->nullable();
            $table->timestamp('cleared_at')->nullable();
            $table->timestamps();

            // Ensure a student can only have one clearance record per semester per academic year
            $table->unique(['student_id', 'academic_year', 'semester']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_clearances');
    }
};
