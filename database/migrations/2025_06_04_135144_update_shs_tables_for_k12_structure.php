<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Create the shs_strands table
        Schema::create('shs_strands', function (Blueprint $table): void {
            $table->id();
            $table
                ->foreignId('track_id')
                ->constrained('shs_tracks')
                ->onDelete('cascade');
            $table->string('strand_name');
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // 2. Modify the shs_students table
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shs_students', function (Blueprint $table): void {
            // Reverse: Drop foreign keys and columns

            if (Schema::hasColumn('shs_students', 'strand_id')) {
                $table->dropForeign(['strand_id']);
                $table->dropColumn('strand_id');
            }

            // Reverse: Change birthdate back to string (or its original type)
            // This assumes it was a string. Adjust if it was another type.
            if (Schema::hasColumn('shs_students', 'birthdate')) {
                $table->string('birthdate')->nullable()->change();
            }

            // Reverse: Add the 'track' column back (IF IT WAS DROPPED)
            // $table->string('track')->nullable()->after('grade_level');
        });

        Schema::dropIfExists('shs_strands');
    }
};
