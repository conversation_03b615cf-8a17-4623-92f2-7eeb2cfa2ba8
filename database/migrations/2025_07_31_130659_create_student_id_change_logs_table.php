<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_id_change_logs', function (Blueprint $table) {
            $table->id();
            $table->string('old_student_id');
            $table->string('new_student_id');
            $table->string('student_name');
            $table->string('changed_by');
            $table->json('affected_records'); // Store the count of updated records
            $table->json('backup_data')->nullable(); // Store backup data for undo
            $table->boolean('is_undone')->default(false);
            $table->timestamp('undone_at')->nullable();
            $table->string('undone_by')->nullable();
            $table->text('reason')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['old_student_id', 'new_student_id']);
            $table->index('changed_by');
            $table->index('is_undone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_id_change_logs');
    }
};
