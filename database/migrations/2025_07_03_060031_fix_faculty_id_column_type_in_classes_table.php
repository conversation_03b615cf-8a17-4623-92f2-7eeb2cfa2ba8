<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix the faculty_id column type to be UUID instead of character varying
        // This will resolve the PostgreSQL type mismatch error when searching
        DB::statement('ALTER TABLE classes ALTER COLUMN faculty_id TYPE uuid USING faculty_id::uuid');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to character varying if needed
        DB::statement('ALTER TABLE classes ALTER COLUMN faculty_id TYPE character varying USING faculty_id::character varying');
    }
};
