<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Add optimized indexes for student section transfer operations
     */
    public function up(): void
    {
        // Optimize class_enrollments table for transfer operations
        Schema::table('class_enrollments', function (Blueprint $table): void {
            // Composite index for finding student's enrollment in a specific class
            if (!$this->indexExists('class_enrollments', 'idx_class_enrollments_student_class')) {
                $table->index(['student_id', 'class_id'], 'idx_class_enrollments_student_class');
            }

            // Index for counting enrollments per class (capacity checking)
            if (!$this->indexExists('class_enrollments', 'idx_class_enrollments_class_status')) {
                $table->index(['class_id', 'status'], 'idx_class_enrollments_class_status');
            }

            // Index for finding active enrollments by student
            if (!$this->indexExists('class_enrollments', 'idx_class_enrollments_student_status')) {
                $table->index(['student_id', 'status'], 'idx_class_enrollments_student_status');
            }
        });

        // Optimize subject_enrollments table for transfer operations
        Schema::table('subject_enrollments', function (Blueprint $table): void {
            // Composite index for finding subject enrollment during transfers
            if (!$this->indexExists('subject_enrollments', 'idx_subject_enrollments_transfer_lookup')) {
                $table->index([
                    'student_id', 
                    'class_id', 
                    'school_year', 
                    'semester'
                ], 'idx_subject_enrollments_transfer_lookup');
            }

            // Index for subject enrollment by class and semester
            if (!$this->indexExists('subject_enrollments', 'idx_subject_enrollments_class_semester')) {
                $table->index(['class_id', 'school_year', 'semester'], 'idx_subject_enrollments_class_semester');
            }

            // Index for enrollment_id lookups
            if (!$this->indexExists('subject_enrollments', 'idx_subject_enrollments_enrollment_id')) {
                $table->index('enrollment_id', 'idx_subject_enrollments_enrollment_id');
            }
        });

        // Optimize classes table for section lookup operations
        Schema::table('classes', function (Blueprint $table): void {
            // Composite index for finding classes by subject, school year, and semester
            if (!$this->indexExists('classes', 'idx_classes_subject_year_semester')) {
                $table->index([
                    'subject_code', 
                    'school_year', 
                    'semester'
                ], 'idx_classes_subject_year_semester');
            }

            // Index for subject_id lookups (new column)
            if (!$this->indexExists('classes', 'idx_classes_subject_id')) {
                $table->index('subject_id', 'idx_classes_subject_id');
            }

            // Index for faculty assignments
            if (!$this->indexExists('classes', 'idx_classes_faculty_id')) {
                $table->index('faculty_id', 'idx_classes_faculty_id');
            }

            // Index for classification filtering (college/shs)
            if (!$this->indexExists('classes', 'idx_classes_classification')) {
                $table->index('classification', 'idx_classes_classification');
            }
        });

        // Optimize student_enrollment table for related lookups
        Schema::table('student_enrollment', function (Blueprint $table): void {
            // Composite index for finding student enrollments by period
            if (!$this->indexExists('student_enrollment', 'idx_student_enrollment_period')) {
                $table->index([
                    'student_id', 
                    'school_year', 
                    'semester', 
                    'status'
                ], 'idx_student_enrollment_period');
            }

            // Index for status filtering
            if (!$this->indexExists('student_enrollment', 'idx_student_enrollment_status')) {
                $table->index('status', 'idx_student_enrollment_status');
            }
        });

        // Optimize students table for name searches and lookups
        Schema::table('students', function (Blueprint $table): void {
            // Composite index for full name searches
            if (!$this->indexExists('students', 'idx_students_name_search')) {
                $table->index(['first_name', 'last_name'], 'idx_students_name_search');
            }

            // Index for course filtering
            if (!$this->indexExists('students', 'idx_students_course_id')) {
                $table->index('course_id', 'idx_students_course_id');
            }

            // Index for student_id (unique identifier)
            if (!$this->indexExists('students', 'idx_students_student_id')) {
                $table->index('student_id', 'idx_students_student_id');
            }
        });

        // Add database-specific optimizations
        $this->addDatabaseSpecificOptimizations();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove indexes in reverse order
        Schema::table('students', function (Blueprint $table): void {
            $table->dropIndex('idx_students_name_search');
            $table->dropIndex('idx_students_course_id');
            $table->dropIndex('idx_students_student_id');
        });

        Schema::table('student_enrollment', function (Blueprint $table): void {
            $table->dropIndex('idx_student_enrollment_period');
            $table->dropIndex('idx_student_enrollment_status');
        });

        Schema::table('classes', function (Blueprint $table): void {
            $table->dropIndex('idx_classes_subject_year_semester');
            $table->dropIndex('idx_classes_subject_id');
            $table->dropIndex('idx_classes_faculty_id');
            $table->dropIndex('idx_classes_classification');
        });

        Schema::table('subject_enrollments', function (Blueprint $table): void {
            $table->dropIndex('idx_subject_enrollments_transfer_lookup');
            $table->dropIndex('idx_subject_enrollments_class_semester');
            $table->dropIndex('idx_subject_enrollments_enrollment_id');
        });

        Schema::table('class_enrollments', function (Blueprint $table): void {
            $table->dropIndex('idx_class_enrollments_student_class');
            $table->dropIndex('idx_class_enrollments_class_status');
            $table->dropIndex('idx_class_enrollments_student_status');
        });
    }

    /**
     * Check if an index exists on a table
     */
    private function indexExists(string $table, string $indexName): bool
    {
        try {
            $driver = DB::connection()->getDriverName();

            if ($driver === 'pgsql') {
                $result = DB::select("
                    SELECT 1 FROM pg_indexes
                    WHERE tablename = ? AND indexname = ?
                ", [$table, $indexName]);

                return !empty($result);
            } elseif ($driver === 'mysql') {
                $result = DB::select("
                    SELECT 1 FROM information_schema.statistics
                    WHERE table_schema = DATABASE()
                    AND table_name = ?
                    AND index_name = ?
                ", [$table, $indexName]);

                return !empty($result);
            }

            return false;
        } catch (\Exception $e) {
            // If we can't check, assume it doesn't exist and let the database handle duplicates
            return false;
        }
    }

    /**
     * Add database-specific optimizations
     */
    private function addDatabaseSpecificOptimizations(): void
    {
        $driver = DB::connection()->getDriverName();

        if ($driver === 'pgsql') {
            // PostgreSQL specific optimizations
            $this->addPostgreSQLOptimizations();
        } elseif ($driver === 'mysql') {
            // MySQL specific optimizations
            $this->addMySQLOptimizations();
        }
    }

    /**
     * Add PostgreSQL specific optimizations
     */
    private function addPostgreSQLOptimizations(): void
    {
        // Create partial indexes for active enrollments only (without CONCURRENTLY in migration)
        DB::statement("
            CREATE INDEX IF NOT EXISTS idx_class_enrollments_active_only
            ON class_enrollments (class_id, student_id)
            WHERE status = true
        ");

        DB::statement("
            CREATE INDEX IF NOT EXISTS idx_subject_enrollments_active_semester
            ON subject_enrollments (class_id, student_id)
            WHERE school_year IS NOT NULL AND semester IS NOT NULL
        ");

        // Create expression index for case-insensitive subject code searches
        DB::statement("
            CREATE INDEX IF NOT EXISTS idx_classes_subject_code_lower
            ON classes (LOWER(subject_code), school_year, semester)
        ");
    }

    /**
     * Add MySQL specific optimizations
     */
    private function addMySQLOptimizations(): void
    {
        // MySQL doesn't support partial indexes, but we can optimize with covering indexes
        DB::statement("
            CREATE INDEX idx_class_enrollments_covering 
            ON class_enrollments (class_id, student_id, status, created_at)
        ");

        DB::statement("
            CREATE INDEX idx_subject_enrollments_covering
            ON subject_enrollments (student_id, class_id, school_year, semester, enrollment_id)
        ");
    }
};
