<?php

declare(strict_types=1);

use App\Models\Subject;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Clean up trailing spaces in subject codes
        // This will help prevent future issues with subject code matching
        
        echo "Cleaning up trailing spaces in subject codes...\n";
        
        // Get all subjects with trailing spaces
        $subjectsWithSpaces = Subject::whereRaw('code != TRIM(code)')->get();
        
        foreach ($subjectsWithSpaces as $subject) {
            $originalCode = $subject->code;
            $trimmedCode = mb_trim($subject->code);
            
            // Check if a subject with the trimmed code already exists in the same course
            $existingSubject = Subject::where('code', $trimmedCode)
                ->where('course_id', $subject->course_id)
                ->where('id', '!=', $subject->id)
                ->first();
            
            if ($existingSubject) {
                echo "Warning: Subject ID {$subject->id} ('{$originalCode}') cannot be trimmed to '{$trimmedCode}' because it would conflict with existing subject ID {$existingSubject->id} in course {$subject->course_id}\n";
                // Keep the trailing space to maintain uniqueness
                continue;
            }
            
            // Safe to trim - update the subject code
            $subject->code = $trimmedCode;
            $subject->save();
            
            echo "Updated subject ID {$subject->id}: '{$originalCode}' -> '{$trimmedCode}' (Course: {$subject->course_id})\n";
        }
        
        // Also clean up trailing spaces in classes table subject_code field
        echo "\nCleaning up trailing spaces in classes subject_code...\n";
        
        $classesWithSpaces = DB::table('classes')
            ->whereRaw('subject_code != TRIM(subject_code)')
            ->get();
        
        foreach ($classesWithSpaces as $class) {
            $originalCode = $class->subject_code;
            $trimmedCode = mb_trim($class->subject_code);
            
            DB::table('classes')
                ->where('id', $class->id)
                ->update(['subject_code' => $trimmedCode]);
            
            echo "Updated class ID {$class->id}: subject_code '{$originalCode}' -> '{$trimmedCode}'\n";
        }
        
        echo "\nTrailing space cleanup completed.\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is for data cleanup, no need to reverse
        echo "This migration cleaned up trailing spaces and cannot be reversed.\n";
    }
};
