<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, set person_id to NULL for Faculty records since they use email-based relationships
        DB::statement("UPDATE accounts SET person_id = NULL WHERE person_type = 'App\\Models\\Faculty'");
        
        // Also handle any UUID values (Faculty IDs) by setting them to NULL
        DB::statement("UPDATE accounts SET person_id = NULL WHERE person_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'");
        
        // Now convert the person_id column type to bigint for Student records
        // This will resolve the PostgreSQL type mismatch error when querying accounts by person_id
        DB::statement('ALTER TABLE accounts ALTER COLUMN person_id TYPE bigint USING person_id::bigint');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to character varying if needed
        DB::statement('ALTER TABLE accounts ALTER COLUMN person_id TYPE character varying USING person_id::character varying');
    }
};
