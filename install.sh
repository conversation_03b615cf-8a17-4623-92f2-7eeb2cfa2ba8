#!/bin/bash

# DCCP Admin V2 - Universal Installation Script
# This script automatically installs DCCP Admin V2 with all dependencies
# Supports: Ubuntu, Debian, CentOS, RHEL, Fedora, macOS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REPO_URL="https://github.com/yukazakiri/DccpAdminV2.git"
APP_DIR="DccpAdminV2"
DB_NAME="dccpadminv2"
DB_USER="dccp_user"
DB_PASS=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect operating system
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/debian_version ]; then
            OS="debian"
            DISTRO=$(lsb_release -si 2>/dev/null || echo "Debian")
        elif [ -f /etc/redhat-release ]; then
            OS="redhat"
            DISTRO=$(cat /etc/redhat-release | awk '{print $1}')
        else
            OS="linux"
            DISTRO="Unknown"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        DISTRO="macOS"
    else
        OS="unknown"
        DISTRO="Unknown"
    fi
    
    log_info "Detected OS: $DISTRO ($OS)"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "Running as root. This is not recommended for development."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Install Docker
install_docker() {
    log_info "Installing Docker..."
    
    if command -v docker &> /dev/null; then
        log_success "Docker is already installed"
        return
    fi
    
    case $OS in
        "debian")
            curl -fsSL https://get.docker.com -o get-docker.sh
            sudo sh get-docker.sh
            sudo usermod -aG docker $USER
            ;;
        "redhat")
            sudo dnf install -y docker docker-compose
            sudo systemctl start docker
            sudo systemctl enable docker
            sudo usermod -aG docker $USER
            ;;
        "macos")
            if ! command -v brew &> /dev/null; then
                log_info "Installing Homebrew..."
                /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            fi
            brew install --cask docker
            ;;
    esac
    
    log_success "Docker installed successfully"
}

# Install Docker Compose
install_docker_compose() {
    log_info "Installing Docker Compose..."
    
    if command -v docker-compose &> /dev/null; then
        log_success "Docker Compose is already installed"
        return
    fi
    
    case $OS in
        "debian"|"redhat")
            sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
            ;;
        "macos")
            # Docker Desktop includes Docker Compose
            log_success "Docker Compose included with Docker Desktop"
            ;;
    esac
    
    log_success "Docker Compose installed successfully"
}

# Install Git
install_git() {
    log_info "Installing Git..."
    
    if command -v git &> /dev/null; then
        log_success "Git is already installed"
        return
    fi
    
    case $OS in
        "debian")
            sudo apt update
            sudo apt install -y git
            ;;
        "redhat")
            sudo dnf install -y git
            ;;
        "macos")
            brew install git
            ;;
    esac
    
    log_success "Git installed successfully"
}

# Clone repository
clone_repository() {
    log_info "Cloning DCCP Admin V2 repository..."
    
    if [ -d "$APP_DIR" ]; then
        log_warning "Directory $APP_DIR already exists"
        read -p "Remove and re-clone? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$APP_DIR"
        else
            cd "$APP_DIR"
            git pull origin main
            log_success "Repository updated"
            return
        fi
    fi
    
    git clone "$REPO_URL" "$APP_DIR"
    cd "$APP_DIR"
    log_success "Repository cloned successfully"
}

# Setup environment
setup_environment() {
    log_info "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        log_success "Environment file created"
    else
        log_warning "Environment file already exists"
    fi
    
    # Generate random passwords and keys
    APP_KEY="base64:$(openssl rand -base64 32)"
    
    # Update environment file
    sed -i.bak "s|APP_NAME=.*|APP_NAME=\"DCCP Admin V2\"|" .env
    sed -i.bak "s|APP_ENV=.*|APP_ENV=production|" .env
    sed -i.bak "s|APP_DEBUG=.*|APP_DEBUG=false|" .env
    sed -i.bak "s|APP_KEY=.*|APP_KEY=$APP_KEY|" .env
    sed -i.bak "s|DB_DATABASE=.*|DB_DATABASE=$DB_NAME|" .env
    sed -i.bak "s|DB_USERNAME=.*|DB_USERNAME=$DB_USER|" .env
    sed -i.bak "s|DB_PASSWORD=.*|DB_PASSWORD=$DB_PASS|" .env
    
    log_success "Environment configured"
}

# Start services
start_services() {
    log_info "Starting Docker services..."
    
    # Pull latest images
    docker-compose pull
    
    # Start services
    docker-compose up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to start..."
    sleep 30
    
    log_success "Services started successfully"
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Wait for database to be ready
    docker-compose exec -T app php artisan migrate:status || sleep 10
    
    # Run migrations
    docker-compose exec -T app php artisan migrate --force
    
    # Seed database
    docker-compose exec -T app php artisan db:seed --force
    
    log_success "Database migrations completed"
}

# Create admin user
create_admin_user() {
    log_info "Creating admin user..."
    
    echo "Please provide admin user details:"
    read -p "Name: " ADMIN_NAME
    read -p "Email: " ADMIN_EMAIL
    read -s -p "Password: " ADMIN_PASSWORD
    echo
    
    docker-compose exec -T app php artisan make:filament-user \
        --name="$ADMIN_NAME" \
        --email="$ADMIN_EMAIL" \
        --password="$ADMIN_PASSWORD"
    
    log_success "Admin user created successfully"
}

# Display completion message
show_completion() {
    log_success "🎉 DCCP Admin V2 installation completed successfully!"
    echo
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${GREEN}📋 Installation Summary${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    echo -e "${BLUE}🌐 Application URL:${NC} http://localhost:8000"
    echo -e "${BLUE}🔧 Admin Panel:${NC} http://localhost:8000/admin"
    echo -e "${BLUE}📚 Knowledge Base:${NC} http://localhost:8000/kb"
    echo -e "${BLUE}📖 API Docs:${NC} http://localhost:8000/docs"
    echo
    echo -e "${BLUE}👤 Admin Credentials:${NC}"
    echo -e "   Email: $ADMIN_EMAIL"
    echo -e "   Password: [as entered]"
    echo
    echo -e "${BLUE}🗄️ Database:${NC}"
    echo -e "   Name: $DB_NAME"
    echo -e "   User: $DB_USER"
    echo -e "   Password: $DB_PASS"
    echo
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${YELLOW}📝 Next Steps:${NC}"
    echo "1. Access the admin panel and configure system settings"
    echo "2. Set up courses, subjects, and fee structures"
    echo "3. Create user accounts for staff"
    echo "4. Import student data"
    echo "5. Test the enrollment process"
    echo
    echo -e "${YELLOW}🔧 Useful Commands:${NC}"
    echo "• View logs: docker-compose logs -f"
    echo "• Stop services: docker-compose down"
    echo "• Restart services: docker-compose restart"
    echo "• Access shell: docker-compose exec app bash"
    echo
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# Main installation function
main() {
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${GREEN}🎓 DCCP Admin V2 - Automated Installation${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    
    detect_os
    check_root
    
    log_info "Starting installation process..."
    
    install_git
    install_docker
    install_docker_compose
    clone_repository
    setup_environment
    start_services
    run_migrations
    create_admin_user
    
    show_completion
}

# Run main function
main "$@"
