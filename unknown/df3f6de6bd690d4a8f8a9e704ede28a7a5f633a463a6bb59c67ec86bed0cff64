# DCCP Admin - Portainer Deployment Guide

This guide will help you deploy DCCP Admin using Portainer with Docker Compose stacks.

## Prerequisites

- Portainer CE/EE installed and running
- Docker environment with at least 2GB RAM available
- External PostgreSQL and Redis services (recommended) OR use included services

## Quick Start

### 1. Choose Your Deployment Method

#### Option A: Using Docker Volumes (Recommended)
Use `portainer-stack.yml` - data stored in Docker volumes, easier setup.

#### Option B: Using Host Bind Mounts
Use `portainer-stack-with-bind-mounts.yml` - data stored on host filesystem.

### 2. Prepare Environment (For Bind Mounts Only)

If using bind mounts, run this on your Docker host:

```bash
# Download and run the setup script
curl -O https://raw.githubusercontent.com/YOUR_USERNAME/dccp-admin/main/setup-storage.sh
chmod +x setup-storage.sh
sudo ./setup-storage.sh
```

Or manually create directories:
```bash
sudo mkdir -p /opt/dccp-admin/storage/{app,logs,framework/{cache,sessions,views},app/public}
sudo chmod -R 755 /opt/dccp-admin/storage
```

### 3. Deploy in Portainer

1. **Navigate to Stacks** in Portainer
2. **Click "Add Stack"**
3. **Name your stack**: `dccp-admin`
4. **Choose deployment method**:
   - **Web editor**: Copy content from `portainer-stack.yml`
   - **Upload**: Upload the stack file
   - **Git repository**: Point to your repository

### 4. Configure Environment Variables

Set these required variables in Portainer:

#### Essential Configuration
```env
# Application
APP_KEY=base64:your-32-character-key-here
APP_URL=https://your-domain.com
APP_NAME=DCCP Admin

# Database (External PostgreSQL)
DB_HOST=your-postgres-host
DB_PORT=5432
DB_DATABASE=dccp_admin
DB_USERNAME=dccp_user
DB_PASSWORD=your-secure-password

# Redis (External)
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-smtp-user
MAIL_PASSWORD=your-smtp-password
MAIL_FROM_ADDRESS=<EMAIL>
```

#### Optional Configuration
```env
# Custom Image (if using your own build)
APP_IMAGE=your-registry/dccp-admin:latest

# Port Configuration
APP_PORT=8000

# Logging
LOG_LEVEL=warning

# Storage Path (for bind mounts only)
STORAGE_PATH=/opt/dccp-admin/storage

# Resource Limits
WORKER_CPU_LIMIT=1.0
WORKER_MEMORY_LIMIT=512M

# First-time setup
RUN_MIGRATIONS=true
```

### 5. Profile Selection

Choose services based on your setup:

#### External Services (Recommended)
Deploy with default profile (no additional services)

#### Include Internal Services
Add these profiles in Portainer:
- `with-redis` - Include Redis container
- `with-postgres` - Include PostgreSQL container
- `with-redis,with-postgres` - Include both

### 6. Deploy the Stack

1. **Set environment variables**
2. **Select profiles** (if using internal services)
3. **Click "Deploy the stack"**

## Post-Deployment Setup

### 1. Generate Application Key (First Time)

If you didn't set `APP_KEY`, generate one:

```bash
# Connect to the app container
docker exec -it dccp-admin-app bash

# Generate key
php artisan key:generate --show

# Exit container
exit
```

Update your stack with the generated key.

### 2. Run Database Migrations

Either set `RUN_MIGRATIONS=true` in environment variables, or run manually:

```bash
docker exec -it dccp-admin-app php artisan migrate --force
```

### 3. Create Admin User

```bash
docker exec -it dccp-admin-app php artisan make:filament-user
```

## Service Configuration

### Main Application (`app`)
- **Port**: 8000 (configurable via `APP_PORT`)
- **Health check**: `/health` endpoint
- **Resources**: 2 CPU / 1GB RAM limit

### Queue Worker (`worker`)
- **Purpose**: Background job processing
- **Command**: `php artisan queue:work`
- **Resources**: 1 CPU / 512MB RAM limit

### Redis (Optional)
- **Port**: 6379 (configurable via `REDIS_EXTERNAL_PORT`)
- **Data**: Persisted in `redis_data` volume
- **Profile**: `with-redis`

### PostgreSQL (Optional)
- **Port**: 5432 (configurable via `POSTGRES_EXTERNAL_PORT`)
- **Data**: Persisted in `postgres_data` volume
- **Profile**: `with-postgres`

## Volume Management

### Docker Volumes (Default)
- `app_storage` - Application files
- `app_logs` - Application logs
- `app_cache` - Framework cache
- `app_sessions` - User sessions
- `app_views` - Compiled views
- `app_public` - Public uploads

### Bind Mounts (Alternative)
Controlled by `STORAGE_PATH` environment variable:
- Default: `/opt/dccp-admin/storage`
- Requires host directory setup

## Monitoring & Maintenance

### Health Checks
- **Application**: `curl http://localhost:8000/health`
- **Redis**: `redis-cli ping`
- **PostgreSQL**: `pg_isready`

### Logs Access
```bash
# Application logs
docker logs dccp-admin-app

# Worker logs
docker logs dccp-admin-worker

# All stack logs
docker-compose logs -f
```

### Scaling Worker
Increase worker replicas in Portainer or add more worker services.

## Troubleshooting

### Common Issues

#### 1. Volume Mount Errors
```
failed to mount local volume: no such file or directory
```
**Solution**: Run setup script or switch to Docker volumes

#### 2. Database Connection Failed
**Check**: Database credentials, network connectivity, service health

#### 3. Redis Connection Failed
**Check**: Redis credentials, network connectivity, service availability

#### 4. Permission Denied
```bash
# Fix storage permissions
docker exec -it dccp-admin-app chown -R www-data:www-data storage bootstrap/cache
```

### Stack Recreation
To completely recreate the stack:
1. Stop and remove stack in Portainer
2. Remove volumes (if desired): `docker volume prune`
3. Redeploy with fresh configuration

## Security Considerations

1. **Use strong passwords** for all services
2. **Enable TLS/SSL** with reverse proxy
3. **Limit network exposure** - only expose necessary ports
4. **Regular updates** - monitor for security updates
5. **Backup strategy** - backup volumes and database regularly

## Production Recommendations

1. **External databases** - Use managed PostgreSQL/Redis
2. **Load balancer** - Use nginx/Traefik for SSL termination
3. **Monitoring** - Add Prometheus/Grafana stack
4. **Backups** - Automated backup solutions
5. **Secrets management** - Use Docker secrets or external vaults

For advanced configurations and troubleshooting, refer to the main documentation.