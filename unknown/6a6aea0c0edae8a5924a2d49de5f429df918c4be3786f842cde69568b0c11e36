version: '3.8'

services:
  # <PERSON>vel Application
  app:
    image: ghcr.io/yukazakiri/dccpadminv2:latest
    container_name: dccp-admin-app
    restart: unless-stopped
    tty: true
    env_file:
      - .env
    environment:
      - DB_HOST=db
      - REDIS_HOST=redis
      - SESSION_DRIVER=redis
    ports:
      - "8000:8000"
    networks:
      - dccp-network
    depends_on:
      - db
      - redis

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: dccp-admin-db
    restart: unless-stopped
    tty: true
    environment:
      POSTGRES_DB: ${DB_DATABASE}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - dbdata:/var/lib/postgresql/data
    networks:
      - dccp-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: dccp-admin-redis
    restart: unless-stopped
    networks:
      - dccp-network

# Networks
networks:
  dccp-network:
    driver: bridge

# Volumes
volumes:
  dbdata:
    driver: local
