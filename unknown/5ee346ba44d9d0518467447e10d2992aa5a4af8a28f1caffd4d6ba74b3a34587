# DCCP Admin V2 - Windows Installation Script
# PowerShell script for automated installation on Windows

param(
    [switch]$SkipPrerequisites,
    [string]$InstallPath = "C:\DccpAdminV2",
    [switch]$Development
)

# Set execution policy for this session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Colors for output
$Colors = @{
    Info = "Cyan"
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Type = "Info"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] " -NoNewline
    Write-Host $Message -ForegroundColor $Colors[$Type]
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Install-Chocolatey {
    Write-Log "Installing Chocolatey package manager..." "Info"
    
    if (Get-Command choco -ErrorAction SilentlyContinue) {
        Write-Log "Chocolatey is already installed" "Success"
        return
    }
    
    try {
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Write-Log "Chocolatey installed successfully" "Success"
    }
    catch {
        Write-Log "Failed to install Chocolatey: $($_.Exception.Message)" "Error"
        exit 1
    }
}

function Install-Prerequisites {
    Write-Log "Installing prerequisites..." "Info"
    
    $packages = @(
        "docker-desktop",
        "git",
        "nodejs",
        "php",
        "composer"
    )
    
    foreach ($package in $packages) {
        Write-Log "Installing $package..." "Info"
        try {
            choco install $package -y --no-progress
            Write-Log "$package installed successfully" "Success"
        }
        catch {
            Write-Log "Failed to install $package: $($_.Exception.Message)" "Warning"
        }
    }
    
    Write-Log "Prerequisites installation completed" "Success"
}

function Start-DockerDesktop {
    Write-Log "Starting Docker Desktop..." "Info"
    
    $dockerPath = "${env:ProgramFiles}\Docker\Docker\Docker Desktop.exe"
    if (Test-Path $dockerPath) {
        Start-Process $dockerPath
        Write-Log "Docker Desktop started. Please wait for it to fully initialize..." "Info"
        
        # Wait for Docker to be ready
        $timeout = 120
        $elapsed = 0
        do {
            Start-Sleep 5
            $elapsed += 5
            try {
                docker version | Out-Null
                $dockerReady = $true
            }
            catch {
                $dockerReady = $false
            }
        } while (-not $dockerReady -and $elapsed -lt $timeout)
        
        if ($dockerReady) {
            Write-Log "Docker is ready" "Success"
        }
        else {
            Write-Log "Docker failed to start within timeout period" "Warning"
        }
    }
    else {
        Write-Log "Docker Desktop not found. Please install it manually." "Error"
        exit 1
    }
}

function Clone-Repository {
    Write-Log "Cloning DCCP Admin V2 repository..." "Info"
    
    if (Test-Path $InstallPath) {
        Write-Log "Directory $InstallPath already exists" "Warning"
        $response = Read-Host "Remove and re-clone? (y/N)"
        if ($response -eq 'y' -or $response -eq 'Y') {
            Remove-Item $InstallPath -Recurse -Force
        }
        else {
            Set-Location $InstallPath
            git pull origin main
            Write-Log "Repository updated" "Success"
            return
        }
    }
    
    try {
        git clone "https://github.com/yukazakiri/DccpAdminV2.git" $InstallPath
        Set-Location $InstallPath
        Write-Log "Repository cloned successfully" "Success"
    }
    catch {
        Write-Log "Failed to clone repository: $($_.Exception.Message)" "Error"
        exit 1
    }
}

function Setup-Environment {
    Write-Log "Setting up environment configuration..." "Info"
    
    if (-not (Test-Path ".env")) {
        Copy-Item ".env.example" ".env"
        Write-Log "Environment file created" "Success"
    }
    else {
        Write-Log "Environment file already exists" "Warning"
    }
    
    # Generate random values
    $appKey = "base64:" + [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((New-Guid).ToString()))
    $dbPass = -join ((1..25) | ForEach {Get-Random -input ([char[]]([char]'a'..[char]'z') + [char[]]([char]'A'..[char]'Z') + [char[]]([char]'0'..[char]'9'))})
    
    # Update environment file
    $envContent = Get-Content ".env"
    $envContent = $envContent -replace 'APP_NAME=.*', 'APP_NAME="DCCP Admin V2"'
    $envContent = $envContent -replace 'APP_ENV=.*', 'APP_ENV=production'
    $envContent = $envContent -replace 'APP_DEBUG=.*', 'APP_DEBUG=false'
    $envContent = $envContent -replace 'APP_KEY=.*', "APP_KEY=$appKey"
    $envContent = $envContent -replace 'DB_DATABASE=.*', 'DB_DATABASE=dccpadminv2'
    $envContent = $envContent -replace 'DB_USERNAME=.*', 'DB_USERNAME=dccp_user'
    $envContent = $envContent -replace 'DB_PASSWORD=.*', "DB_PASSWORD=$dbPass"
    
    $envContent | Set-Content ".env"
    
    Write-Log "Environment configured" "Success"
    return $dbPass
}

function Start-Services {
    Write-Log "Starting Docker services..." "Info"
    
    try {
        # Pull latest images
        docker-compose pull
        
        # Start services
        docker-compose up -d
        
        # Wait for services to be ready
        Write-Log "Waiting for services to start..." "Info"
        Start-Sleep 30
        
        Write-Log "Services started successfully" "Success"
    }
    catch {
        Write-Log "Failed to start services: $($_.Exception.Message)" "Error"
        exit 1
    }
}

function Run-Migrations {
    Write-Log "Running database migrations..." "Info"
    
    try {
        # Wait for database to be ready
        Start-Sleep 10
        
        # Run migrations
        docker-compose exec -T app php artisan migrate --force
        
        # Seed database
        docker-compose exec -T app php artisan db:seed --force
        
        Write-Log "Database migrations completed" "Success"
    }
    catch {
        Write-Log "Failed to run migrations: $($_.Exception.Message)" "Error"
        exit 1
    }
}

function Create-AdminUser {
    Write-Log "Creating admin user..." "Info"
    
    $adminName = Read-Host "Enter admin name"
    $adminEmail = Read-Host "Enter admin email"
    $adminPassword = Read-Host "Enter admin password" -AsSecureString
    $adminPasswordPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($adminPassword))
    
    try {
        docker-compose exec -T app php artisan make:filament-user --name="$adminName" --email="$adminEmail" --password="$adminPasswordPlain"
        Write-Log "Admin user created successfully" "Success"
        return @{
            Name = $adminName
            Email = $adminEmail
        }
    }
    catch {
        Write-Log "Failed to create admin user: $($_.Exception.Message)" "Error"
        exit 1
    }
}

function Show-Completion {
    param(
        [hashtable]$AdminUser,
        [string]$DbPassword
    )
    
    Write-Log "🎉 DCCP Admin V2 installation completed successfully!" "Success"
    Write-Host ""
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Green
    Write-Host "📋 Installation Summary" -ForegroundColor Green
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Application URL: " -NoNewline -ForegroundColor Cyan
    Write-Host "http://localhost:8000"
    Write-Host "🔧 Admin Panel: " -NoNewline -ForegroundColor Cyan
    Write-Host "http://localhost:8000/admin"
    Write-Host "📚 Knowledge Base: " -NoNewline -ForegroundColor Cyan
    Write-Host "http://localhost:8000/kb"
    Write-Host "📖 API Docs: " -NoNewline -ForegroundColor Cyan
    Write-Host "http://localhost:8000/docs"
    Write-Host ""
    Write-Host "👤 Admin Credentials:" -ForegroundColor Cyan
    Write-Host "   Email: $($AdminUser.Email)"
    Write-Host "   Name: $($AdminUser.Name)"
    Write-Host ""
    Write-Host "🗄️ Database:" -ForegroundColor Cyan
    Write-Host "   Name: dccpadminv2"
    Write-Host "   User: dccp_user"
    Write-Host "   Password: $DbPassword"
    Write-Host ""
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Green
    Write-Host "📝 Next Steps:" -ForegroundColor Yellow
    Write-Host "1. Access the admin panel and configure system settings"
    Write-Host "2. Set up courses, subjects, and fee structures"
    Write-Host "3. Create user accounts for staff"
    Write-Host "4. Import student data"
    Write-Host "5. Test the enrollment process"
    Write-Host ""
    Write-Host "🔧 Useful Commands:" -ForegroundColor Yellow
    Write-Host "• View logs: docker-compose logs -f"
    Write-Host "• Stop services: docker-compose down"
    Write-Host "• Restart services: docker-compose restart"
    Write-Host "• Access shell: docker-compose exec app bash"
    Write-Host ""
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Green
}

# Main installation function
function Main {
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Green
    Write-Host "🎓 DCCP Admin V2 - Windows Installation" -ForegroundColor Green
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Green
    Write-Host ""
    
    if (-not (Test-Administrator)) {
        Write-Log "This script requires administrator privileges. Please run as administrator." "Error"
        exit 1
    }
    
    Write-Log "Starting installation process..." "Info"
    
    if (-not $SkipPrerequisites) {
        Install-Chocolatey
        Install-Prerequisites
        Start-DockerDesktop
    }
    
    Clone-Repository
    $dbPassword = Setup-Environment
    Start-Services
    Run-Migrations
    $adminUser = Create-AdminUser
    
    Show-Completion -AdminUser $adminUser -DbPassword $dbPassword
}

# Run main function
Main
