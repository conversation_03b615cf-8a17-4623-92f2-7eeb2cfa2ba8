# DCCP Admin V2 - Docker Deployment Guide

This guide provides comprehensive instructions for deploying the DCCP Admin V2 Laravel application using Docker with external Redis and PostgreSQL services.

## Overview

The Docker setup includes:
- **Laravel Application**: Running on FrankenPHP with Laravel Octane
- **External PostgreSQL**: Database service
- **External Redis**: Cache, session, and queue storage
- **Health Monitoring**: Built-in health checks
- **Production Optimizations**: Multi-stage builds, caching, and security

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 2GB RAM available
- Network connectivity to external services

## Quick Start

1. **Clone and navigate to the project:**
```bash
cd /path/to/DccpAdminV2
```

2. **Run the deployment script:**
```bash
./deploy.sh deploy
```

This will automatically:
- Build the production Docker image
- Set up environment configuration
- Start all services
- Run database migrations
- Optimize the application
- Perform health checks

## Manual Deployment Steps

### 1. Environment Configuration

Copy the production environment template:
```bash
cp .env.production .env
```

Update the following critical variables in `.env`:

```bash
# Application
APP_KEY=base64:your_generated_key_here
APP_URL=https://your-domain.com

# External PostgreSQL
DB_HOST=your-postgres-host
DB_PORT=5432
DB_DATABASE=dccpadminv2
DB_USERNAME=your_db_user
DB_PASSWORD=your_secure_password

# External Redis
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_email_password
MAIL_FROM_ADDRESS=<EMAIL>
```

### 2. Generate Application Key

```bash
./deploy.sh build
docker run --rm -v $(pwd):/app -w /app dccp-admin:latest php artisan key:generate --show
```

Update the `APP_KEY` in your `.env` file with the generated key.

### 3. Build Docker Image

Build the production-optimized image:
```bash
./deploy.sh build-prod
```

Or manually:
```bash
docker build --target production -t dccp-admin:production .
```

### 4. Start Services

For deployment with included Redis and PostgreSQL:
```bash
docker-compose -f docker-compose.prod.yml up -d
```

For deployment with external services only:
```bash
docker-compose -f docker-compose.external.yml up -d
```

### 5. Run Database Migrations

```bash
./deploy.sh migrate
```

Or manually:
```bash
docker exec dccp-admin-app php artisan migrate --force
```

### 6. Optimize Application

```bash
./deploy.sh optimize
```

## Deployment Scripts

The `deploy.sh` script provides several commands:

| Command | Description |
|---------|-------------|
| `deploy` | Full deployment process |
| `build` | Build Docker image |
| `build-prod` | Build production image |
| `start` | Start services |
| `stop` | Stop services |
| `restart` | Restart services |
| `logs` | View application logs |
| `migrate` | Run database migrations |
| `optimize` | Optimize Laravel application |
| `health` | Check application health |
| `status` | Show service status |
| `cleanup` | Clean up Docker resources |

## Configuration Options

### Docker Compose Files

- **docker-compose.prod.yml**: Complete setup with included Redis and PostgreSQL
- **docker-compose.external.yml**: App-only setup for external services

### Environment Variables

#### Application Settings
```bash
APP_NAME="DCCP Admin"
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost:8000
```

#### Database Configuration
```bash
DB_CONNECTION=pgsql
DB_HOST=postgres
DB_PORT=5432
DB_DATABASE=dccpadminv2
DB_USERNAME=dccp_user
DB_PASSWORD=secure_password
```

#### Redis Configuration
```bash
REDIS_CLIENT=phpredis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
CACHE_STORE=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
```

#### Docker-Specific Settings
```bash
APP_PORT=8000                    # External port mapping
REDIS_EXTERNAL_PORT=6379         # Redis external port
POSTGRES_EXTERNAL_PORT=5432      # PostgreSQL external port
RUN_MIGRATIONS=false             # Auto-run migrations on start
```

## External Services Setup

### PostgreSQL Requirements

Your external PostgreSQL instance should:
- Be version 12 or higher
- Have the required database created
- Allow connections from the Docker container
- Have proper user permissions set

Example PostgreSQL setup:
```sql
CREATE DATABASE dccpadminv2;
CREATE USER dccp_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE dccpadminv2 TO dccp_user;
```

### Redis Requirements

Your external Redis instance should:
- Be version 6 or higher
- Allow connections from the Docker container
- Have sufficient memory allocated
- Optionally have persistence enabled

## Networking

### Default Network Configuration

The application creates a `dccp-network` bridge network with subnet `**********/16`.

### External Service Connectivity

Ensure your external services are accessible from:
- Docker container IP range
- Host machine (if services are on the same host)
- Network firewall rules allow required ports

### Port Mappings

| Service | Internal Port | External Port | Purpose |
|---------|---------------|---------------|---------|
| Laravel App | 8000 | 8000 | Web application |
| PostgreSQL | 5432 | 5432 | Database (if included) |
| Redis | 6379 | 6379 | Cache/Sessions (if included) |

## Monitoring and Health Checks

### Health Check Endpoint

The application provides a health check at `/health`:
```bash
curl http://localhost:8000/health
```

Response format:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000000Z",
  "services": {
    "database": "connected",
    "redis": "connected"
  }
}
```

### Container Health Monitoring

Docker health checks are configured to:
- Check every 30 seconds
- Timeout after 10 seconds
- Retry 3 times before marking unhealthy
- Wait 60 seconds before starting checks

### Viewing Logs

```bash
# Application logs
./deploy.sh logs

# Specific service logs
docker-compose -f docker-compose.prod.yml logs app
docker-compose -f docker-compose.prod.yml logs redis
docker-compose -f docker-compose.prod.yml logs postgres

# Follow logs in real-time
docker-compose -f docker-compose.prod.yml logs -f app
```

## Performance Optimization

### PHP Configuration

The production PHP configuration includes:
- OPcache enabled with optimized settings
- Increased memory limits
- Realpath cache optimization
- Production error reporting

### Laravel Optimizations

The deployment automatically runs:
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
php artisan optimize
```

### Docker Optimizations

- Multi-stage builds for smaller image size
- Non-root user for security
- Optimized layer caching
- Production-only dependencies

## Security Considerations

### Container Security

- Runs as non-root user (UID 1000)
- Minimal attack surface with Alpine Linux
- Production PHP configuration
- Security headers enabled

### Network Security

- Internal Docker network isolation
- No unnecessary port exposure
- Configurable external access

### Data Security

- Persistent volumes for data
- Secure environment variable handling
- Database connection encryption support

## Backup and Recovery

### Database Backups

For included PostgreSQL:
```bash
docker exec dccp-admin-postgres pg_dump -U dccp_user dccpadminv2 > backup.sql
```

For external PostgreSQL, use your existing backup procedures.

### Application Data Backups

```bash
# Backup storage volumes
docker run --rm -v dccp-admin_app_storage:/data -v $(pwd):/backup alpine tar czf /backup/storage-backup.tar.gz -C /data .

# Backup application logs
docker run --rm -v dccp-admin_app_logs:/data -v $(pwd):/backup alpine tar czf /backup/logs-backup.tar.gz -C /data .
```

### Redis Data Backup

For included Redis:
```bash
docker exec dccp-admin-redis redis-cli BGSAVE
docker cp dccp-admin-redis:/data/dump.rdb ./redis-backup.rdb
```

## Troubleshooting

### Common Issues

1. **Application won't start**
   - Check environment variables
   - Verify external service connectivity
   - Review container logs

2. **Database connection failed**
   - Verify PostgreSQL host and credentials
   - Check network connectivity
   - Ensure database exists

3. **Redis connection failed**
   - Verify Redis host and port
   - Check authentication if required
   - Test network connectivity

4. **Permission errors**
   - Ensure proper volume permissions
   - Check user/group IDs

### Debugging Commands

```bash
# Check service status
./deploy.sh status

# View detailed logs
docker-compose -f docker-compose.prod.yml logs --details

# Execute commands in container
docker exec -it dccp-admin-app bash

# Test external connectivity
docker exec dccp-admin-app nc -z postgres-host 5432
docker exec dccp-admin-app nc -z redis-host 6379

# Check Laravel configuration
docker exec dccp-admin-app php artisan config:show database
docker exec dccp-admin-app php artisan config:show cache
```

### Log Locations

- Application logs: `/app/storage/logs/`
- PHP error logs: `/var/log/php_errors.log`
- Container logs: `docker logs dccp-admin-app`

## Production Deployment Checklist

- [ ] Environment variables configured
- [ ] APP_KEY generated and set
- [ ] External PostgreSQL accessible and configured
- [ ] External Redis accessible and configured
- [ ] SSL/TLS certificates configured (if using HTTPS)
- [ ] Firewall rules configured
- [ ] Backup procedures established
- [ ] Monitoring setup completed
- [ ] Health checks verified
- [ ] Performance testing completed

## Support and Maintenance

### Regular Maintenance

- Monitor disk usage for logs and storage
- Regular database backups
- Update Docker images periodically
- Monitor application performance
- Review security logs

### Updates

To update the application:
1. Pull latest code changes
2. Rebuild Docker image
3. Stop current services
4. Start updated services
5. Run any new migrations

```bash
git pull origin main
./deploy.sh build-prod
./deploy.sh restart
./deploy.sh migrate
```

For questions or issues, refer to the application documentation or contact the development team.