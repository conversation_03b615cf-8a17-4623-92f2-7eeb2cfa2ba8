#!/bin/bash

# DCCP Admin V2 Deployment Script
# This script helps build and deploy the Laravel application with Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="dccp-admin"
CONTAINER_NAME="dccp-admin-app"
NETWORK_NAME="dccp-network"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    log_success "Requirements check passed"
}

setup_environment() {
    log_info "Setting up environment..."
    
    if [ ! -f .env ]; then
        if [ -f .env.production ]; then
            log_info "Copying .env.production to .env"
            cp .env.production .env
        else
            log_warning "No .env file found. Copying from .env.example"
            cp .env.example .env
        fi
        
        log_warning "Please configure your .env file with proper values"
        log_warning "Especially: APP_KEY, DB_* and REDIS_* settings"
    fi
}

generate_app_key() {
    log_info "Generating application key..."
    
    if [ -z "$(grep '^APP_KEY=' .env | cut -d '=' -f2)" ]; then
        log_info "APP_KEY is empty, generating new key..."
        
        # Generate key using Laravel container
        docker run --rm -v $(pwd):/app -w /app \
            dunglas/frankenphp:1.4.0-php8.3-alpine \
            sh -c "composer install --no-dev && php artisan key:generate --show" > .app_key_temp
        
        APP_KEY=$(cat .app_key_temp)
        rm .app_key_temp
        
        # Update .env file
        sed -i "s/^APP_KEY=.*/APP_KEY=${APP_KEY}/" .env
        log_success "Generated APP_KEY: ${APP_KEY}"
    else
        log_info "APP_KEY already exists"
    fi
}

build_image() {
    log_info "Building Docker image..."
    
    docker build -t ${IMAGE_NAME}:latest .
    
    if [ $? -eq 0 ]; then
        log_success "Docker image built successfully"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
}

build_production() {
    log_info "Building production image with optimizations..."
    
    # Build multi-stage production image
    docker build --target production -t ${IMAGE_NAME}:production .
    
    if [ $? -eq 0 ]; then
        log_success "Production Docker image built successfully"
    else
        log_error "Failed to build production Docker image"
        exit 1
    fi
}

start_services() {
    log_info "Starting services..."
    
    # Create network if it doesn't exist
    docker network create ${NETWORK_NAME} 2>/dev/null || true
    
    # Start services using docker-compose
    if [ -f docker-compose.prod.yml ]; then
        docker-compose -f docker-compose.prod.yml up -d
    else
        log_error "docker-compose.prod.yml not found"
        exit 1
    fi
    
    log_success "Services started successfully"
}

stop_services() {
    log_info "Stopping services..."
    
    if [ -f docker-compose.prod.yml ]; then
        docker-compose -f docker-compose.prod.yml down
    else
        log_warning "docker-compose.prod.yml not found, stopping containers manually"
        docker stop ${CONTAINER_NAME} 2>/dev/null || true
        docker stop dccp-admin-redis 2>/dev/null || true
        docker stop dccp-admin-postgres 2>/dev/null || true
    fi
    
    log_success "Services stopped"
}

restart_services() {
    log_info "Restarting services..."
    stop_services
    start_services
}

view_logs() {
    log_info "Viewing application logs..."
    
    if [ -f docker-compose.prod.yml ]; then
        docker-compose -f docker-compose.prod.yml logs -f app
    else
        docker logs -f ${CONTAINER_NAME}
    fi
}

cleanup() {
    log_info "Cleaning up Docker resources..."
    
    # Remove stopped containers
    docker container prune -f
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    log_success "Cleanup completed"
}

run_migrations() {
    log_info "Running database migrations..."
    
    docker exec ${CONTAINER_NAME} php artisan migrate --force
    
    if [ $? -eq 0 ]; then
        log_success "Migrations completed successfully"
    else
        log_error "Migration failed"
        exit 1
    fi
}

optimize_application() {
    log_info "Optimizing Laravel application..."
    
    docker exec ${CONTAINER_NAME} sh -c "
        php artisan config:cache &&
        php artisan route:cache &&
        php artisan view:cache &&
        php artisan event:cache &&
        php artisan icon:cache &&
        php artisan optimize
    "
    
    if [ $? -eq 0 ]; then
        log_success "Application optimized successfully"
    else
        log_error "Optimization failed"
        exit 1
    fi
}

check_health() {
    log_info "Checking application health..."
    
    sleep 10  # Wait for container to start
    
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "Application is healthy"
    else
        log_error "Application health check failed"
        log_info "Checking container logs..."
        docker logs ${CONTAINER_NAME} --tail 50
        exit 1
    fi
}

full_deployment() {
    log_info "Starting full deployment process..."
    
    check_requirements
    setup_environment
    generate_app_key
    build_production
    stop_services
    start_services
    sleep 30  # Wait for services to start
    run_migrations
    optimize_application
    check_health
    
    log_success "Deployment completed successfully!"
    log_info "Application is available at: http://localhost:8000"
}

show_status() {
    log_info "Service Status:"
    echo "===================="
    
    if [ -f docker-compose.prod.yml ]; then
        docker-compose -f docker-compose.prod.yml ps
    else
        docker ps --filter "name=dccp-admin"
    fi
    
    echo ""
    log_info "Application URL: http://localhost:8000"
    log_info "Health Check: http://localhost:8000/health"
}

show_help() {
    echo "DCCP Admin V2 Deployment Script"
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build         Build Docker image"
    echo "  build-prod    Build production Docker image"
    echo "  start         Start services"
    echo "  stop          Stop services"
    echo "  restart       Restart services"
    echo "  logs          View application logs"
    echo "  migrate       Run database migrations"
    echo "  optimize      Optimize Laravel application"
    echo "  health        Check application health"
    echo "  status        Show service status"
    echo "  cleanup       Clean up Docker resources"
    echo "  deploy        Full deployment (recommended)"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 deploy     # Full deployment"
    echo "  $0 build      # Build image only"
    echo "  $0 start      # Start services"
    echo "  $0 logs       # View logs"
}

# Main script logic
case "${1:-help}" in
    "build")
        check_requirements
        build_image
        ;;
    "build-prod")
        check_requirements
        build_production
        ;;
    "start")
        check_requirements
        start_services
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "logs")
        view_logs
        ;;
    "migrate")
        run_migrations
        ;;
    "optimize")
        optimize_application
        ;;
    "health")
        check_health
        ;;
    "status")
        show_status
        ;;
    "cleanup")
        cleanup
        ;;
    "deploy")
        full_deployment
        ;;
    "help"|*)
        show_help
        ;;
esac