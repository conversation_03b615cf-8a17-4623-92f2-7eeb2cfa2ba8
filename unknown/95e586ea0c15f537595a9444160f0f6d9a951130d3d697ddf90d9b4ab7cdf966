[www]
user = http
group = http

; Socket configuration
listen = /var/run/php-fpm/php-fpm.sock
listen.owner = http
listen.group = http
listen.mode = 0660

; Adjust the number of child processes based on your server's resources
; For a server with 4 CPU cores, a good starting point is:
pm = dynamic
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 15
pm.max_requests = 500

; Optimize for performance
request_terminate_timeout = 300
request_slowlog_timeout = 5s
slowlog = /var/log/php-fpm/slow.log

; Increase memory limits for better performance
php_admin_value[memory_limit] = 256M
php_admin_value[max_execution_time] = 300
php_admin_value[upload_max_filesize] = 64M
php_admin_value[post_max_size] = 64M

; Enable OpCache for better performance
php_admin_value[opcache.enable] = 1
php_admin_value[opcache.memory_consumption] = 128
php_admin_value[opcache.interned_strings_buffer] = 16
php_admin_value[opcache.max_accelerated_files] = 10000
php_admin_value[opcache.revalidate_freq] = 60
php_admin_value[opcache.fast_shutdown] = 1
php_admin_value[opcache.enable_cli] = 1
