# Tuition Calculation System Documentation

## Overview

The DccpAdminV2 system implements a comprehensive tuition calculation system for student enrollments. This document explains how tuition fees are calculated, the components involved, and the business logic behind the calculations.

## Core Components

### 1. Models Involved

- **StudentEnrollment**: Main enrollment record
- **StudentTuition**: Stores calculated tuition amounts and payment information
- **Subject**: Contains lecture and laboratory unit information
- **Course**: Contains fee rates per unit and miscellaneous fees
- **SubjectEnrollment**: Links students to specific subjects with calculated fees

### 2. Key Services

- **EnrollmentServiceProvider**: Handles tuition calculations and updates
- **EnrollmentService**: Manages enrollment creation and fee calculations
- **GeneralSettingsService**: Provides current academic period information

## Tuition Calculation Logic

### Base Fee Calculation

The tuition calculation follows this hierarchy:

1. **Subject-Level Fees**: Each subject has lecture and laboratory units
2. **Course-Level Rates**: Each course defines cost per unit for lectures and labs
3. **Special Cases**: NSTP subjects get 50% discount on lecture fees
4. **Modular Subjects**: Fixed fee of ₱2,400 regardless of units

### Detailed Calculation Steps

#### 1. Individual Subject Fee Calculation

For each enrolled subject:

```php
// Get subject units and course rates
$totalUnits = $subject->lecture + $subject->laboratory;
$courseLecPerUnit = $subject->course->lec_per_unit ?? 0;
$courseLabPerUnit = $subject->course->lab_per_unit ?? 0;

// Calculate lecture fee
$lectureFee = $subject->lecture ? $totalUnits * $courseLecPerUnit : 0;

// Special case: NSTP subjects get 50% discount
if (str_contains(strtoupper($subject->code), 'NSTP')) {
    $lectureFee *= 0.5;
}

// Calculate laboratory fee (per lab unit, not total units)
$laboratoryFee = $subject->laboratory ? 1 * $courseLabPerUnit : 0;
```

#### 2. Modular Subject Override

If a subject is marked as modular:
```php
if ($subject->is_modular) {
    $lectureFee = 2400; // Fixed modular fee
    $laboratoryFee = 0;  // No lab fee for modular
}
```

#### 3. Total Calculation

```php
// Sum all subject fees
$totalLecture = sum_of_all_lecture_fees;
$totalLaboratory = sum_of_all_laboratory_fees;

// Apply discount (only to lecture portion)
$discount = percentage_discount; // 0-100
$discountedLecture = $totalLecture * (1 - $discount / 100);

// Calculate tuition (discounted lecture + full laboratory)
$totalTuition = $discountedLecture + $totalLaboratory;

// Add miscellaneous fees
$miscellaneousFee = $course->miscelaneous ?? 3500; // Default ₱3,500
$overallTotal = $totalTuition + $miscellaneousFee;

// Calculate balance
$downPayment = user_input_amount;
$balance = $overallTotal - $downPayment;
```

## Fee Structure

### Course-Based Rates

Each course defines:
- **lec_per_unit**: Cost per lecture unit (e.g., ₱500 per unit)
- **lab_per_unit**: Cost per laboratory unit (e.g., ₱800 per unit)
- **miscelaneous**: Fixed miscellaneous fees (default ₱3,500)

### Subject Units

Each subject defines:
- **lecture**: Number of lecture units
- **laboratory**: Number of laboratory units
- **units**: Total units (lecture + laboratory)

### Special Cases

1. **NSTP Subjects**: 50% discount on lecture fees
2. **Modular Subjects**: Fixed ₱2,400 fee, no laboratory charges
3. **Laboratory-Only Subjects**: Only laboratory fees apply
4. **Lecture-Only Subjects**: Only lecture fees apply

## Discount System

### Discount Application Rules

- Discounts are applied **only to the lecture portion** of tuition
- Laboratory fees are **never discounted**
- Miscellaneous fees are **never discounted**
- Available discounts: 0%, 5%, 10%, 15%, ..., 100% (in 5% increments)

### Discount Calculation Example

```
Original Lecture Fee: ₱15,000
Laboratory Fee: ₱3,000
Discount: 20%

Discounted Lecture: ₱15,000 × (1 - 0.20) = ₱12,000
Total Tuition: ₱12,000 + ₱3,000 = ₱15,000
Miscellaneous: ₱3,500
Overall Total: ₱15,000 + ₱3,500 = ₱18,500
```

## Payment Structure

### Components

1. **Total Tuition**: Discounted lecture + laboratory fees
2. **Miscellaneous Fees**: Course-specific or default ₱3,500
3. **Overall Total**: Total tuition + miscellaneous fees
4. **Down Payment**: Initial payment (minimum ₱500, default ₱3,500)
5. **Balance**: Overall total - down payment

### Payment Status

- **Fully Paid**: Balance ≤ 0
- **Not Fully Paid**: Balance > 0

## Database Storage

### StudentTuition Table Fields

- `total_tuition`: Final tuition after discounts
- `total_lectures`: Lecture fees after discount
- `total_laboratory`: Laboratory fees (no discount)
- `total_miscelaneous_fees`: Miscellaneous fees
- `discount`: Discount percentage applied
- `downpayment`: Initial payment amount
- `overall_tuition`: Total including miscellaneous
- `total_balance`: Remaining balance

## Manual Override System

### Override Capabilities

Administrators can manually override:
- Individual subject lecture/laboratory fees
- Total lecture amount
- Total laboratory amount
- Discount percentage
- Miscellaneous fees
- Down payment amount

### Override Behavior

- Manual changes set `is_manually_modified` flag
- System preserves manual overrides during recalculations
- Discounts still apply to manually set lecture amounts
- Balance automatically recalculates when payments change

## Real-Time Calculation

### Form Interactions

The system provides real-time updates when:
- Adding/removing subjects
- Changing modular status
- Adjusting discount percentage
- Modifying down payment
- Manually editing fees

### Calculation Triggers

- Subject selection/deselection
- Modular toggle changes
- Discount field updates
- Manual fee adjustments
- Down payment modifications

## Example Calculation

### Sample Enrollment

**Student**: John Doe  
**Course**: BSIT (Lecture: ₱500/unit, Lab: ₱800/unit, Misc: ₱3,500)  
**Subjects**:
1. Programming 1 (3 lecture, 1 lab)
2. Mathematics (3 lecture, 0 lab)
3. NSTP 1 (1 lecture, 0 lab)

### Step-by-Step Calculation

```
Programming 1:
- Total units: 3 + 1 = 4
- Lecture fee: 4 × ₱500 = ₱2,000
- Lab fee: 1 × ₱800 = ₱800

Mathematics:
- Total units: 3 + 0 = 3
- Lecture fee: 3 × ₱500 = ₱1,500
- Lab fee: 0 × ₱800 = ₱0

NSTP 1 (50% discount):
- Total units: 1 + 0 = 1
- Lecture fee: 1 × ₱500 × 0.5 = ₱250
- Lab fee: 0 × ₱800 = ₱0

Totals:
- Total Lecture: ₱2,000 + ₱1,500 + ₱250 = ₱3,750
- Total Laboratory: ₱800 + ₱0 + ₱0 = ₱800

With 10% Discount:
- Discounted Lecture: ₱3,750 × 0.9 = ₱3,375
- Total Tuition: ₱3,375 + ₱800 = ₱4,175
- Miscellaneous: ₱3,500
- Overall Total: ₱4,175 + ₱3,500 = ₱7,675

With ₱3,500 Down Payment:
- Balance: ₱7,675 - ₱3,500 = ₱4,175
```

## Error Handling

### Validation Rules

- Down payment minimum: ₱500
- Discount range: 0-100%
- Subject fees must be numeric
- Required fields: student, subjects, academic info

### Edge Cases

- Missing course fee rates: Uses 0 as default
- Missing miscellaneous fee: Uses ₱3,500 default
- Zero-unit subjects: No fees calculated
- Duplicate subject enrollments: Prevented by validation

## Integration Points

### Related Systems

- **Student Management**: Links to student records
- **Course Management**: Retrieves fee structures
- **Subject Management**: Gets unit information
- **Class Scheduling**: Validates subject availability
- **Payment Processing**: Uses calculated amounts
- **Academic Records**: Stores enrollment history

This calculation system ensures accurate, transparent, and flexible tuition management while maintaining data integrity and supporting various payment scenarios.
