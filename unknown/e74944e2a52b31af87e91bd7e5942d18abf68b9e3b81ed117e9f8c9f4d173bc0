# Container Deployment Guide for Browsershot

This guide provides step-by-step instructions for deploying and verifying Browsershot in your Nix container environment.

## Current Status

Based on your container output, the system has detected:
- ✅ Chromium binaries at `/root/.nix-profile/bin/chromium` and `/root/.nix-profile/bin/chromium-browser`
- ✅ Node.js at `/root/.nix-profile/bin/node`
- ❌ Environment variables not set (CHROME_PATH, NODE_BINARY_PATH, NPM_BINARY_PATH)
- ❓ NPM availability needs verification

## Deployment Steps

### Step 1: Deploy Updated Configuration

1. **Ensure your `nixpacks.toml` includes the updated configuration:**
   ```toml
   nixPkgs = [
       "...",
       "python311Packages.supervisor",
       "chromium",
       "nodejs_20",
       "nodePackages.npm",
   ]
   ```

2. **Redeploy your application** to apply the nixpacks configuration changes.

### Step 2: Verify Container Environment

Once your container is running, execute these commands **inside the container**:

```bash
# Test the environment
php artisan test:environment

# Find Chrome paths
php artisan find:chrome-path

# Quick Browsershot test
php artisan test:browsershot

# Full verification
php artisan verify:browsershot --detailed
```

### Step 3: Check Environment Variables

After deployment, verify that the start script is setting environment variables:

```bash
# Check if variables are set
echo "CHROME_PATH: $CHROME_PATH"
echo "NODE_BINARY_PATH: $NODE_BINARY_PATH"
echo "NPM_BINARY_PATH: $NPM_BINARY_PATH"

# Check supervisor logs to see if paths were detected
cat /var/log/supervisord.log | grep -A 10 "Browsershot Environment Setup"
```

### Step 4: Manual Path Verification

If environment variables aren't being set, verify the paths manually:

```bash
# Check if binaries exist and are executable
ls -la /root/.nix-profile/bin/chromium*
ls -la /root/.nix-profile/bin/node
ls -la /root/.nix-profile/bin/npm

# Test execution
/root/.nix-profile/bin/chromium --version
/root/.nix-profile/bin/node --version
/root/.nix-profile/bin/npm --version
```

## Troubleshooting

### Issue 1: Environment Variables Not Set

If environment variables are not being set by the start script:

**Solution A: Manual Environment Setup**
```bash
# Set environment variables manually
export CHROME_PATH="/root/.nix-profile/bin/chromium"
export NODE_BINARY_PATH="/root/.nix-profile/bin/node"
export NPM_BINARY_PATH="/root/.nix-profile/bin/npm"

# Test Browsershot
php artisan test:browsershot
```

**Solution B: Check Start Script Execution**
```bash
# Check if start script is running
ps aux | grep supervisord

# Check supervisor logs
tail -f /var/log/supervisord.log
```

### Issue 2: NPM Not Found

If NPM is not available:

**Check NPM Installation:**
```bash
# Look for NPM in different locations
find /root/.nix-profile -name "npm" -type f
find /nix -name "npm" -type f 2>/dev/null | head -5

# Check if nodePackages.npm is properly included
ls -la /root/.nix-profile/bin/ | grep npm
```

**Alternative NPM Package:**
If `nodePackages.npm` doesn't work, try updating `nixpacks.toml`:
```toml
nixPkgs = [
    "...",
    "python311Packages.supervisor",
    "chromium",
    "nodejs_20",
    "nodejs_20.pkgs.npm",  # Alternative npm package
]
```

### Issue 3: Chromium Not Working

If Chromium is found but not working:

**Test Chromium Directly:**
```bash
# Test chromium with basic options
/root/.nix-profile/bin/chromium --version
/root/.nix-profile/bin/chromium --headless --disable-gpu --no-sandbox --dump-dom about:blank
```

**Check Dependencies:**
```bash
# Check if all required libraries are available
ldd /root/.nix-profile/bin/chromium | grep "not found"
```

### Issue 4: Permission Issues

If you encounter permission errors:

```bash
# Check storage permissions
ls -la storage/
chmod -R 755 storage/
chown -R www-data:www-data storage/

# Create temp directory if needed
mkdir -p storage/app/browsershot-temp
chmod 755 storage/app/browsershot-temp
```

## Expected Results

After successful deployment and setup:

1. **Environment Test:**
   ```bash
   php artisan test:environment
   ```
   Should show:
   - ✅ All environment variables set
   - ✅ All binaries found and executable
   - ✅ Directories writable

2. **Browsershot Test:**
   ```bash
   php artisan test:browsershot
   ```
   Should show:
   - ✅ Browsershot test PASSED!
   - ✅ PDF generation working
   - ✅ File created successfully

3. **Full Verification:**
   ```bash
   php artisan verify:browsershot --detailed
   ```
   Should show:
   - 🎉 All checks passed! Browsershot is ready for production. (6/6)

## Using Browsershot in Your Application

Once everything is working, use the BrowsershotService in your application:

```php
use App\Services\BrowsershotService;

// Simple PDF generation
$html = '<h1>Hello World</h1><p>This is a PDF.</p>';
$outputPath = storage_path('app/document.pdf');
$success = BrowsershotService::generatePdf($html, $outputPath);

// With custom options
$success = BrowsershotService::generatePdf($html, $outputPath, [
    'format' => 'A4',
    'margin_top' => 10,
    'margin_bottom' => 10,
    'print_background' => true,
]);

// For screenshots
$url = 'https://example.com';
$screenshotPath = storage_path('app/screenshot.png');
$success = BrowsershotService::generateScreenshot($url, $screenshotPath);
```

## Container Restart

After any configuration changes, restart your container services:

```bash
# Restart supervisor (if running)
supervisorctl restart all

# Or restart the entire container
# (depends on your deployment platform)
```

## Monitoring

Set up monitoring to ensure Browsershot continues working:

```bash
# Add to your health check script
php artisan verify:browsershot > /dev/null 2>&1 || echo "Browsershot not working"

# Or create a simple endpoint
# GET /health/browsershot -> returns 200 if working
```

## Common Deployment Platforms

### Nixpacks-based Platforms (Railway, etc.)

1. Update your `nixpacks.toml` with the correct configuration
2. Push your changes to trigger a new deployment
3. Check the build logs for "Browsershot Environment Setup" messages
4. Run verification commands once the container is running

### Docker-based Deployments

If using Docker instead of Nixpacks:

```dockerfile
# Add to your Dockerfile
RUN apt-get update && apt-get install -y \
    chromium-browser \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

ENV CHROME_PATH=/usr/bin/chromium-browser
ENV NODE_BINARY_PATH=/usr/bin/node
ENV NPM_BINARY_PATH=/usr/bin/npm
```

## Support

If you continue to have issues:

1. Run `php artisan test:environment` and share the output
2. Check supervisor logs: `cat /var/log/supervisord.log`
3. Verify your nixpacks.toml is correctly deployed
4. Ensure your deployment platform supports the Nix packages

The goal is to have all verification commands pass, indicating that Browsershot is ready for production use in your Laravel application.