[Unit]
Description=DCCP Admin V2 Laravel Application
Documentation=https://github.com/your-org/DccpAdminV2
After=docker.service
Wants=docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/DccpAdminV2
ExecStartPre=/usr/bin/docker-compose -f docker-compose.prod.yml down
ExecStart=/usr/bin/docker-compose -f docker-compose.prod.yml up -d
ExecStop=/usr/bin/docker-compose -f docker-compose.prod.yml down
ExecReload=/usr/bin/docker-compose -f docker-compose.prod.yml restart
TimeoutStartSec=300
TimeoutStopSec=120
RestartSec=10
Restart=on-failure

# Security settings
User=dccp
Group=dccp
UMask=0027
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/DccpAdminV2

# Environment
Environment=COMPOSE_PROJECT_NAME=dccp-admin
Environment=DOCKER_BUILDKIT=1

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=dccp-admin

[Install]
WantedBy=multi-user.target