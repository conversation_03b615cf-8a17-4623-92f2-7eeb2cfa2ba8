# Deployment Configuration for Browsershot PDF Generation

This document outlines the configuration needed for proper PDF generation using Browsershot in a Nixpacks deployment environment.

## Environment Variables

Set these environment variables in your deployment platform:

```bash
# Chrome/Chromium Configuration
CHROME_PATH="/root/.nix-profile/bin/chromium"
NODE_BINARY_PATH="/root/.nix-profile/bin/node"
NPM_BINARY_PATH="/tmp/fake-npm-bin/npm.js"

# Browsershot Configuration
BROWSERSHOT_NO_SANDBOX="true"
BROWSERSHOT_DISABLE_WEB_SECURITY="true"
BROWSERSHOT_IGNORE_HTTPS_ERRORS="true"
BROWSERSHOT_TIMEOUT="120"

# Chrome Runtime Configuration
CHROME_DEVEL_SANDBOX="/tmp"
DISPLAY=":99"
```

## Deployment Structure

The deployment includes:

1. **nixpacks.toml** - Main deployment configuration
2. **config/browsershot.php** - <PERSON><PERSON> Browsershot configuration
3. **app/Services/BrowsershotService.php** - Service for consistent PDF generation
4. **Supervisor configuration** - Process management for queue workers

## Key Features

### Root-Safe Chrome Execution
- Chrome runs with `--no-sandbox` flag for root user compatibility
- Proper crash dump and user data directories configured
- Environment variables exported to all supervisor processes

### Consistent Configuration
- All PDF generation uses `BrowsershotService` for unified settings
- Chrome arguments optimized for containerized environments
- Proper timeout and memory management

### Queue Worker Support
- Environment variables properly passed to queue workers
- PDF generation jobs can run in background with correct Chrome access

## Testing Commands

### Verify Deployment
```bash
php artisan verify:deployment --fix
```

### Simple Browsershot Test
```bash
php artisan test:browsershot-simple
```

### Full Browsershot Test
```bash
php artisan test:browsershot --detailed
```

## File Structure

```
DccpAdminV2/
├── nixpacks.toml                          # Deployment configuration
├── config/browsershot.php                 # Browsershot settings
├── app/
│   ├── Services/BrowsershotService.php    # PDF generation service
│   ├── Jobs/GenerateAssessmentPdfJob.php  # Background PDF job
│   ├── Notifications/MigrateToStudent.php # Notification with PDF
│   └── Console/Commands/
│       ├── TestBrowsershot.php            # Full test command
│       ├── TestBrowsershotSimple.php      # Simple test command
│       └── VerifyDeployment.php           # Deployment verification
└── storage/
    ├── app/
    │   ├── private/                       # PDF storage directory
    │   └── browsershot-temp/              # Temporary files
    └── logs/                              # Application logs
```

## Troubleshooting

### Common Issues

1. **Chrome fails with sandbox error**
   - Ensure `BROWSERSHOT_NO_SANDBOX="true"` is set
   - Check that Chrome arguments include `--no-sandbox`

2. **PDF generation times out**
   - Increase `BROWSERSHOT_TIMEOUT` value
   - Check memory limits in php-fpm configuration

3. **Queue workers can't access Chrome**
   - Verify environment variables are exported in supervisor config
   - Check that `/tmp/chrome-*` directories have proper permissions

4. **NPM errors during PDF generation**
   - Ensure fake NPM script exists at `/tmp/fake-npm-bin/npm.js`
   - Service automatically skips NPM to prevent shell errors

### Debug Commands

Check Chrome binary:
```bash
/root/.nix-profile/bin/chromium --version --no-sandbox --disable-dev-shm-usage
```

Check environment in container:
```bash
env | grep -E "(CHROME|NODE|BROWSERSHOT)"
```

Test PDF generation manually:
```bash
php artisan tinker
>>> App\Services\BrowsershotService::test()
```

## Security Notes

- Chrome runs with `--no-sandbox` for container compatibility
- Web security disabled for PDF generation contexts
- Temporary directories properly isolated with unique names
- PDFs stored in private storage directory by default

## Performance Optimization

- Single-process Chrome mode reduces memory usage
- Proper cleanup of temporary files after generation
- Queue-based PDF generation for better user experience
- Memory limits increased during PDF generation operations

## Dependencies

Required packages in Nix environment:
- `chromium` - For PDF rendering
- `python311Packages.supervisor` - Process management
- Standard Laravel dependencies

The deployment automatically detects and configures these binaries from the Nix store.