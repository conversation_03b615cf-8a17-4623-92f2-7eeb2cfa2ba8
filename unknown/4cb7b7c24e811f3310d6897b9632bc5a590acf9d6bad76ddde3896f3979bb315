# Laravel specific
/.phpunit.cache
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/app/public
/storage/debugbar
/storage/ssr
/storage/clockwork
/storage/logs
/storage/pail
.phpunit.result.cache

# Environment and config files
.env
.env.*
!.env.example
!.env.production
.env.backup

# Development files
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
.php-cs-fixer.cache
.husky

# IDE files
/.idea
/.vscode
.phpstorm.meta.php
_ide_helper_models.php
_ide_helper.php

# OS files
**/.DS_Store
.Thumbs.db

# Testing
/tests
.phpunit.database.checksum
.phpunit.cache

# Cache and temporary files
/public/page-cache
/bootstrap/cache/*.php
/storage/framework/cache/data
/storage/framework/sessions
/storage/framework/views

# Build artifacts
rr
.rr.yaml
frankenphp
.config
.data

# Version control
.git
.gitignore
.gitattributes

# Documentation
README.md
DOCKER-DEPLOYMENT.md
*.md
docs/

# Development tools
rector.php
.rector.cache
patches/
deployment/

# Docker files (except Dockerfile)
docker-compose.yml
docker-compose.*.yml
!docker-compose.prod.yml
!docker-compose.external.yml

# Scripts
*.sh
!docker-entrypoint.sh

# Backup files
*.backup
*.bak
*.tmp

# Performance files
performance_tips.md
optimize_*.sh
*_optimizations.conf
temp_*.conf

# Chrome/PDF testing
test_chrome_pdf.php
setup_chrome_crashpad.sh
fix_chrome_permissions.sh