# Setup Guide: Assessment Resend System

## Prerequisites

Before setting up the new assessment resend system, ensure you have:

1. **Laravel Application** running on PHP 8.1+
2. **Redis Server** installed and configured
3. **Chrome/Chromium** browser installed for PDF generation
4. **Queue Workers** capability (Supervisor recommended for production)
5. **Email Configuration** properly set up

## Step 1: Environment Configuration

### Update your `.env` file:

```env
# Queue Configuration
QUEUE_CONNECTION=redis
REDIS_QUEUE_CONNECTION=default
REDIS_QUEUE=default
REDIS_QUEUE_RETRY_AFTER=90

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0

# PDF Generation (Browsershot/Chrome)
NODE_BINARY_PATH=/usr/bin/node
NPM_BINARY_PATH=/usr/bin/npm
CHROME_PATH=/usr/bin/chromium
# or
CHROME_PATH=/usr/bin/google-chrome

# Mail Configuration (ensure these are set)
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
```

## Step 2: Install Dependencies

The system uses existing Laravel packages, but verify they're installed:

```bash
# Spatie Browsershot (should already be installed)
composer require spatie/browsershot

# Chrome PHP (if using alternative PDF generation)
composer require chrome-php/chrome

# Redis (if not already installed)
composer require predis/predis
```

## Step 3: Database Setup

Ensure these tables exist (they should if your Laravel app is properly set up):

```bash
# Run migrations if needed
php artisan migrate

# The system uses these existing tables:
# - jobs (Laravel's job queue table)
# - failed_jobs (Laravel's failed job table)
# - student_enrollments
# - resources (for PDF storage)
# - users (for notifications)
```

## Step 4: Queue Configuration

### Create Supervisor Configuration (Production)

Create `/etc/supervisor/conf.d/assessment-workers.conf`:

```ini
[program:assessment-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/your/app/artisan queue:work redis --queue=assessments,pdf-generation,notifications --sleep=3 --tries=3 --timeout=300
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/path/to/your/app/storage/logs/worker.log
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=10
stopwaitsecs=3600
```

Then reload Supervisor:
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start assessment-worker:*
```

### For Development (Simple Queue Worker)

```bash
# Start queue worker in terminal
php artisan queue:work redis --queue=assessments,pdf-generation,notifications --timeout=300

# Or use queue daemon
php artisan queue:listen redis --queue=assessments,pdf-generation,notifications
```

## Step 5: Test System Components

### Test Redis Connection
```bash
php artisan tinker
>>> Cache::store('redis')->put('test', 'value', 60);
>>> Cache::store('redis')->get('test');
# Should return 'value'
```

### Test PDF Generation
```bash
php artisan test:browsershot
# Should show successful PDF generation
```

### Test Assessment System
```bash
# Test with specific enrollment (replace 123 with actual enrollment ID)
php artisan test:assessment-resend 123

# Test with automatic selection
php artisan test:assessment-resend

# Dry run test
php artisan test:assessment-resend --dry-run
```

## Step 6: Directory Permissions

Ensure proper permissions for file operations:

```bash
# Storage directories
chmod -R 775 storage/app/private
chmod -R 775 storage/app/temp
chmod -R 775 storage/logs

# Create directories if they don't exist
mkdir -p storage/app/private
mkdir -p storage/app/temp
mkdir -p storage/app/browsershot-temp

# Set ownership (adjust user as needed)
chown -R www-data:www-data storage/
```

## Step 7: Chrome/Chromium Setup

### Ubuntu/Debian:
```bash
# Install Chromium
sudo apt update
sudo apt install chromium-browser

# Or install Google Chrome
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
sudo apt update
sudo apt install google-chrome-stable
```

### CentOS/RHEL:
```bash
# Install Chromium
sudo yum install chromium

# Or download Chrome RPM
wget https://dl.google.com/linux/direct/google-chrome-stable_current_x86_64.rpm
sudo rpm -ivh google-chrome-stable_current_x86_64.rpm
```

### Docker Environment:
```dockerfile
# Add to your Dockerfile
RUN apt-get update && apt-get install -y \
    chromium-browser \
    fonts-liberation \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libxss1 \
    libxtst6 \
    xdg-utils

# Set Chrome path
ENV CHROME_PATH=/usr/bin/chromium
```

## Step 8: Monitoring Setup

### View Queue Status
```bash
# Monitor assessment jobs
php artisan monitor:assessment-jobs

# Monitor active jobs only
php artisan monitor:assessment-jobs --active-only

# Monitor specific job
php artisan monitor:assessment-jobs --job-id=assessment_resend_xxxxx

# Clear completed jobs
php artisan monitor:assessment-jobs --clear-completed
```

### Log Monitoring
```bash
# Follow application logs
tail -f storage/logs/laravel.log

# Follow worker logs (if using Supervisor)
tail -f storage/logs/worker.log

# Follow queue status
php artisan queue:monitor
```

## Step 9: Performance Tuning

### Redis Configuration
Edit `/etc/redis/redis.conf`:

```conf
# Memory management
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence (optional, depends on your needs)
save 900 1
save 300 10
save 60 10000

# Network
timeout 0
tcp-keepalive 300
```

### PHP Configuration
Adjust `php.ini` for better performance:

```ini
# Memory and execution time
memory_limit = 512M
max_execution_time = 300
max_input_time = 300

# File uploads (for PDF handling)
upload_max_filesize = 10M
post_max_size = 10M

# Process control
pm.max_children = 20
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 10
```

## Step 10: Security Considerations

### File Permissions
```bash
# Secure storage directory
find storage -type f -exec chmod 644 {} \;
find storage -type d -exec chmod 755 {} \;

# Secure config files
chmod 600 .env
```

### Redis Security
```conf
# In redis.conf
bind 127.0.0.1
requirepass your-secure-password
```

Update `.env`:
```env
REDIS_PASSWORD=your-secure-password
```

## Step 11: Verification Checklist

### System Check
- [ ] Redis server running and accessible
- [ ] Chrome/Chromium installed and accessible
- [ ] Queue workers running
- [ ] Storage directories writable
- [ ] Mail configuration working
- [ ] PDF generation test passes
- [ ] Assessment resend test passes

### Functional Check
- [ ] Can access Student Enrollment view page
- [ ] "Resend Assessment" button visible for appropriate enrollments
- [ ] Confirmation modal appears on button click
- [ ] Progress notifications appear after confirmation
- [ ] Email notifications sent successfully
- [ ] PDF attachments generated and attached
- [ ] Admin notifications sent to super admins
- [ ] Job progress tracking works

### Performance Check
- [ ] PDF generation completes within 30 seconds
- [ ] Email sending completes within 60 seconds
- [ ] No memory leaks in queue workers
- [ ] Redis memory usage stable
- [ ] Queue workers restart properly after failures

## Troubleshooting

### Common Issues and Solutions

#### 1. Jobs Stuck in Queue
```bash
# Check queue worker status
php artisan queue:work --once

# Restart queue workers
sudo supervisorctl restart assessment-worker:*

# Clear stuck jobs
php artisan queue:clear
```

#### 2. PDF Generation Fails
```bash
# Test Browsershot directly
php artisan test:browsershot --detailed

# Check Chrome/Chromium
which chromium
which google-chrome

# Check permissions
ls -la storage/app/private/
```

#### 3. Redis Connection Issues
```bash
# Test Redis
redis-cli ping

# Check Redis config
redis-cli config get "*"

# Restart Redis
sudo systemctl restart redis
```

#### 4. Email Not Sending
```bash
# Test mail config
php artisan tinker
>>> Mail::raw('Test', function($msg) { $msg->to('<EMAIL>')->subject('Test'); });

# Check logs
grep -i "mail" storage/logs/laravel.log
```

## Maintenance

### Regular Tasks
```bash
# Weekly: Clear old job data
php artisan monitor:assessment-jobs --clear-completed

# Daily: Check queue status
php artisan queue:monitor

# As needed: Restart workers
sudo supervisorctl restart assessment-worker:*
```

### Monitoring Commands
```bash
# View active jobs
php artisan monitor:assessment-jobs --active-only

# Check Redis memory
redis-cli info memory

# View failed jobs
php artisan queue:failed
```

## Support

If you encounter issues:

1. Check the logs in `storage/logs/laravel.log`
2. Run the test commands to isolate the problem
3. Verify all prerequisites are met
4. Check the troubleshooting section above
5. Review the main documentation in `ASSESSMENT_RESEND_SYSTEM.md`

## Success Indicators

You'll know the system is working correctly when:

- Queue workers process jobs without errors
- PDFs generate in under 30 seconds
- Emails are delivered with proper attachments
- Users receive clear progress notifications
- Admin notifications work properly
- System handles failures gracefully

The assessment resend system should now significantly improve performance and user experience compared to the previous synchronous approach.