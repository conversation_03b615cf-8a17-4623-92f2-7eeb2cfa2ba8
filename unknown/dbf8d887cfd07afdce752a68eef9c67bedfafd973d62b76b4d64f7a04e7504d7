[phases.setup]
nixPkgs = ["...", "python311Packages.supervisor", "chromium"]


[phases.build]
cmds = [
    "mkdir -p /etc/supervisor/conf.d/",
    "cp /assets/worker-*.conf /etc/supervisor/conf.d/",
    "cp /assets/supervisord.conf /etc/supervisord.conf",
    "chmod +x /assets/start.sh",
    "chmod +x /assets/start-php.sh",
    "mkdir -p /tmp/chrome-crashpad",
    "mkdir -p /tmp/chrome-user-data",
    "chmod 755 /tmp/chrome-crashpad",
    "chmod 755 /tmp/chrome-user-data",
    "mkdir -p /app/storage/logs",
    "mkdir -p /app/storage/framework/cache",
    "mkdir -p /app/storage/framework/sessions",
    "mkdir -p /app/storage/framework/views",
    "mkdir -p /app/storage/app/private",
    "chmod -R 775 /app/storage",
    "mkdir -p /tmp/fake-npm-bin",
    "echo '#!/bin/bash' > /tmp/fake-npm-bin/npm",
    "echo 'if [ \"$1\" = \"root\" ] && [ \"$2\" = \"-g\" ]; then' >> /tmp/fake-npm-bin/npm",
    "echo '  echo \"/usr/lib/node_modules\"' >> /tmp/fake-npm-bin/npm",
    "echo 'else' >> /tmp/fake-npm-bin/npm",
    "echo '  exit 0' >> /tmp/fake-npm-bin/npm",
    "echo 'fi' >> /tmp/fake-npm-bin/npm",
    "chmod +x /tmp/fake-npm-bin/npm",
    "...",
]

[start]
cmd = '/assets/start.sh'

[staticAssets]
"start.sh" = '''
#!/bin/bash

# Find the actual binary paths in the Nix store, which is accessible to all users.
# This is the correct way to locate binaries installed via Nix.
CHROME_BIN=$(find /nix/store -name chromium -type f -executable | head -1)
NODE_BIN=$(find /nix/store -name node -type f -executable | head -1)
NPM_BIN=$(find /nix/store -name npm -type f -executable | head -1)
PHP_FPM_BIN=$(which php-fpm || find /nix/store -name php-fpm -type f -executable | head -1)

# Export the paths for Browsershot and other processes.
# These will be inherited by the supervisor child processes (nginx, php-fpm, and the laravel worker).
export CHROME_PATH="${CHROME_BIN}"
export NODE_BINARY_PATH="${NODE_BIN}"
export NPM_BINARY_PATH="/tmp/fake-npm-bin/npm"
export PHP_FPM_PATH="${PHP_FPM_BIN:-php-fpm}"

# Set additional Chrome/Browsershot environment variables for root execution
export BROWSERSHOT_NO_SANDBOX="true"
export BROWSERSHOT_DISABLE_WEB_SECURITY="true"
export BROWSERSHOT_IGNORE_HTTPS_ERRORS="true"
export BROWSERSHOT_TIMEOUT="120"
export CHROME_DEVEL_SANDBOX="/tmp"
export DISPLAY=":99"

# Export all environment variables to supervisor and all child processes
export -p > /etc/environment
echo "Environment variables exported to /etc/environment for all processes"

# Ensure the fake NPM binary is executable and properly linked
if [ -f "/tmp/fake-npm-bin/npm" ]; then
    chmod +x /tmp/fake-npm-bin/npm
    export NPM_BINARY_PATH="/tmp/fake-npm-bin/npm"
    echo "Fake NPM binary configured: $NPM_BINARY_PATH"
else
    echo "WARNING: Fake NPM binary not found at /tmp/fake-npm-bin/npm"
fi

# Add the binary directories to PATH to ensure subprocess access
if [ -n "$NODE_BIN" ]; then
    NODE_DIR=$(dirname "$NODE_BIN")
    export PATH="$NODE_DIR:$PATH"
fi

if [ -n "$CHROME_BIN" ]; then
    CHROME_DIR=$(dirname "$CHROME_BIN")
    export PATH="$CHROME_DIR:$PATH"
fi

# Set NODE_PATH to prevent npm usage issues
if [ -n "$NODE_BIN" ]; then
    NODE_LIB_DIR=$(dirname $(dirname "$NODE_BIN"))/lib/node_modules
    if [ -d "$NODE_LIB_DIR" ]; then
        export NODE_PATH="$NODE_LIB_DIR"
        echo "NODE_PATH set to: $NODE_PATH"
    fi
fi

# Add fake npm to PATH
export PATH="/tmp/fake-npm-bin:$PATH"
echo "Fake npm wrapper created and added to PATH"

# Transform the nginx configuration
# The original prestart.mjs script might need node, so we ensure it's available in the PATH.
export PATH=$(dirname "$NODE_BIN"):$PATH
node /assets/scripts/prestart.mjs /assets/nginx.template.conf /etc/nginx.conf

# Log the detected paths for debugging purposes.
echo "=== Browsershot Environment Setup ==="
echo "Chrome path set to: $CHROME_PATH"
echo "Node path set to: $NODE_BINARY_PATH"
echo "NPM path set to: $NPM_BINARY_PATH"
echo "PHP-FPM path set to: $PHP_FPM_PATH"
echo "No sandbox mode: $BROWSERSHOT_NO_SANDBOX"
echo "Disable web security: $BROWSERSHOT_DISABLE_WEB_SECURITY"
echo "Chrome sandbox dir: $CHROME_DEVEL_SANDBOX"
echo "Display: $DISPLAY"
echo "===================================="

# Verify the binaries exist and are executable.
if [ -z "$CHROME_PATH" ] || [ ! -x "$CHROME_PATH" ]; then
    echo "ERROR: Chromium binary not found or not executable!"
else
    echo "Chromium binary verified at: $CHROME_PATH"
    # Test Chrome with root-safe arguments
    "$CHROME_PATH" --version --no-sandbox --disable-dev-shm-usage 2>/dev/null || echo "Chrome version check with safety flags failed"
fi

if [ -z "$NODE_BIN" ] || [ ! -x "$NODE_BIN" ]; then
    echo "ERROR: Node.js binary not found or not executable!"
else
    echo "Node.js binary verified at: $NODE_BIN"
    "$NODE_BIN" --version
fi

if [ -z "$NPM_BIN" ] || [ ! -x "$NPM_BIN" ]; then
    echo "WARNING: NPM binary not found, using fake npm"
    echo "Fake NPM at: $NPM_BINARY_PATH"
else
    echo "NPM binary verified at: $NPM_BIN"
    "$NPM_BIN" --version
fi

if [ -n "$PHP_FPM_BIN" ] && [ -x "$PHP_FPM_BIN" ]; then
    echo "PHP-FPM binary verified at: $PHP_FPM_BIN"
    "$PHP_FPM_BIN" --version
elif command -v php-fpm >/dev/null 2>&1; then
    echo "PHP-FPM found in PATH: $(which php-fpm)"
    php-fpm --version
else
    echo "WARNING: PHP-FPM binary not found, using default 'php-fpm'"
fi

# Fix Laravel storage permissions. This ensures the web user and queue worker can write logs and cache.
mkdir -p /app/storage/logs \
         /app/storage/framework/cache \
         /app/storage/framework/sessions \
         /app/storage/framework/views \
         /app/storage/app/private \
         /app/storage/app/browsershot-temp

# Set ownership to www-data
chown -R www-data:www-data /app/storage

# Set proper permissions - directories 775, files 664
find /app/storage -type d -exec chmod 775 {} \;
find /app/storage -type f -exec chmod 664 {} \;

# Ensure log files exist with proper permissions
touch /app/storage/logs/laravel.log
chown www-data:www-data /app/storage/logs/laravel.log
chmod 664 /app/storage/logs/laravel.log

echo "Storage permissions set for /app/storage"

# Ensure Chrome temp directories have proper permissions for root execution
mkdir -p /tmp/chrome-crashpad /tmp/chrome-user-data /tmp/.X11-unix
chmod 777 /tmp/chrome-crashpad /tmp/chrome-user-data /tmp/.X11-unix
echo "Chrome temp directories configured for root execution"

# Ensure supervisor log files exist and have proper permissions.
touch /var/log/worker-laravel.log \
      /var/log/worker-nginx.log \
      /var/log/worker-phpfpm.log
chown www-data:www-data /var/log/worker-*.log
chmod 664 /var/log/worker-*.log

# Ensure Laravel can write to its log directory from both web and CLI
chgrp -R www-data /app/storage/logs
chmod -R g+w /app/storage/logs

echo "Supervisor log files configured"

# Start supervisor to manage nginx, php-fpm, and the Laravel queue worker.
# The "-n" flag keeps it in the foreground, which is standard for container entrypoints.
echo "Starting supervisord..."
supervisord -c /etc/supervisord.conf -n
'''

"supervisord.conf" = '''
[unix_http_server]
file=/assets/supervisor.sock

[supervisord]
logfile=/var/log/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/assets/supervisord.pid
nodaemon=false
silent=false
minfds=1024
minprocs=200

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///assets/supervisor.sock

[include]
files = /etc/supervisor/conf.d/*.conf
'''

"worker-nginx.conf" = '''
[program:worker-nginx]
process_name=%(program_name)s_%(process_num)02d
command=nginx -c /etc/nginx.conf
autostart=true
autorestart=true
stdout_logfile=/var/log/worker-nginx.log
stderr_logfile=/var/log/worker-nginx.log
'''

"worker-phpfpm.conf" = '''
[program:worker-phpfpm]
process_name=%(program_name)s_%(process_num)02d
command=/assets/start-php.sh
autostart=true
autorestart=true
stdout_logfile=/var/log/worker-phpfpm.log
stderr_logfile=/var/log/worker-phpfpm.log
environment=CHROME_PATH="%(ENV_CHROME_PATH)s",NODE_BINARY_PATH="%(ENV_NODE_BINARY_PATH)s",NPM_BINARY_PATH="%(ENV_NPM_BINARY_PATH)s",BROWSERSHOT_NO_SANDBOX="true",BROWSERSHOT_DISABLE_WEB_SECURITY="true",BROWSERSHOT_IGNORE_HTTPS_ERRORS="true",BROWSERSHOT_TIMEOUT="120"
'''
"worker-laravel.conf" = '''
[program:worker-laravel]
process_name=%(program_name)s_%(process_num)02d
command=bash -c 'source /etc/environment 2>/dev/null || true; exec php /app/artisan queue:work --sleep=3 --tries=3 --max-time=3600'
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
numprocs=12
startsecs=0
stopwaitsecs=3600
user=www-data
stdout_logfile=/var/log/worker-laravel.log
stderr_logfile=/var/log/worker-laravel.log
environment=CHROME_PATH="%(ENV_CHROME_PATH)s",NODE_BINARY_PATH="%(ENV_NODE_BINARY_PATH)s",NPM_BINARY_PATH="%(ENV_NPM_BINARY_PATH)s",BROWSERSHOT_NO_SANDBOX="true",BROWSERSHOT_DISABLE_WEB_SECURITY="true",BROWSERSHOT_IGNORE_HTTPS_ERRORS="true",BROWSERSHOT_TIMEOUT="120"
'''
"start-php.sh" = '''
#!/bin/bash

echo "Starting PHP Server..."

# Use PHP built-in server directly since PHP-FPM is causing issues
if command -v php >/dev/null 2>&1; then
    echo "Found PHP: $(php --version | head -1)"
    cd /app

    # Ensure storage permissions
    mkdir -p storage/logs storage/framework/cache storage/framework/sessions storage/framework/views
    chmod -R 775 storage 2>/dev/null || true
    touch storage/logs/laravel.log
    chmod 666 storage/logs/laravel.log 2>/dev/null || true

    echo "Starting PHP built-in server on 127.0.0.1:9000"
    echo "Document root: /app/public"

    # Start with error logging
    exec php -S 127.0.0.1:9000 -t public 2>&1
else
    echo "ERROR: PHP binary not found!"
    exit 127
fi
'''
"php-fpm.conf" = '''
[global]
error_log = /dev/stderr
daemonize = no
log_level = notice

[www]
listen = 127.0.0.1:9000
user = www-data
group = www-data
pm = dynamic
pm.max_children = 50
pm.min_spare_servers = 4
pm.max_spare_servers = 32
pm.start_servers = 18
clear_env = no
catch_workers_output = yes
php_admin_value[memory_limit] = 1G
php_admin_value[max_execution_time] = 60
php_admin_value[max_input_time] = 60
php_admin_value[post_max_size] = 256M
php_admin_value[upload_max_filesize] = 30M

'''
"nginx.template.conf" = '''
user www-data www-data;
worker_processes 5;
daemon off;

worker_rlimit_nofile 8192;

events {
  worker_connections  4096;  # Default: 1024
}

http {
    include    $!{nginx}/conf/mime.types;
    index    index.html index.htm index.php;

    default_type application/octet-stream;
    log_format   main '$remote_addr - $remote_user [$time_local]  $status '
        '"$request" $body_bytes_sent "$http_referer" '
        '"$http_user_agent" "$http_x_forwarded_for"';
    access_log /var/log/nginx-access.log;
    error_log /var/log/nginx-error.log;
    sendfile     on;
    tcp_nopush   on;
    server_names_hash_bucket_size 128; # this seems to be required for some vhosts

    server {
        listen ${PORT};
        listen [::]:${PORT};
        server_name localhost;

        $if(NIXPACKS_PHP_ROOT_DIR) (
            root ${NIXPACKS_PHP_ROOT_DIR};
        ) else (
            root /app;
        )

        add_header X-Content-Type-Options "nosniff";

        client_max_body_size 35M;

        index index.php;

        charset utf-8;

        # Proxy to PHP built-in server instead of FastCGI
        location / {
            try_files $uri $uri/ @php;
        }

        location @php {
            proxy_pass http://127.0.0.1:9000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location = /favicon.ico { access_log off; log_not_found off; }
        location = /robots.txt  { access_log off; log_not_found off; }

        # Handle PHP files by proxying to built-in server
        location ~ \.php$ {
            proxy_pass http://127.0.0.1:9000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location ~ /\.(?!well-known).* {
            deny all;
        }
    }
}
'''
