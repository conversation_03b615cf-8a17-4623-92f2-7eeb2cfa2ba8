# DccpAdminV2

DccpAdminV2 is a comprehensive administration panel for educational institutions, built with Laravel and Filament. It provides tools for managing students, enrollments, academics, and finances. The application is designed to be deployed as a Docker container.

## Technical Stack

- **Backend**: Laravel 11 with PHP 8.4 (running on FrankenPHP)
- **Frontend**: Filament Admin Panel with Livewire & Alpine.js
- **Database**: PostgreSQL
- **Cache & Queues**: Redis
- **PDF Generation**: <PERSON><PERSON> (utilizing a headless Chromium instance)
- **Containerization**: Docker

---

## Deployment

This application is designed to be deployed using Docker. The CI/CD pipeline automatically builds and publishes new images to Docker Hub.

### Automated Deployment (CI/CD)

The repository is configured with a GitHub Actions workflow that automates the deployment process:

1.  **Trigger**: The workflow runs on every push to the `main` branch.
2.  **Build**: It builds a new Docker image, including all PHP and Node.js dependencies.
3.  **Test**: A crucial step runs a container from the newly built image to verify that <PERSON><PERSON><PERSON>hot (the PDF generation engine) is working correctly.
4.  **Push to Docker Hub**: If the build and test steps succeed, the image is pushed to Docker Hub, tagged with the commit SHA.
5.  **Tag as Latest**: The new image is also tagged as `latest` on Docker Hub, making it easy to pull the most recent version.

### Manual Deployment on a Server

To deploy the application on your server, you will need **Docker** and **Docker Compose**.

**1. Set Up Environment**

First, clone the repository to your server:
```bash
git clone https://github.com/yukazakiri/DccpAdminV2.git
cd DccpAdminV2
```

Next, create a `.env` file from the example:
```bash
cp .env.example .env
```

**2. Configure Environment Variables**

You **must** edit the `.env` file and fill in the required values. Pay close attention to the database and Redis credentials, ensuring they match what you will configure in your `docker-compose.yml`.

```dotenv
# Application
APP_NAME=DccpAdmin
APP_ENV=production
APP_KEY= # LEAVE THIS BLANK! It will be generated automatically.
APP_DEBUG=false
APP_URL=http://your_domain_or_ip

# Database Connection (must match docker-compose.yml)
DB_CONNECTION=pgsql
DB_HOST=db
DB_PORT=5432
DB_DATABASE=your_production_db_name
DB_USERNAME=your_production_db_user
DB_PASSWORD=your_strong_production_db_password

# Redis Connection (must match docker-compose.yml)
REDIS_HOST=redis
REDIS_PORT=6379
```

**3. Configure Docker Compose**

A `docker-compose.yml` file is required to run the application and its services (database, Redis). You will need to create one. Here is a basic example:

```yaml
version: '3.8'

services:
  app:
    image: your_dockerhub_username/dccpadminv2:latest # <-- IMPORTANT: Change this
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./.env:/app/.env
      - storage:/app/storage
    depends_on:
      - db
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  db:
    image: postgres:15-alpine
    restart: unless-stopped
    volumes:
      - pgdata:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: ${DB_DATABASE}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    restart: unless-stopped

volumes:
  pgdata:
  storage:
```
**Important:** Replace `your_dockerhub_username` with your actual Docker Hub username.

**4. Pull the Image and Start the Application**

Pull the latest image from Docker Hub:
```bash
docker pull your_dockerhub_username/dccpadminv2:latest # <-- IMPORTANT: Change this
```

Then, start the services using Docker Compose:
```bash
docker-compose up -d
```

The first time you run this, the application's entrypoint script will:
- Generate an `APP_KEY` if one is not set.
- Run database migrations.

Your application should now be running and accessible at `http://your_server_ip:8000`.

---

## System Overview

This section provides a high-level overview of the system's features and user roles.

### Key Features
- **Automated Workflows**: Email notifications for enrollment and automated PDF generation for assessments and schedules.
- **Data Integrity**: Enforces clearance checks, prevents schedule conflicts, and tracks records by academic period.
- **Document Management**: Centralized creation and storage for assessment forms, class schedules, and transaction records.

### User Roles
- **Academic Head**: Manages academic schedules, class configurations, and curriculum.
- **Head Department**: Processes student enrollments, verifies clearances, and manages class assignments.
- **Cashier**: Handles all financial transactions, including tuition payments and receipt generation.
- **System Administrator**: Maintains system settings, user accounts, and overall technical health.

---
*Last Updated: 2024 | DCCP Admin System V2*