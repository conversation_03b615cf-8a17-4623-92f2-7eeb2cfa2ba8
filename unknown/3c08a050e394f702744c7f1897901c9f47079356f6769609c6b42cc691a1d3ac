#!/bin/bash

# Apply <PERSON><PERSON> performance optimizations
echo "Applying Laravel performance optimizations..."

# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# Optimize Composer autoloader
composer install --optimize-autoloader --no-dev

# Enable <PERSON><PERSON>'s built-in OPcache
echo "Optimizing Laravel OPcache..."
echo "opcache.enable=1" >> /etc/php/conf.d/opcache.ini
echo "opcache.memory_consumption=128" >> /etc/php/conf.d/opcache.ini
echo "opcache.interned_strings_buffer=8" >> /etc/php/conf.d/opcache.ini
echo "opcache.max_accelerated_files=4000" >> /etc/php/conf.d/opcache.ini
echo "opcache.revalidate_freq=60" >> /etc/php/conf.d/opcache.ini
echo "opcache.fast_shutdown=1" >> /etc/php/conf.d/opcache.ini

# Apply Nginx optimizations
echo "Applying Nginx optimizations..."
sudo cp /home/<USER>/Projects/DccpAdminV2/optimized_nginx.conf /etc/nginx/sites-available/dccpadmin.conf
sudo systemctl restart nginx

# Apply PHP-FPM optimizations
echo "Applying PHP-FPM optimizations..."
sudo cp /home/<USER>/Projects/DccpAdminV2/optimized_php_fpm.conf /etc/php/php-fpm.d/www.conf
sudo systemctl restart php-fpm

echo "Performance optimizations completed!"
