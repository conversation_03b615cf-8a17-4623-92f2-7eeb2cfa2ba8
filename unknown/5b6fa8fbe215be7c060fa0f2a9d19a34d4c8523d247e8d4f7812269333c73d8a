# Stage 1: PHP Dependencies
# This stage installs composer dependencies and creates a base for the frontend build.
FROM dunglas/frankenphp:1.4.0-php8.4-alpine AS php-builder
WORKDIR /app
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

RUN install-php-extensions \
    pcntl intl pdo_mysql pdo_pgsql pgsql redis zip bcmath gd exif opcache sockets curl

COPY composer.json composer.lock ./
RUN composer install --no-dev --no-interaction --no-plugins --no-scripts --prefer-dist

COPY . .

# Stage 2: Frontend Assets
# This stage builds the frontend assets using Node. It copies the full application
# source from the php-builder stage to ensure it has the composer vendor directory.
FROM node:20-alpine AS frontend-builder
WORKDIR /app
COPY --from=php-builder /app .
RUN npm install
RUN npm run build

# Stage 3: Production Image
# This is the final, optimized image for production.
FROM dunglas/frankenphp:1.4.0-php8.4 AS production
WORKDIR /app

# Install runtime dependencies and Node.js 22 with latest npm for Browsershot
ENV NODE_VERSION=22.16.0

RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client redis-server curl chromium-driver chromium ca-certificates \
    && curl -fsSL https://nodejs.org/dist/v$NODE_VERSION/node-v$NODE_VERSION-linux-x64.tar.xz -o /tmp/node.tar.xz \
    && mkdir -p /usr/local/lib/nodejs \
    && tar -xJf /tmp/node.tar.xz -C /usr/local/lib/nodejs \
    && ln -sf /usr/local/lib/nodejs/node-v$NODE_VERSION-linux-x64/bin/node /usr/local/bin/node \
    && ln -sf /usr/local/lib/nodejs/node-v$NODE_VERSION-linux-x64/bin/npm /usr/local/bin/npm \
    && ln -sf /usr/local/lib/nodejs/node-v$NODE_VERSION-linux-x64/bin/npx /usr/local/bin/npx \
    && npm install -g npm@latest \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/*

# Create user, group, and directories
RUN addgroup -g 1000 appgroup && \
    adduser -u 1000 -G appgroup -h /app -s /bin/sh -D appuser && \
    mkdir -p /data/caddy /config/caddy /home/<USER>/share/caddy /var/log && \
    chmod -R 755 /data /config /home/<USER>/var/log && \
    chown -R appuser:appgroup /data /config /home/<USER>/var/log && \
    ln -sf /usr/bin/chromium-browser /sbin/chromium

# Set environment variables
ENV APP_ENV=production \
    APP_DEBUG=false \
    OCTANE_SERVER=frankenphp \
    XDG_CONFIG_HOME=/config \
    XDG_DATA_HOME=/data \
    PHP_INI_DIR=/usr/local/etc/php \
    COMPOSER_ALLOW_SUPERUSER=1 \
    CHROME_PATH=/usr/bin/chromium-browser \
    PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Copy PHP configuration
COPY docker/php/production.ini $PHP_INI_DIR/conf.d/
RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

# Copy application code from php-builder (it has the vendor dir and all source)
COPY --from=php-builder --chown=appuser:appgroup /app .

# Copy compiled assets from frontend-builder
COPY --from=frontend-builder --chown=appuser:appgroup /app/public/build ./public/build

# Create Laravel's cache and storage directories
RUN mkdir -p bootstrap/cache storage/framework/sessions storage/framework/views storage/framework/cache/data storage/logs storage/app/public

# Set permissions and run final optimizations
RUN chown -R appuser:appgroup storage bootstrap/cache && \
    chmod -R 775 storage bootstrap/cache && \
    php artisan config:cache && \
    php artisan route:cache && \
    php artisan view:cache

# Remove development files
RUN rm -rf tests node_modules docker .git* .env

# Entrypoint
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh && \
    chown appuser:appgroup /app/docker-entrypoint.sh

USER appuser

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

EXPOSE 8000

ENTRYPOINT ["/app/docker-entrypoint.sh"]
CMD ["php", "artisan", "octane:start", "--server=frankenphp", "--host=0.0.0.0", "--port=8000"]
