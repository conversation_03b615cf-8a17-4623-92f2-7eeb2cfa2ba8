#!/bin/sh

set -e

# Check if .env file exists. If not, copy .env.example
if [ ! -f /app/.env ]; then
    echo "Creating .env file..."
    cp /app/.env.example /app/.env
fi

# Generate application key
php artisan key:generate

# Run database migrations
php artisan migrate --force

# Execute the command passed to the script (i.e., the Dockerfile's CMD)
echo "Starting Octane server..."
exec "$@"
set -e

echo "Starting Laravel application..."

# Install netcat if not available (for health checks)
if ! command -v nc >/dev/null 2>&1; then
    apk add --no-cache netcat-openbsd
fi

# Wait for external services (optional)
if [ -n "$DB_HOST" ] && [ -n "$DB_PORT" ]; then
    echo "Waiting for database at $DB_HOST:$DB_PORT..."
    timeout=60
    while ! nc -z "$DB_HOST" "$DB_PORT" && [ $timeout -gt 0 ]; do
        sleep 1
        timeout=$((timeout - 1))
    done
    if [ $timeout -eq 0 ]; then
        echo "Warning: Database connection timeout, proceeding anyway..."
    else
        echo "Database is ready!"
    fi
fi

if [ -n "$REDIS_HOST" ] && [ -n "$REDIS_PORT" ]; then
    echo "Waiting for Redis at $REDIS_HOST:$REDIS_PORT..."
    timeout=60
    while ! nc -z "$REDIS_HOST" "$REDIS_PORT" && [ $timeout -gt 0 ]; do
        sleep 1
        timeout=$((timeout - 1))
    done
    if [ $timeout -eq 0 ]; then
        echo "Warning: Redis connection timeout, proceeding anyway..."
    else
        echo "Redis is ready!"
    fi
fi

# Generate app key if not exists
if [ -z "$APP_KEY" ]; then
    echo "Generating application key..."
    php artisan key:generate --force
fi

# Run Laravel optimizations
echo "Optimizing Laravel..."
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
php artisan icon:cache

# Run migrations if enabled
if [ "$RUN_MIGRATIONS" = "true" ]; then
    echo "Running database migrations..."
    php artisan migrate --force
fi

# Create storage link
php artisan storage:link || true

# Ensure proper permissions
chown -R appuser:appgroup /app/storage /app/bootstrap/cache

echo "Starting FrankenPHP server..."
exec "$@"