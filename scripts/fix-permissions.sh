#!/bin/bash

# Fix Permissions Script for DccpAdminV2
# This script fixes file and directory permissions for Laravel storage and logs

set -e  # Exit on any error

echo "🔧 Fixing Laravel Storage and Log Permissions"
echo "=============================================="
echo

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Check if running in correct directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: This script must be run from the Laravel root directory"
    echo "Current directory: $(pwd)"
    echo "Expected files: artisan, composer.json"
    exit 1
fi

log "Starting permission fixes..."

# Create necessary directories
log "Creating storage directories..."
mkdir -p storage/logs
mkdir -p storage/framework/cache
mkdir -p storage/framework/sessions
mkdir -p storage/framework/views
mkdir -p storage/app/private
mkdir -p storage/app/browsershot-temp
mkdir -p storage/app/public

# Set directory permissions (775 allows group write)
log "Setting directory permissions..."
find storage -type d -exec chmod 775 {} \;

# Set file permissions (664 allows group write)
log "Setting file permissions..."
find storage -type f -exec chmod 664 {} \;

# Special handling for log file
log "Fixing log file permissions..."
touch storage/logs/laravel.log
chmod 666 storage/logs/laravel.log  # Make it world writable to ensure it works

# Create Chrome temp directories
log "Creating Chrome temp directories..."
mkdir -p /tmp/chrome-crashpad
mkdir -p /tmp/chrome-user-data
chmod 777 /tmp/chrome-crashpad
chmod 777 /tmp/chrome-user-data

# Try to change ownership if running as root
if [ "$(id -u)" = "0" ]; then
    log "Running as root - setting ownership to www-data..."
    chown -R www-data:www-data storage/
    chgrp -R www-data storage/
else
    log "Not running as root - ownership unchanged"
fi

# Test log file writing
log "Testing log file write permissions..."
TEST_MESSAGE="[$(date '+%Y-%m-%d %H:%i:%s')] Permission fix test - $(date)"
if echo "$TEST_MESSAGE" >> storage/logs/laravel.log; then
    echo "✅ Log file write test successful"
else
    echo "❌ Log file write test failed"
    echo "Attempting fallback fix..."
    chmod 777 storage/logs/laravel.log
    if echo "$TEST_MESSAGE" >> storage/logs/laravel.log; then
        echo "✅ Log file write test successful after fallback"
    else
        echo "❌ Log file still not writable"
    fi
fi

# Show final permissions
echo
echo "📋 Final Permission Summary:"
echo "============================"
echo "Storage directory: $(stat -c '%a' storage)"
echo "Logs directory: $(stat -c '%a' storage/logs)"
if [ -f "storage/logs/laravel.log" ]; then
    echo "Laravel log file: $(stat -c '%a' storage/logs/laravel.log)"
fi

# Show ownership
if command -v stat >/dev/null 2>&1; then
    echo "Storage ownership: $(stat -c '%U:%G' storage)"
fi

echo
echo "🎉 Permission fixes completed!"
echo
echo "💡 Next steps:"
echo "  1. Test your Filament actions again"
echo "  2. If still having issues, restart your web server"
echo "  3. Run: php artisan fix:log-permissions"
echo "  4. Run: php artisan verify:deployment --fix"
echo

# Optional: Run Laravel's own permission fix
if command -v php >/dev/null 2>&1; then
    echo "🔄 Running Laravel permission checks..."
    if php artisan fix:log-permissions 2>/dev/null; then
        echo "✅ Laravel permission fix successful"
    else
        echo "⚠️  Laravel permission fix command not available or failed"
    fi
fi

echo "✅ All fixes applied successfully!"
exit 0
