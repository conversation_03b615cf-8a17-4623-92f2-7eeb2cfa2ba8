#!/bin/bash

# Database Optimization Script for Laravel DCCP Admin V2
# This script optimizes PostgreSQL database performance by adding indexes, 
# configuring settings, and performing maintenance tasks

DB_NAME="dccpadmin2"
DB_USER="user_t3SRr2"
DB_HOST="localhost"
DB_PORT="54321"
DB_PASSWORD="password_zfdX7n"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Database Optimization for Laravel DCCP Admin V2${NC}"
echo -e "${BLUE}Database: $DB_NAME${NC}"
echo -e "${BLUE}===============================================${NC}"

# Function to execute SQL with error handling
execute_sql() {
    local sql="$1"
    local description="$2"
    
    echo -e "${YELLOW}⏳ $description...${NC}"
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$sql" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $description completed successfully${NC}"
    else
        echo -e "${RED}❌ $description failed${NC}"
    fi
}

# Function to create indexes with error handling
create_index() {
    local table=$1
    local columns=$2
    local index_name="idx_${table}_$(echo $columns | tr ',' '_' | tr ' ' '_')"
    
    execute_sql "CREATE INDEX IF NOT EXISTS ${index_name} ON ${table} (${columns});" "Creating index ${index_name}"
}

# Function to create composite indexes
create_composite_index() {
    local table=$1
    local columns=$2
    local index_name="idx_${table}_$(echo $columns | tr ',' '_' | tr ' ' '_' | tr '(' '_' | tr ')' '_')"
    
    execute_sql "CREATE INDEX IF NOT EXISTS ${index_name} ON ${table} (${columns});" "Creating composite index ${index_name}"
}

# Function to create partial indexes
create_partial_index() {
    local table=$1
    local columns=$2
    local condition=$3
    local index_name="idx_${table}_$(echo $columns | tr ',' '_' | tr ' ' '_')_partial"
    
    execute_sql "CREATE INDEX IF NOT EXISTS ${index_name} ON ${table} (${columns}) WHERE ${condition};" "Creating partial index ${index_name}"
}

echo -e "\n${BLUE}📊 Step 1: Configuring PostgreSQL Settings${NC}"
echo -e "${BLUE}===========================================${NC}"

# Enable slow query logging
execute_sql "ALTER SYSTEM SET log_min_duration_statement = 500;" "Enabling slow query logging (500ms threshold)"
execute_sql "ALTER SYSTEM SET log_statement_stats = off;" "Disabling statement stats logging"
execute_sql "ALTER SYSTEM SET log_duration = on;" "Enabling query duration logging"
execute_sql "ALTER SYSTEM SET log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h ';" "Setting log line prefix"

# Optimize memory settings
execute_sql "ALTER SYSTEM SET shared_buffers = '256MB';" "Setting shared buffers"
execute_sql "ALTER SYSTEM SET effective_cache_size = '1GB';" "Setting effective cache size"
execute_sql "ALTER SYSTEM SET maintenance_work_mem = '64MB';" "Setting maintenance work memory"
execute_sql "ALTER SYSTEM SET checkpoint_completion_target = 0.9;" "Setting checkpoint completion target"
execute_sql "ALTER SYSTEM SET wal_buffers = '16MB';" "Setting WAL buffers"
execute_sql "ALTER SYSTEM SET default_statistics_target = 100;" "Setting default statistics target"

# Enable auto-vacuum and optimize settings
execute_sql "ALTER SYSTEM SET autovacuum = on;" "Enabling autovacuum"
execute_sql "ALTER SYSTEM SET autovacuum_max_workers = 3;" "Setting autovacuum max workers"
execute_sql "ALTER SYSTEM SET autovacuum_naptime = '1min';" "Setting autovacuum naptime"

# Reload configuration
execute_sql "SELECT pg_reload_conf();" "Reloading PostgreSQL configuration"

echo -e "\n${BLUE}🔍 Step 2: Creating Primary Indexes for Core Tables${NC}"
echo -e "${BLUE}=================================================${NC}"

# Users table indexes (authentication, search, filtering)
create_index "users" "email"
create_index "users" "account_status"
create_index "users" "last_login_at"
create_index "users" "department"
create_index "users" "employee_id"
create_index "users" "manager_id"
create_index "users" "created_at"
create_index "users" "active_status"
create_index "users" "email_verified_at"
create_composite_index "users" "account_status, last_login_at"
create_composite_index "users" "department, position"

# Students table indexes (most frequently accessed)
create_index "students" "email"
create_index "students" "course_id"
create_index "students" "academic_year"
create_index "students" "status"
create_index "students" "student_id"
create_index "students" "clearance_status"
create_index "students" "year_graduated"
create_index "students" "created_at"
create_index "students" "updated_at"
create_index "students" "deleted_at"
create_composite_index "students" "course_id, academic_year"
create_composite_index "students" "last_name, first_name"
create_composite_index "students" "status, course_id"

# Classes table indexes (class management, scheduling)
create_index "classes" "subject_id"
create_index "classes" "faculty_id"
create_index "classes" "academic_year"
create_index "classes" "semester"
create_index "classes" "school_year"
create_index "classes" "room_id"
create_index "classes" "schedule_id"
create_index "classes" "classification"
create_index "classes" "shs_track_id"
create_index "classes" "shs_strand_id"
create_composite_index "classes" "academic_year, semester"
create_composite_index "classes" "school_year, semester"
create_composite_index "classes" "faculty_id, academic_year, semester"
create_composite_index "classes" "subject_id, academic_year, semester"

# Class enrollments table indexes (student-class relationships)
create_index "class_enrollments" "class_id"
create_index "class_enrollments" "student_id"
create_index "class_enrollments" "status"
create_index "class_enrollments" "verified_by"
create_index "class_enrollments" "verified_at"
create_index "class_enrollments" "is_grades_finalized"
create_index "class_enrollments" "is_grades_verified"
create_index "class_enrollments" "created_at"
create_index "class_enrollments" "updated_at"
create_index "class_enrollments" "deleted_at"
create_composite_index "class_enrollments" "class_id, student_id"
create_composite_index "class_enrollments" "student_id, status"
create_composite_index "class_enrollments" "class_id, status"

# Student enrollment table indexes (enrollment management)
create_index "student_enrollment" "student_id"
create_index "student_enrollment" "course_id"
create_index "student_enrollment" "status"
create_index "student_enrollment" "semester"
create_index "student_enrollment" "academic_year"
create_index "student_enrollment" "school_year"
create_index "student_enrollment" "payment_method"
create_index "student_enrollment" "created_at"
create_index "student_enrollment" "updated_at"
create_index "student_enrollment" "deleted_at"
create_composite_index "student_enrollment" "student_id, school_year"
create_composite_index "student_enrollment" "course_id, academic_year, semester"
create_composite_index "student_enrollment" "status, school_year"

# Faculty table indexes (faculty management)
create_index "faculty" "email"
create_index "faculty" "department"
create_index "faculty" "status"
create_index "faculty" "created_at"
create_index "faculty" "updated_at"
create_composite_index "faculty" "department, status"

# Subject table indexes (subject management)
create_index "subject" "code"
create_index "subject" "course_id"
create_index "subject" "academic_year"
create_index "subject" "semester"
create_index "subject" "classification"
create_index "subject" "is_credited"
create_index "subject" "created_at"
create_index "subject" "updated_at"
create_composite_index "subject" "course_id, academic_year, semester"
create_composite_index "subject" "code, course_id"

echo -e "\n${BLUE}⚡ Step 3: Creating Transaction and Financial Indexes${NC}"
echo -e "${BLUE}=================================================${NC}"

# Student transactions (financial records)
create_index "student_transactions" "student_id"
create_index "student_transactions" "transaction_id"
create_index "student_transactions" "status"
create_index "student_transactions" "amount"
create_index "student_transactions" "created_at"
create_index "student_transactions" "updated_at"
create_composite_index "student_transactions" "student_id, status"
create_composite_index "student_transactions" "student_id, created_at"

# Admin transactions
create_index "admin_transactions" "admin_id"
create_index "admin_transactions" "transaction_id"
create_index "admin_transactions" "status"
create_index "admin_transactions" "created_at"
create_index "admin_transactions" "updated_at"
create_composite_index "admin_transactions" "admin_id, status"

# Student tuition (if exists)
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\dt student_tuition" 2>/dev/null | grep -q "student_tuition"; then
    create_index "student_tuition" "student_id"
    create_index "student_tuition" "academic_year"
    create_index "student_tuition" "semester"
    create_index "student_tuition" "status"
    create_composite_index "student_tuition" "student_id, academic_year, semester"
fi

echo -e "\n${BLUE}🔐 Step 4: Creating Security and Audit Indexes${NC}"
echo -e "${BLUE}=============================================${NC}"

# Authentication logs (if exists)
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\dt authentication_log" 2>/dev/null | grep -q "authentication_log"; then
    create_index "authentication_log" "authenticatable_id"
    create_index "authentication_log" "login_at"
    create_index "authentication_log" "ip_address"
    create_composite_index "authentication_log" "authenticatable_id, login_at"
fi

# User activity logs
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\dt user_activity_logs" 2>/dev/null | grep -q "user_activity_logs"; then
    create_index "user_activity_logs" "user_id"
    create_index "user_activity_logs" "created_at"
    create_index "user_activity_logs" "activity_type"
    create_composite_index "user_activity_logs" "user_id, created_at"
fi

# Sessions table
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\dt sessions" 2>/dev/null | grep -q "sessions"; then
    create_index "sessions" "user_id"
    create_index "sessions" "last_activity"
    create_composite_index "sessions" "user_id, last_activity"
fi

echo -e "\n${BLUE}📚 Step 5: Creating Permission and Role Indexes${NC}"
echo -e "${BLUE}=============================================${NC}"

# Model has permissions
create_index "model_has_permissions" "model_id"
create_index "model_has_permissions" "permission_id"
create_index "model_has_permissions" "model_type"
create_composite_index "model_has_permissions" "model_id, model_type"

# Model has roles
create_index "model_has_roles" "model_id"
create_index "model_has_roles" "role_id"
create_index "model_has_roles" "model_type"
create_composite_index "model_has_roles" "model_id, model_type"

# Role has permissions
create_index "role_has_permissions" "role_id"
create_index "role_has_permissions" "permission_id"

echo -e "\n${BLUE}🎯 Step 6: Creating Specialized Indexes${NC}"
echo -e "${BLUE}=====================================${NC}"

# Partial indexes for active records only
create_partial_index "users" "email, account_status" "account_status = 'active'"
create_partial_index "students" "course_id, academic_year" "deleted_at IS NULL"
create_partial_index "class_enrollments" "class_id, student_id" "deleted_at IS NULL"
create_partial_index "student_enrollment" "student_id, school_year" "deleted_at IS NULL AND status = 'enrolled'"

# Full-text search indexes (if using PostgreSQL full-text search)
if command -v psql >/dev/null 2>&1; then
    execute_sql "CREATE INDEX IF NOT EXISTS idx_students_fulltext ON students USING gin(to_tsvector('english', first_name || ' ' || last_name || ' ' || COALESCE(email, '')));" "Creating full-text search index for students"
    execute_sql "CREATE INDEX IF NOT EXISTS idx_users_fulltext ON users USING gin(to_tsvector('english', name || ' ' || email));" "Creating full-text search index for users"
fi

echo -e "\n${BLUE}🧹 Step 7: Database Maintenance${NC}"
echo -e "${BLUE}===============================${NC}"

# Update table statistics
execute_sql "ANALYZE;" "Updating table statistics"

# Vacuum and analyze all tables
execute_sql "VACUUM ANALYZE;" "Running VACUUM ANALYZE on all tables"

# Reindex system catalogs
execute_sql "REINDEX SYSTEM ${DB_NAME};" "Reindexing system catalogs"

echo -e "\n${BLUE}📈 Step 8: Performance Monitoring Setup${NC}"
echo -e "${BLUE}=======================================${NC}"

# Enable pg_stat_statements for query performance monitoring
execute_sql "CREATE EXTENSION IF NOT EXISTS pg_stat_statements;" "Installing pg_stat_statements extension"

# Enable auto_explain for slow query analysis
execute_sql "ALTER SYSTEM SET auto_explain.log_min_duration = '1s';" "Enabling auto explain for slow queries"
execute_sql "ALTER SYSTEM SET auto_explain.log_analyze = on;" "Enabling analyze in auto explain"
execute_sql "ALTER SYSTEM SET auto_explain.log_verbose = on;" "Enabling verbose auto explain"

# Reload configuration one more time
execute_sql "SELECT pg_reload_conf();" "Final configuration reload"

echo -e "\n${BLUE}📊 Step 9: Performance Analysis${NC}"
echo -e "${BLUE}=============================${NC}"

# Show database size
echo -e "${YELLOW}📦 Database Size Analysis:${NC}"
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT pg_size_pretty(pg_database_size('$DB_NAME')) as database_size;"

# Show table sizes
echo -e "\n${YELLOW}📋 Top 10 Largest Tables:${NC}"
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size FROM pg_tables WHERE schemaname = 'public' ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC LIMIT 10;"

# Show index usage
echo -e "\n${YELLOW}📊 Index Usage Statistics:${NC}"
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT schemaname, relname as tablename, indexrelname as indexname, idx_tup_read, idx_tup_fetch FROM pg_stat_user_indexes WHERE schemaname = 'public' ORDER BY idx_tup_read + idx_tup_fetch DESC LIMIT 10;"

echo -e "\n${GREEN}🎉 Database optimization completed successfully!${NC}"
echo -e "${GREEN}=============================================${NC}"
echo -e "${GREEN}✅ Slow query logging enabled${NC}"
echo -e "${GREEN}✅ Performance indexes created${NC}"
echo -e "${GREEN}✅ Database maintenance completed${NC}"
echo -e "${GREEN}✅ Monitoring tools configured${NC}"
echo -e "\n${YELLOW}📝 Next Steps:${NC}"
echo -e "${YELLOW}1. Monitor slow query logs in PostgreSQL log files${NC}"
echo -e "${YELLOW}2. Review query performance using pg_stat_statements${NC}"
echo -e "${YELLOW}3. Consider implementing Redis caching for frequently accessed data${NC}"
echo -e "${YELLOW}4. Run this script monthly for maintenance${NC}"
echo -e "${YELLOW}5. Monitor index usage and remove unused indexes if needed${NC}"

echo -e "\n${BLUE}📝 Laravel Application Optimization Tips:${NC}"
echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}Consider implementing these in your Laravel app:${NC}"
echo -e "${BLUE}• Use eager loading: Model::with(['relation1', 'relation2'])${NC}"
echo -e "${BLUE}• Implement caching: Cache::remember() for expensive queries${NC}"
echo -e "${BLUE}• Use pagination: paginate() instead of get() for large datasets${NC}"
echo -e "${BLUE}• Add select() clauses to limit returned columns${NC}"
echo -e "${BLUE}• Use whereHas() instead of joins when appropriate${NC}"
echo -e "${BLUE}• Implement database query caching in config/cache.php${NC}"

echo -e "\n${GREEN}🚀 Optimization complete! Your Laravel app should be faster now.${NC}"

