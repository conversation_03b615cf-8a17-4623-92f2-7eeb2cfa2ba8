#!/bin/bash

# Test Deployment Script for DccpAdminV2 Browsershot Configuration
# This script tests the entire PDF generation pipeline in the deployed environment

set -e  # Exit on any error

echo "🚀 Starting Deployment Test for Browsershot PDF Generation"
echo "==========================================================="
echo

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check command success
check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ $1 - PASSED"
    else
        echo "❌ $1 - FAILED"
        exit 1
    fi
}

# Test 1: Environment Variables
log "Testing Environment Variables..."
php -r "
\$vars = ['CHROME_PATH', 'NODE_BINARY_PATH', 'BROWSERSHOT_NO_SANDBOX'];
\$missing = [];
foreach (\$vars as \$var) {
    if (!env(\$var)) \$missing[] = \$var;
}
if (!empty(\$missing)) {
    echo 'Missing: ' . implode(', ', \$missing) . PHP_EOL;
    exit(1);
}
echo 'All required environment variables are set' . PHP_EOL;
"
check_success "Environment Variables Check"

# Test 2: Binary Paths
log "Testing Binary Paths..."
if [ -n "$CHROME_PATH" ] && [ -x "$CHROME_PATH" ]; then
    echo "Chrome binary found at: $CHROME_PATH"
    $CHROME_PATH --version --no-sandbox --disable-dev-shm-usage 2>/dev/null || echo "Chrome version check failed but binary exists"
else
    echo "❌ Chrome binary not found or not executable at: ${CHROME_PATH:-'not set'}"
    exit 1
fi

if [ -n "$NODE_BINARY_PATH" ] && [ -x "$NODE_BINARY_PATH" ]; then
    echo "Node binary found at: $NODE_BINARY_PATH"
    $NODE_BINARY_PATH --version 2>/dev/null || echo "Node version check failed but binary exists"
else
    echo "❌ Node binary not found or not executable at: ${NODE_BINARY_PATH:-'not set'}"
    exit 1
fi
check_success "Binary Paths Check"

# Test 3: Required Directories
log "Testing Required Directories..."
php artisan verify:deployment --fix
check_success "Directory Setup"

# Test 4: Simple Browsershot Test
log "Testing Simple Browsershot..."
php artisan test:browsershot-simple
check_success "Simple Browsershot Test"

# Test 5: Full Browsershot Test
log "Testing Full Browsershot Service..."
php artisan test:browsershot
check_success "Full Browsershot Test"

# Test 6: Queue Configuration Test
log "Testing Queue Configuration..."
php artisan queue:work --once --timeout=30 &
QUEUE_PID=$!
sleep 2
if kill -0 $QUEUE_PID 2>/dev/null; then
    kill $QUEUE_PID 2>/dev/null || true
    echo "Queue worker started successfully"
else
    echo "⚠️  Queue worker test inconclusive"
fi
check_success "Queue Configuration Test"

# Test 7: PDF Generation Job Test
log "Testing PDF Generation Job..."
php -r "
try {
    \$enrollment = App\Models\StudentEnrollment::first();
    if (!\$enrollment) {
        echo 'No student enrollment found for testing' . PHP_EOL;
        exit(1);
    }

    \$job = new App\Jobs\GenerateAssessmentPdfJob(\$enrollment);
    \$jobId = \$job->getJobId();

    // Test synchronous execution
    \$job->handle();
    echo 'PDF generation job executed successfully' . PHP_EOL;
} catch (Exception \$e) {
    echo 'PDF generation job failed: ' . \$e->getMessage() . PHP_EOL;
    exit(1);
}
"
check_success "PDF Generation Job Test"

# Test 8: Service Integration Test
log "Testing BrowsershotService Integration..."
php -r "
try {
    \$html = '<html><body><h1>Integration Test</h1><p>Generated at: ' . now() . '</p></body></html>';
    \$path = storage_path('app/integration-test.pdf');

    \$success = App\Services\BrowsershotService::generatePdf(\$html, \$path, [
        'format' => 'A4',
        'landscape' => true,
        'print_background' => true,
        'timeout' => 60
    ]);

    if (!\$success || !file_exists(\$path)) {
        throw new Exception('PDF was not generated');
    }

    \$size = filesize(\$path);
    if (\$size < 1000) {
        throw new Exception('PDF file is too small (' . \$size . ' bytes)');
    }

    unlink(\$path);
    echo 'Integration test passed - PDF generated successfully (' . \$size . ' bytes)' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Integration test failed: ' . \$e->getMessage() . PHP_EOL;
    exit(1);
}
"
check_success "Service Integration Test"

# Test 9: Memory and Performance Test
log "Testing Memory and Performance..."
php -r "
\$start = microtime(true);
\$memStart = memory_get_peak_usage(true);

try {
    \$html = str_repeat('<p>Performance test content</p>', 1000);
    \$path = storage_path('app/performance-test.pdf');

    \$success = App\Services\BrowsershotService::generatePdf(\$html, \$path);

    \$end = microtime(true);
    \$memEnd = memory_get_peak_usage(true);

    if (!\$success) {
        throw new Exception('Performance test PDF generation failed');
    }

    \$duration = round(\$end - \$start, 2);
    \$memUsed = round((\$memEnd - \$memStart) / 1024 / 1024, 2);

    echo 'Performance test completed in ' . \$duration . 's using ' . \$memUsed . 'MB memory' . PHP_EOL;

    if (\$duration > 30) {
        echo '⚠️  PDF generation took longer than expected (' . \$duration . 's)' . PHP_EOL;
    }

    unlink(\$path);
} catch (Exception \$e) {
    echo 'Performance test failed: ' . \$e->getMessage() . PHP_EOL;
    exit(1);
}
"
check_success "Memory and Performance Test"

# Test 10: Chrome Process Test
log "Testing Chrome Process Management..."
CHROME_PROCESSES_BEFORE=$(pgrep -c chromium || echo "0")
php -r "
\$html = '<html><body><h1>Process Test</h1></body></html>';
\$path = storage_path('app/process-test.pdf');
App\Services\BrowsershotService::generatePdf(\$html, \$path);
unlink(\$path);
echo 'Chrome process test completed' . PHP_EOL;
"
sleep 2
CHROME_PROCESSES_AFTER=$(pgrep -c chromium || echo "0")

if [ "$CHROME_PROCESSES_AFTER" -le "$CHROME_PROCESSES_BEFORE" ]; then
    echo "Chrome processes cleaned up properly"
else
    echo "⚠️  Chrome processes may not be cleaned up properly"
    echo "Processes before: $CHROME_PROCESSES_BEFORE, after: $CHROME_PROCESSES_AFTER"
fi
check_success "Chrome Process Management Test"

# Final Summary
echo
echo "🎉 All Deployment Tests Passed!"
echo "================================"
echo
echo "✅ Environment variables configured correctly"
echo "✅ Binary paths working"
echo "✅ Directory structure setup"
echo "✅ Browsershot service functional"
echo "✅ PDF generation working"
echo "✅ Queue system ready"
echo "✅ Memory usage acceptable"
echo "✅ Chrome process management working"
echo
echo "Your deployment is ready for production PDF generation!"
echo
echo "💡 Quick commands for monitoring:"
echo "  - Check logs: tail -f storage/logs/laravel.log"
echo "  - Test PDF: php artisan test:browsershot-simple"
echo "  - Verify setup: php artisan verify:deployment"
echo
echo "🔧 Configuration in use:"
echo "  - Chrome: ${CHROME_PATH:-'auto-detected'}"
echo "  - Node: ${NODE_BINARY_PATH:-'auto-detected'}"
echo "  - Timeout: ${BROWSERSHOT_TIMEOUT:-'120'}s"
echo "  - No Sandbox: ${BROWSERSHOT_NO_SANDBOX:-'true'}"
echo

exit 0
