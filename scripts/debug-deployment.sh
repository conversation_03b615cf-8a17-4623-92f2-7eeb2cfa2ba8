#!/bin/bash

# Debug Deployment Script for DccpAdminV2
# This script diagnoses common deployment issues

set -e

echo "🔍 Debugging Deployment Issues"
echo "=============================="
echo

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check command success
check_command() {
    if command -v "$1" >/dev/null 2>&1; then
        echo "✅ $1 is available: $(which $1)"
        return 0
    else
        echo "❌ $1 is not available"
        return 1
    fi
}

# Function to check process status
check_process() {
    if pgrep -f "$1" >/dev/null 2>&1; then
        echo "✅ $1 is running"
        pgrep -f "$1" | head -5 | while read pid; do
            echo "  PID: $pid"
        done
        return 0
    else
        echo "❌ $1 is not running"
        return 1
    fi
}

log "Starting deployment diagnosis..."

echo "🔧 System Information"
echo "===================="
echo "OS: $(uname -a)"
echo "User: $(whoami)"
echo "Working directory: $(pwd)"
echo "PATH: $PATH"
echo

echo "📋 Required Commands"
echo "==================="
check_command "php"
check_command "php-fpm"
check_command "nginx"
check_command "node"
check_command "chromium"
check_command "supervisord"
echo

echo "🔍 PHP Information"
echo "=================="
if command -v php >/dev/null 2>&1; then
    echo "PHP Version: $(php --version | head -1)"
    echo "PHP-FPM Version:"
    php-fpm --version 2>/dev/null || echo "  ❌ php-fpm version check failed"
    echo "PHP Modules:"
    php -m | grep -E "(fpm|curl|json|mbstring)" || echo "  ⚠️  Some modules may be missing"
else
    echo "❌ PHP not found"
fi
echo

echo "🌐 Nginx Information"
echo "==================="
if command -v nginx >/dev/null 2>&1; then
    echo "Nginx Version: $(nginx -v 2>&1)"
    echo "Nginx Configuration Test:"
    nginx -t 2>&1 || echo "  ❌ Nginx configuration test failed"

    if [ -f "/etc/nginx.conf" ]; then
        echo "✅ Nginx config file exists: /etc/nginx.conf"
    else
        echo "❌ Nginx config file missing: /etc/nginx.conf"
    fi
else
    echo "❌ Nginx not found"
fi
echo

echo "🎯 Process Status"
echo "================"
check_process "nginx"
check_process "php-fpm"
check_process "supervisord"
check_process "queue:work"
echo

echo "📁 File Permissions"
echo "==================="
if [ -d "/app/storage" ]; then
    echo "✅ Storage directory exists"
    echo "Storage permissions: $(stat -c '%a' /app/storage 2>/dev/null || echo 'unknown')"
    echo "Storage owner: $(stat -c '%U:%G' /app/storage 2>/dev/null || echo 'unknown')"

    if [ -f "/app/storage/logs/laravel.log" ]; then
        echo "✅ Laravel log file exists"
        echo "Log file permissions: $(stat -c '%a' /app/storage/logs/laravel.log)"
        echo "Log file size: $(du -h /app/storage/logs/laravel.log | cut -f1)"
    else
        echo "❌ Laravel log file missing"
    fi
else
    echo "❌ Storage directory missing"
fi
echo

echo "🚀 Supervisor Status"
echo "==================="
if command -v supervisorctl >/dev/null 2>&1; then
    echo "Supervisor processes:"
    supervisorctl status 2>/dev/null || echo "❌ Cannot connect to supervisor"
else
    echo "❌ supervisorctl not available"
fi
echo

echo "🔗 Network Status"
echo "================"
echo "Listening ports:"
netstat -tlnp 2>/dev/null | grep -E "(80|9000|22)" || ss -tlnp | grep -E "(80|9000|22)" || echo "❌ Cannot check ports"
echo

echo "📝 Recent Logs"
echo "=============="
echo "Supervisor logs (last 10 lines):"
if [ -f "/var/log/supervisord.log" ]; then
    tail -10 /var/log/supervisord.log
else
    echo "❌ Supervisor log not found"
fi

echo
echo "PHP-FPM logs (last 10 lines):"
if [ -f "/var/log/worker-phpfpm.log" ]; then
    tail -10 /var/log/worker-phpfpm.log
else
    echo "❌ PHP-FPM log not found"
fi

echo
echo "Nginx logs (last 10 lines):"
if [ -f "/var/log/worker-nginx.log" ]; then
    tail -10 /var/log/worker-nginx.log
else
    echo "❌ Nginx log not found"
fi

echo
echo "Laravel logs (last 5 lines):"
if [ -f "/app/storage/logs/laravel.log" ]; then
    tail -5 /app/storage/logs/laravel.log
else
    echo "❌ Laravel log not found"
fi
echo

echo "🔬 Environment Variables"
echo "========================"
echo "CHROME_PATH: ${CHROME_PATH:-'not set'}"
echo "NODE_BINARY_PATH: ${NODE_BINARY_PATH:-'not set'}"
echo "NPM_BINARY_PATH: ${NPM_BINARY_PATH:-'not set'}"
echo "BROWSERSHOT_NO_SANDBOX: ${BROWSERSHOT_NO_SANDBOX:-'not set'}"
echo "APP_ENV: ${APP_ENV:-'not set'}"
echo "APP_DEBUG: ${APP_DEBUG:-'not set'}"
echo

echo "🧪 Quick Tests"
echo "=============="
echo "Testing PHP-FPM socket:"
if [ -S "127.0.0.1:9000" ] || netstat -tlnp 2>/dev/null | grep ":9000" >/dev/null; then
    echo "✅ PHP-FPM appears to be listening on port 9000"
else
    echo "❌ PHP-FPM not listening on port 9000"
fi

echo "Testing Nginx configuration:"
if nginx -t >/dev/null 2>&1; then
    echo "✅ Nginx configuration is valid"
else
    echo "❌ Nginx configuration has errors"
fi

echo "Testing Laravel artisan:"
if [ -f "/app/artisan" ]; then
    cd /app
    if php artisan --version >/dev/null 2>&1; then
        echo "✅ Laravel artisan working: $(php artisan --version)"
    else
        echo "❌ Laravel artisan not working"
    fi
else
    echo "❌ Laravel artisan file not found"
fi
echo

echo "💡 Troubleshooting Suggestions"
echo "==============================="

# Check for common issues
ISSUES_FOUND=0

if ! pgrep -f "php-fpm" >/dev/null 2>&1; then
    echo "❌ PHP-FPM is not running"
    echo "   Try: supervisorctl restart worker-phpfpm_00"
    echo "   Or: php-fpm --fpm-config /assets/php-fpm.conf --nodaemonize"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

if ! pgrep -f "nginx" >/dev/null 2>&1; then
    echo "❌ Nginx is not running"
    echo "   Try: supervisorctl restart worker-nginx_00"
    echo "   Or: nginx -c /etc/nginx.conf"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

if [ ! -f "/app/storage/logs/laravel.log" ]; then
    echo "❌ Laravel log file missing"
    echo "   Try: touch /app/storage/logs/laravel.log && chmod 666 /app/storage/logs/laravel.log"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

if [ $ISSUES_FOUND -eq 0 ]; then
    echo "✅ No obvious issues found"
    echo "💡 If you're still getting 502 errors, check:"
    echo "   - Firewall settings"
    echo "   - Load balancer configuration"
    echo "   - DNS settings"
    echo "   - Application-level errors in Laravel logs"
fi

echo
echo "🚀 Quick Fix Commands"
echo "===================="
echo "# Restart all services:"
echo "supervisorctl restart all"
echo
echo "# Fix permissions:"
echo "chmod -R 775 /app/storage"
echo "chown -R www-data:www-data /app/storage"
echo
echo "# Test PHP-FPM manually:"
echo "php-fpm --fpm-config /assets/php-fpm.conf --nodaemonize --test"
echo
echo "# Test Nginx manually:"
echo "nginx -c /etc/nginx.conf -t"
echo
echo "# Check Laravel:"
echo "cd /app && php artisan config:cache"

echo
echo "🏁 Diagnosis complete!"
exit 0
