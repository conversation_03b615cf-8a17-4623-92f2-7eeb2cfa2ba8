#!/bin/bash

# Emergency PHP Server Script
# This script starts PHP's built-in server as a fallback when PHP-FPM fails
# Only use this for debugging - not recommended for production

set -e

echo "🚨 Starting Emergency PHP Built-in Server"
echo "=========================================="
echo "⚠️  WARNING: This is for debugging only!"
echo

# Check if we're in the Laravel directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: Must be run from Laravel root directory"
    exit 1
fi

# Set working directory
cd /app

# Check PHP availability
if ! command -v php >/dev/null 2>&1; then
    echo "❌ PHP not found in PATH"
    exit 1
fi

echo "✅ PHP Version: $(php --version | head -1)"

# Check if public directory exists
if [ ! -d "public" ]; then
    echo "❌ Public directory not found"
    exit 1
fi

# Set environment variables for Browsershot
export CHROME_PATH="${CHROME_PATH:-/nix/store/*/bin/chromium}"
export NODE_BINARY_PATH="${NODE_BINARY_PATH:-/nix/store/*/bin/node}"
export BROWSERSHOT_NO_SANDBOX="${BROWSERSHOT_NO_SANDBOX:-true}"

echo "🔧 Environment:"
echo "   Chrome: ${CHROME_PATH}"
echo "   Node: ${NODE_BINARY_PATH}"
echo "   No Sandbox: ${BROWSERSHOT_NO_SANDBOX}"

# Fix storage permissions quickly
echo "🔧 Fixing storage permissions..."
mkdir -p storage/logs storage/framework/cache storage/framework/sessions storage/framework/views
chmod -R 775 storage
touch storage/logs/laravel.log
chmod 666 storage/logs/laravel.log

# Clear caches to ensure fresh start
echo "🔄 Clearing Laravel caches..."
php artisan config:clear 2>/dev/null || echo "   Config clear failed (continuing...)"
php artisan cache:clear 2>/dev/null || echo "   Cache clear failed (continuing...)"
php artisan view:clear 2>/dev/null || echo "   View clear failed (continuing...)"

# Test basic Laravel functionality
echo "🧪 Testing Laravel..."
if php artisan --version >/dev/null 2>&1; then
    echo "✅ Laravel is working: $(php artisan --version)"
else
    echo "❌ Laravel artisan failed"
    exit 1
fi

# Start the server
HOST="0.0.0.0"
PORT="80"

echo
echo "🚀 Starting PHP built-in server..."
echo "   Host: $HOST"
echo "   Port: $PORT"
echo "   Document root: $(pwd)/public"
echo
echo "🌐 Your application should be available at:"
echo "   http://your-domain.com:$PORT"
echo
echo "⚠️  This server is for debugging only!"
echo "   For production, fix PHP-FPM configuration"
echo
echo "🛑 Press Ctrl+C to stop the server"
echo

# Start the server with error reporting
exec php -S $HOST:$PORT -t public 2>&1 | while IFS= read -r line; do
    echo "[$(date '+%H:%M:%S')] $line"
done
