#!/bin/bash

# Browsershot Deployment Verification Script
# This script verifies that Browsershot is working correctly after deployment

set -e

echo "🔍 Browsershot Deployment Verification"
echo "======================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a file exists and is executable
is_executable() {
    [ -f "$1" ] && [ -x "$1" ]
}

# Change to the Laravel application directory
cd "$(dirname "$0")/.."

print_status "INFO" "Starting Browsershot verification..."
echo ""

# Check 1: Verify required binaries exist
print_status "INFO" "Checking required binaries..."

if command_exists php; then
    PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2)
    print_status "SUCCESS" "PHP found: $PHP_VERSION"
else
    print_status "ERROR" "PHP not found"
    exit 1
fi

if command_exists node; then
    NODE_VERSION=$(node --version)
    print_status "SUCCESS" "Node.js found: $NODE_VERSION"
else
    print_status "ERROR" "Node.js not found"
    exit 1
fi

if command_exists npm; then
    NPM_VERSION=$(npm --version)
    print_status "SUCCESS" "NPM found: v$NPM_VERSION"
else
    print_status "ERROR" "NPM not found"
    exit 1
fi

# Check for Chrome/Chromium
CHROME_FOUND=false
if command_exists chromium; then
    CHROME_PATH=$(which chromium)
    print_status "SUCCESS" "Chromium found: $CHROME_PATH"
    CHROME_FOUND=true
elif command_exists chromium-browser; then
    CHROME_PATH=$(which chromium-browser)
    print_status "SUCCESS" "Chromium browser found: $CHROME_PATH"
    CHROME_FOUND=true
elif command_exists google-chrome-stable; then
    CHROME_PATH=$(which google-chrome-stable)
    print_status "SUCCESS" "Google Chrome found: $CHROME_PATH"
    CHROME_FOUND=true
elif command_exists google-chrome; then
    CHROME_PATH=$(which google-chrome)
    print_status "SUCCESS" "Google Chrome found: $CHROME_PATH"
    CHROME_FOUND=true
else
    print_status "ERROR" "No Chrome/Chromium binary found"
    exit 1
fi

echo ""

# Check 2: Verify environment variables
print_status "INFO" "Checking environment variables..."

if [ -n "$CHROME_PATH" ]; then
    print_status "SUCCESS" "CHROME_PATH environment variable is set: $CHROME_PATH"
else
    print_status "WARNING" "CHROME_PATH environment variable not set"
fi

if [ -n "$NODE_BINARY_PATH" ]; then
    print_status "SUCCESS" "NODE_BINARY_PATH environment variable is set: $NODE_BINARY_PATH"
else
    print_status "WARNING" "NODE_BINARY_PATH environment variable not set"
fi

if [ -n "$NPM_BINARY_PATH" ]; then
    print_status "SUCCESS" "NPM_BINARY_PATH environment variable is set: $NPM_BINARY_PATH"
else
    print_status "WARNING" "NPM_BINARY_PATH environment variable not set"
fi

echo ""

# Check 3: Verify Laravel application
print_status "INFO" "Checking Laravel application..."

if [ -f "artisan" ]; then
    print_status "SUCCESS" "Laravel artisan command found"
else
    print_status "ERROR" "Laravel artisan command not found"
    exit 1
fi

# Check if we can run artisan commands
if php artisan --version >/dev/null 2>&1; then
    LARAVEL_VERSION=$(php artisan --version | grep -o 'Laravel Framework [0-9.]*')
    print_status "SUCCESS" "$LARAVEL_VERSION"
else
    print_status "ERROR" "Cannot run Laravel artisan commands"
    exit 1
fi

echo ""

# Check 4: Test Browsershot functionality
print_status "INFO" "Testing Browsershot functionality..."

# Run the find chrome path command
print_status "INFO" "Running chrome path detection..."
if php artisan find:chrome-path >/dev/null 2>&1; then
    print_status "SUCCESS" "Chrome path detection command executed successfully"
else
    print_status "ERROR" "Chrome path detection command failed"
    exit 1
fi

# Run the browsershot test
print_status "INFO" "Running Browsershot PDF generation test..."
TEST_OUTPUT=$(php artisan test:browsershot 2>&1)
TEST_EXIT_CODE=$?

if [ $TEST_EXIT_CODE -eq 0 ] && echo "$TEST_OUTPUT" | grep -q "Browsershot test PASSED"; then
    print_status "SUCCESS" "Browsershot PDF generation test passed"
else
    print_status "ERROR" "Browsershot PDF generation test failed"
    echo "Test output:"
    echo "$TEST_OUTPUT"
    exit 1
fi

echo ""

# Check 5: Verify file permissions and directories
print_status "INFO" "Checking file permissions and directories..."

# Check storage directory
if [ -d "storage" ] && [ -w "storage" ]; then
    print_status "SUCCESS" "Storage directory is writable"
else
    print_status "ERROR" "Storage directory is not writable"
    exit 1
fi

# Check if browsershot temp directory can be created
TEMP_DIR="storage/app/browsershot-temp"
if mkdir -p "$TEMP_DIR" 2>/dev/null; then
    print_status "SUCCESS" "Browsershot temp directory can be created"
    rmdir "$TEMP_DIR" 2>/dev/null || true
else
    print_status "ERROR" "Cannot create Browsershot temp directory"
    exit 1
fi

echo ""

# Check 6: Verify Composer dependencies
print_status "INFO" "Checking critical dependencies..."

# Check if Browsershot package is installed
if php -r "require 'vendor/autoload.php'; echo class_exists('Spatie\Browsershot\Browsershot') ? 'yes' : 'no';" | grep -q "yes"; then
    print_status "SUCCESS" "Browsershot package is installed and loadable"
else
    print_status "ERROR" "Browsershot package is not properly installed"
    exit 1
fi

echo ""

# Summary
print_status "SUCCESS" "All Browsershot deployment checks passed!"
echo ""
print_status "INFO" "Summary:"
echo "  • PHP: Available and working"
echo "  • Node.js: Available and working"
echo "  • NPM: Available and working"
echo "  • Chrome/Chromium: Available at $CHROME_PATH"
echo "  • Laravel: Working correctly"
echo "  • Browsershot: PDF generation working"
echo "  • File permissions: Correct"
echo "  • Dependencies: All loaded"
echo ""
print_status "SUCCESS" "Your Browsershot deployment is ready for production! 🚀"

exit 0
