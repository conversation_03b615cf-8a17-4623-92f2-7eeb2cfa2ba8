server {
    listen 80;
    listen [::]:80;
    server_name **************; # Your IP address

    # Using a user that has access to the project directory
    root /home/<USER>/Projects/DccpAdminV2/public;
    index index.php index.html index.htm;

    # Add this to disable the default server
    default_server;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi.conf;
        fastcgi_pass unix:/var/run/php-fpm/php-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
