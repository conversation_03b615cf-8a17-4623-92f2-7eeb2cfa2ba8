.PHONY: help build build-prod start stop restart logs migrate optimize health status cleanup deploy

# Variables
IMAGE_NAME := dccp-admin
CONTAINER_NAME := dccp-admin-app
COMPOSE_FILE := docker-compose.prod.yml
COMPOSE_FILE_EXTERNAL := docker-compose.external.yml

# Default target
help: ## Show this help message
	@echo "DCCP Admin V2 - Docker Management"
	@echo "================================="
	@echo "Usage: make [target]"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

build: ## Build development Docker image
	docker build -t $(IMAGE_NAME):latest .

build-prod: ## Build production Docker image
	docker build --target production -t $(IMAGE_NAME):production .

start: ## Start all services
	docker-compose -f $(COMPOSE_FILE) up -d

start-external: ## Start with external services only
	docker-compose -f $(COMPOSE_FILE_EXTERNAL) up -d

stop: ## Stop all services
	docker-compose -f $(COMPOSE_FILE) down

restart: ## Restart all services
	$(MAKE) stop
	$(MAKE) start

logs: ## View application logs
	docker-compose -f $(COMPOSE_FILE) logs -f app

logs-all: ## View logs from all services
	docker-compose -f $(COMPOSE_FILE) logs -f

shell: ## Access application shell
	docker exec -it $(CONTAINER_NAME) sh

migrate: ## Run database migrations
	docker exec $(CONTAINER_NAME) php artisan migrate --force

migrate-fresh: ## Fresh migrations with seeding
	docker exec $(CONTAINER_NAME) php artisan migrate:fresh --seed --force

optimize: ## Optimize Laravel application
	docker exec $(CONTAINER_NAME) php artisan config:cache
	docker exec $(CONTAINER_NAME) php artisan route:cache
	docker exec $(CONTAINER_NAME) php artisan view:cache
	docker exec $(CONTAINER_NAME) php artisan event:cache
	docker exec $(CONTAINER_NAME) php artisan icon:cache

clear-cache: ## Clear all Laravel caches
	docker exec $(CONTAINER_NAME) php artisan optimize:clear

health: ## Check application health
	curl -f http://localhost:8000/health || echo "Health check failed"

status: ## Show service status
	docker-compose -f $(COMPOSE_FILE) ps

cleanup: ## Clean up Docker resources
	docker container prune -f
	docker image prune -f
	docker volume prune -f

deploy: build-prod stop start migrate optimize health ## Full deployment process

deploy-external: build-prod ## Deploy with external services
	docker-compose -f $(COMPOSE_FILE_EXTERNAL) down || true
	docker-compose -f $(COMPOSE_FILE_EXTERNAL) up -d
	sleep 30
	$(MAKE) migrate
	$(MAKE) optimize
	$(MAKE) health

backup-db: ## Backup database (if using included PostgreSQL)
	docker exec dccp-admin-postgres pg_dump -U dccp_user dccpadminv2 > backup-$(shell date +%Y%m%d-%H%M%S).sql

backup-storage: ## Backup application storage
	docker run --rm -v dccp-admin_app_storage:/data -v $(PWD):/backup alpine tar czf /backup/storage-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz -C /data .

restore-db: ## Restore database from backup (usage: make restore-db FILE=backup.sql)
	@if [ -z "$(FILE)" ]; then echo "Usage: make restore-db FILE=backup.sql"; exit 1; fi
	docker exec -i dccp-admin-postgres psql -U dccp_user -d dccpadminv2 < $(FILE)

env-setup: ## Setup environment file
	@if [ ! -f .env ]; then \
		cp .env.production .env; \
		echo "Environment file created from .env.production"; \
		echo "Please update .env with your configuration"; \
	else \
		echo ".env file already exists"; \
	fi

key-generate: ## Generate Laravel application key
	docker run --rm -v $(PWD):/app -w /app $(IMAGE_NAME):production php artisan key:generate --show

test: ## Run tests in container
	docker exec $(CONTAINER_NAME) php artisan test

tinker: ## Open Laravel tinker shell
	docker exec -it $(CONTAINER_NAME) php artisan tinker

queue-work: ## Start queue worker
	docker exec -d $(CONTAINER_NAME) php artisan queue:work --daemon

horizon: ## Start Laravel Horizon (if installed)
	docker exec -d $(CONTAINER_NAME) php artisan horizon

schedule-work: ## Run scheduled tasks (for testing)
	docker exec $(CONTAINER_NAME) php artisan schedule:run

update: ## Update application (pull, build, restart)
	git pull origin main
	$(MAKE) build-prod
	$(MAKE) restart
	$(MAKE) migrate
	$(MAKE) optimize

production-check: ## Check production readiness
	@echo "Checking production readiness..."
	@echo "================================"
	@echo -n "Environment file exists: "
	@if [ -f .env ]; then echo "✓"; else echo "✗ - Run 'make env-setup'"; fi
	@echo -n "APP_KEY is set: "
	@if grep -q "^APP_KEY=base64:" .env 2>/dev/null; then echo "✓"; else echo "✗ - Run 'make key-generate'"; fi
	@echo -n "Database configured: "
	@if grep -q "^DB_HOST=" .env 2>/dev/null; then echo "✓"; else echo "✗ - Configure database settings"; fi
	@echo -n "Redis configured: "
	@if grep -q "^REDIS_HOST=" .env 2>/dev/null; then echo "✓"; else echo "✗ - Configure Redis settings"; fi
	@echo ""