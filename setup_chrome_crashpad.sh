#!/bin/bash

# This script sets up the Chrome Crashpad directory for the http user
# Run this script with sudo

# Define directories
CRASHPAD_DIR="/home/<USER>/.config/chromium/Crashpad"
CONFIG_DIR="/home/<USER>/.config"
CHROME_DATA_DIR="$(pwd)/storage/app/chrome-data"

echo "Setting up Chrome Crashpad directory for the http user..."

# Create Crashpad directory if it doesn't exist
if [ ! -d "$CRASHPAD_DIR" ]; then
    echo "Creating Crashpad directory: $CRASHPAD_DIR"
    mkdir -p "$CRASHPAD_DIR"
else
    echo "Crashpad directory already exists: $CRASHPAD_DIR"
fi

# Create Chrome data directory in Laravel storage if it doesn't exist
if [ ! -d "$CHROME_DATA_DIR" ]; then
    echo "Creating Chrome data directory: $CHROME_DATA_DIR"
    mkdir -p "$CHROME_DATA_DIR"
else
    echo "Chrome data directory already exists: $CHROME_DATA_DIR"
fi

# Set ownership to http user
echo "Setting ownership of $CONFIG_DIR to http:http"
chown -R http:http "$CONFIG_DIR"

echo "Setting ownership of $CHROME_DATA_DIR to http:http"
chown -R http:http "$CHROME_DATA_DIR"

# Set permissions
echo "Setting permissions on $CONFIG_DIR"
chmod -R 755 "$CONFIG_DIR"

echo "Setting permissions on $CHROME_DATA_DIR"
chmod -R 755 "$CHROME_DATA_DIR"

echo "Done! Chrome Crashpad directory is now set up for the http user."
