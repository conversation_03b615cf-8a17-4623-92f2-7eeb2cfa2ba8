<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | Force Enrollment When Classes Appear Full
    |--------------------------------------------------------------------------
    |
    | When set to true, this will override the maximum_slots check during
    | auto-enrollment. Use this when you need to force enrollment despite
    | classes appearing to be at maximum capacity.
    |
    */
    'force_enroll_when_full' => env('FORCE_ENROLL_WHEN_FULL', false),

    /*
    |--------------------------------------------------------------------------
    | Maximum Slots Default
    |--------------------------------------------------------------------------
    |
    | Default maximum number of students per class if not specified
    |
    */
    'default_max_slots' => env('DEFAULT_MAX_SLOTS', 50),
];
