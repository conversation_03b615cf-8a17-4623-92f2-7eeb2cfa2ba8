var x=class{constructor(t,e,i,n){if(isNaN(t)||isNaN(e))throw new Error(`Point is invalid: (${t}, ${e})`);this.x=+t,this.y=+e,this.pressure=i||0,this.time=n||Date.now()}distanceTo(t){return Math.sqrt(Math.pow(this.x-t.x,2)+Math.pow(this.y-t.y,2))}equals(t){return this.x===t.x&&this.y===t.y&&this.pressure===t.pressure&&this.time===t.time}velocityFrom(t){return this.time!==t.time?this.distanceTo(t)/(this.time-t.time):0}},y=class _{static fromPoints(t,e){let i=this.calculateControlPoints(t[0],t[1],t[2]).c2,n=this.calculateControlPoints(t[1],t[2],t[3]).c1;return new _(t[1],i,n,t[2],e.start,e.end)}static calculateControlPoints(t,e,i){let n=t.x-e.x,o=t.y-e.y,s=e.x-i.x,d=e.y-i.y,h={x:(t.x+e.x)/2,y:(t.y+e.y)/2},a={x:(e.x+i.x)/2,y:(e.y+i.y)/2},l=Math.sqrt(n*n+o*o),c=Math.sqrt(s*s+d*d),p=h.x-a.x,g=h.y-a.y,f=l+c==0?0:c/(l+c),v={x:a.x+p*f,y:a.y+g*f},r=e.x-v.x,u=e.y-v.y;return{c1:new x(h.x+r,h.y+u),c2:new x(a.x+r,a.y+u)}}constructor(t,e,i,n,o,s){this.startPoint=t,this.control2=e,this.control1=i,this.endPoint=n,this.startWidth=o,this.endWidth=s}length(){let e=0,i,n;for(let o=0;o<=10;o+=1){let s=o/10,d=this.point(s,this.startPoint.x,this.control1.x,this.control2.x,this.endPoint.x),h=this.point(s,this.startPoint.y,this.control1.y,this.control2.y,this.endPoint.y);if(o>0){let a=d-i,l=h-n;e+=Math.sqrt(a*a+l*l)}i=d,n=h}return e}point(t,e,i,n,o){return e*(1-t)*(1-t)*(1-t)+3*i*(1-t)*(1-t)*t+3*n*(1-t)*t*t+o*t*t*t}},E=class{constructor(){try{this._et=new EventTarget}catch{this._et=document}}addEventListener(t,e,i){this._et.addEventListener(t,e,i)}dispatchEvent(t){return this._et.dispatchEvent(t)}removeEventListener(t,e,i){this._et.removeEventListener(t,e,i)}};function C(_,t=250){let e=0,i=null,n,o,s,d=()=>{e=Date.now(),i=null,n=_.apply(o,s),i||(o=null,s=[])};return function(...a){let l=Date.now(),c=t-(l-e);return o=this,s=a,c<=0||c>t?(i&&(clearTimeout(i),i=null),e=l,n=_.apply(o,s),i||(o=null,s=[])):i||(i=window.setTimeout(d,c)),n}}var P=class _ extends E{constructor(t,e={}){var i,n,o;super(),this.canvas=t,this._drawingStroke=!1,this._isEmpty=!0,this._lastPoints=[],this._data=[],this._lastVelocity=0,this._lastWidth=0,this._handleMouseDown=s=>{!this._isLeftButtonPressed(s,!0)||this._drawingStroke||this._strokeBegin(this._pointerEventToSignatureEvent(s))},this._handleMouseMove=s=>{if(!this._isLeftButtonPressed(s,!0)||!this._drawingStroke){this._strokeEnd(this._pointerEventToSignatureEvent(s),!1);return}this._strokeMoveUpdate(this._pointerEventToSignatureEvent(s))},this._handleMouseUp=s=>{this._isLeftButtonPressed(s)||this._strokeEnd(this._pointerEventToSignatureEvent(s))},this._handleTouchStart=s=>{s.targetTouches.length!==1||this._drawingStroke||(s.cancelable&&s.preventDefault(),this._strokeBegin(this._touchEventToSignatureEvent(s)))},this._handleTouchMove=s=>{if(s.targetTouches.length===1){if(s.cancelable&&s.preventDefault(),!this._drawingStroke){this._strokeEnd(this._touchEventToSignatureEvent(s),!1);return}this._strokeMoveUpdate(this._touchEventToSignatureEvent(s))}},this._handleTouchEnd=s=>{s.targetTouches.length===0&&(s.cancelable&&s.preventDefault(),this.canvas.removeEventListener("touchmove",this._handleTouchMove),this._strokeEnd(this._touchEventToSignatureEvent(s)))},this._handlePointerDown=s=>{!s.isPrimary||!this._isLeftButtonPressed(s)||this._drawingStroke||(s.preventDefault(),this._strokeBegin(this._pointerEventToSignatureEvent(s)))},this._handlePointerMove=s=>{if(s.isPrimary){if(!this._isLeftButtonPressed(s,!0)||!this._drawingStroke){this._strokeEnd(this._pointerEventToSignatureEvent(s),!1);return}s.preventDefault(),this._strokeMoveUpdate(this._pointerEventToSignatureEvent(s))}},this._handlePointerUp=s=>{!s.isPrimary||this._isLeftButtonPressed(s)||(s.preventDefault(),this._strokeEnd(this._pointerEventToSignatureEvent(s)))},this.velocityFilterWeight=e.velocityFilterWeight||.7,this.minWidth=e.minWidth||.5,this.maxWidth=e.maxWidth||2.5,this.throttle=(i=e.throttle)!==null&&i!==void 0?i:16,this.minDistance=(n=e.minDistance)!==null&&n!==void 0?n:5,this.dotSize=e.dotSize||0,this.penColor=e.penColor||"black",this.backgroundColor=e.backgroundColor||"rgba(0,0,0,0)",this.compositeOperation=e.compositeOperation||"source-over",this.canvasContextOptions=(o=e.canvasContextOptions)!==null&&o!==void 0?o:{},this._strokeMoveUpdate=this.throttle?C(_.prototype._strokeUpdate,this.throttle):_.prototype._strokeUpdate,this._ctx=t.getContext("2d",this.canvasContextOptions),this.clear(),this.on()}clear(){let{_ctx:t,canvas:e}=this;t.fillStyle=this.backgroundColor,t.clearRect(0,0,e.width,e.height),t.fillRect(0,0,e.width,e.height),this._data=[],this._reset(this._getPointGroupOptions()),this._isEmpty=!0}fromDataURL(t,e={}){return new Promise((i,n)=>{let o=new Image,s=e.ratio||window.devicePixelRatio||1,d=e.width||this.canvas.width/s,h=e.height||this.canvas.height/s,a=e.xOffset||0,l=e.yOffset||0;this._reset(this._getPointGroupOptions()),o.onload=()=>{this._ctx.drawImage(o,a,l,d,h),i()},o.onerror=c=>{n(c)},o.crossOrigin="anonymous",o.src=t,this._isEmpty=!1})}toDataURL(t="image/png",e){switch(t){case"image/svg+xml":return typeof e!="object"&&(e=void 0),`data:image/svg+xml;base64,${btoa(this.toSVG(e))}`;default:return typeof e!="number"&&(e=void 0),this.canvas.toDataURL(t,e)}}on(){this.canvas.style.touchAction="none",this.canvas.style.msTouchAction="none",this.canvas.style.userSelect="none";let t=/Macintosh/.test(navigator.userAgent)&&"ontouchstart"in document;window.PointerEvent&&!t?this._handlePointerEvents():(this._handleMouseEvents(),"ontouchstart"in window&&this._handleTouchEvents())}off(){this.canvas.style.touchAction="auto",this.canvas.style.msTouchAction="auto",this.canvas.style.userSelect="auto",this.canvas.removeEventListener("pointerdown",this._handlePointerDown),this.canvas.removeEventListener("mousedown",this._handleMouseDown),this.canvas.removeEventListener("touchstart",this._handleTouchStart),this._removeMoveUpEventListeners()}_getListenerFunctions(){var t;let e=window.document===this.canvas.ownerDocument?window:(t=this.canvas.ownerDocument.defaultView)!==null&&t!==void 0?t:this.canvas.ownerDocument;return{addEventListener:e.addEventListener.bind(e),removeEventListener:e.removeEventListener.bind(e)}}_removeMoveUpEventListeners(){let{removeEventListener:t}=this._getListenerFunctions();t("pointermove",this._handlePointerMove),t("pointerup",this._handlePointerUp),t("mousemove",this._handleMouseMove),t("mouseup",this._handleMouseUp),t("touchmove",this._handleTouchMove),t("touchend",this._handleTouchEnd)}isEmpty(){return this._isEmpty}fromData(t,{clear:e=!0}={}){e&&this.clear(),this._fromData(t,this._drawCurve.bind(this),this._drawDot.bind(this)),this._data=this._data.concat(t)}toData(){return this._data}_isLeftButtonPressed(t,e){return e?t.buttons===1:(t.buttons&1)===1}_pointerEventToSignatureEvent(t){return{event:t,type:t.type,x:t.clientX,y:t.clientY,pressure:"pressure"in t?t.pressure:0}}_touchEventToSignatureEvent(t){let e=t.changedTouches[0];return{event:t,type:t.type,x:e.clientX,y:e.clientY,pressure:e.force}}_getPointGroupOptions(t){return{penColor:t&&"penColor"in t?t.penColor:this.penColor,dotSize:t&&"dotSize"in t?t.dotSize:this.dotSize,minWidth:t&&"minWidth"in t?t.minWidth:this.minWidth,maxWidth:t&&"maxWidth"in t?t.maxWidth:this.maxWidth,velocityFilterWeight:t&&"velocityFilterWeight"in t?t.velocityFilterWeight:this.velocityFilterWeight,compositeOperation:t&&"compositeOperation"in t?t.compositeOperation:this.compositeOperation}}_strokeBegin(t){if(!this.dispatchEvent(new CustomEvent("beginStroke",{detail:t,cancelable:!0})))return;let{addEventListener:i}=this._getListenerFunctions();switch(t.event.type){case"mousedown":i("mousemove",this._handleMouseMove),i("mouseup",this._handleMouseUp);break;case"touchstart":i("touchmove",this._handleTouchMove),i("touchend",this._handleTouchEnd);break;case"pointerdown":i("pointermove",this._handlePointerMove),i("pointerup",this._handlePointerUp);break}this._drawingStroke=!0;let n=this._getPointGroupOptions(),o=Object.assign(Object.assign({},n),{points:[]});this._data.push(o),this._reset(n),this._strokeUpdate(t)}_strokeUpdate(t){if(!this._drawingStroke)return;if(this._data.length===0){this._strokeBegin(t);return}this.dispatchEvent(new CustomEvent("beforeUpdateStroke",{detail:t}));let e=this._createPoint(t.x,t.y,t.pressure),i=this._data[this._data.length-1],n=i.points,o=n.length>0&&n[n.length-1],s=o?e.distanceTo(o)<=this.minDistance:!1,d=this._getPointGroupOptions(i);if(!o||!(o&&s)){let h=this._addPoint(e,d);o?h&&this._drawCurve(h,d):this._drawDot(e,d),n.push({time:e.time,x:e.x,y:e.y,pressure:e.pressure})}this.dispatchEvent(new CustomEvent("afterUpdateStroke",{detail:t}))}_strokeEnd(t,e=!0){this._removeMoveUpEventListeners(),this._drawingStroke&&(e&&this._strokeUpdate(t),this._drawingStroke=!1,this.dispatchEvent(new CustomEvent("endStroke",{detail:t})))}_handlePointerEvents(){this._drawingStroke=!1,this.canvas.addEventListener("pointerdown",this._handlePointerDown)}_handleMouseEvents(){this._drawingStroke=!1,this.canvas.addEventListener("mousedown",this._handleMouseDown)}_handleTouchEvents(){this.canvas.addEventListener("touchstart",this._handleTouchStart)}_reset(t){this._lastPoints=[],this._lastVelocity=0,this._lastWidth=(t.minWidth+t.maxWidth)/2,this._ctx.fillStyle=t.penColor,this._ctx.globalCompositeOperation=t.compositeOperation}_createPoint(t,e,i){let n=this.canvas.getBoundingClientRect();return new x(t-n.left,e-n.top,i,new Date().getTime())}_addPoint(t,e){let{_lastPoints:i}=this;if(i.push(t),i.length>2){i.length===3&&i.unshift(i[0]);let n=this._calculateCurveWidths(i[1],i[2],e),o=y.fromPoints(i,n);return i.shift(),o}return null}_calculateCurveWidths(t,e,i){let n=i.velocityFilterWeight*e.velocityFrom(t)+(1-i.velocityFilterWeight)*this._lastVelocity,o=this._strokeWidth(n,i),s={end:o,start:this._lastWidth};return this._lastVelocity=n,this._lastWidth=o,s}_strokeWidth(t,e){return Math.max(e.maxWidth/(t+1),e.minWidth)}_drawCurveSegment(t,e,i){let n=this._ctx;n.moveTo(t,e),n.arc(t,e,i,0,2*Math.PI,!1),this._isEmpty=!1}_drawCurve(t,e){let i=this._ctx,n=t.endWidth-t.startWidth,o=Math.ceil(t.length())*2;i.beginPath(),i.fillStyle=e.penColor;for(let s=0;s<o;s+=1){let d=s/o,h=d*d,a=h*d,l=1-d,c=l*l,p=c*l,g=p*t.startPoint.x;g+=3*c*d*t.control1.x,g+=3*l*h*t.control2.x,g+=a*t.endPoint.x;let f=p*t.startPoint.y;f+=3*c*d*t.control1.y,f+=3*l*h*t.control2.y,f+=a*t.endPoint.y;let v=Math.min(t.startWidth+a*n,e.maxWidth);this._drawCurveSegment(g,f,v)}i.closePath(),i.fill()}_drawDot(t,e){let i=this._ctx,n=e.dotSize>0?e.dotSize:(e.minWidth+e.maxWidth)/2;i.beginPath(),this._drawCurveSegment(t.x,t.y,n),i.closePath(),i.fillStyle=e.penColor,i.fill()}_fromData(t,e,i){for(let n of t){let{points:o}=n,s=this._getPointGroupOptions(n);if(o.length>1)for(let d=0;d<o.length;d+=1){let h=o[d],a=new x(h.x,h.y,h.pressure,h.time);d===0&&this._reset(s);let l=this._addPoint(a,s);l&&e(l,s)}else this._reset(s),i(o[0],s)}}toSVG({includeBackgroundColor:t=!1}={}){let e=this._data,i=Math.max(window.devicePixelRatio||1,1),n=0,o=0,s=this.canvas.width/i,d=this.canvas.height/i,h=document.createElementNS("http://www.w3.org/2000/svg","svg");if(h.setAttribute("xmlns","http://www.w3.org/2000/svg"),h.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),h.setAttribute("viewBox",`${n} ${o} ${s} ${d}`),h.setAttribute("width",s.toString()),h.setAttribute("height",d.toString()),t&&this.backgroundColor){let a=document.createElement("rect");a.setAttribute("width","100%"),a.setAttribute("height","100%"),a.setAttribute("fill",this.backgroundColor),h.appendChild(a)}return this._fromData(e,(a,{penColor:l})=>{let c=document.createElement("path");if(!isNaN(a.control1.x)&&!isNaN(a.control1.y)&&!isNaN(a.control2.x)&&!isNaN(a.control2.y)){let p=`M ${a.startPoint.x.toFixed(3)},${a.startPoint.y.toFixed(3)} C ${a.control1.x.toFixed(3)},${a.control1.y.toFixed(3)} ${a.control2.x.toFixed(3)},${a.control2.y.toFixed(3)} ${a.endPoint.x.toFixed(3)},${a.endPoint.y.toFixed(3)}`;c.setAttribute("d",p),c.setAttribute("stroke-width",(a.endWidth*2.25).toFixed(3)),c.setAttribute("stroke",l),c.setAttribute("fill","none"),c.setAttribute("stroke-linecap","round"),h.appendChild(c)}},(a,{penColor:l,dotSize:c,minWidth:p,maxWidth:g})=>{let f=document.createElement("circle"),v=c>0?c:(p+g)/2;f.setAttribute("r",v.toString()),f.setAttribute("cx",a.x.toString()),f.setAttribute("cy",a.y.toString()),f.setAttribute("fill",l),h.appendChild(f)}),h.outerHTML}};function b({backgroundColor:_,backgroundColorOnDark:t,confirmable:e,disabled:i,dotSize:n,exportBackgroundColor:o,exportPenColor:s,filename:d,maxWidth:h,minDistance:a,minWidth:l,penColor:c,penColorOnDark:p,state:g,throttle:f,velocityFilterWeight:v}){return{state:g,previousState:g,dirty:!1,confirmed:!1,signaturePad:null,initSignaturePad(){this.signaturePad=new P(this.$refs.canvas,{backgroundColor:_,dotSize:n,maxWidth:h,minDistance:a,minWidth:l,penColor:c,throttle:f,velocityFilterWeight:v}),i&&this.signaturePad.off(),this.watchState(),this.watchResize(),this.watchTheme(),g.initialValue&&(this.signaturePad.fromDataURL(g.initialValue),this.signaturePad.addEventListener("beginStroke",()=>{this.signaturePad.clear()},{once:!0}))},clear(){this.signaturePad.clear(),this.state=null,this.confirmed=!1,this.dirty=!1,this.signaturePad.on()},undo(){let r=this.signaturePad.toData();r.length&&(r.pop(),this.signaturePad.fromData(r)),r.length||(this.state=null),this.confirmed=!1,this.dirty=r.length>0,this.signaturePad.on()},done(){let{data:r,canvasBackgroundColor:u,canvasPenColor:m}=this.prepareToExport();this.signaturePad.fromData(r),this.previousState=this.state,this.state=this.signaturePad.toDataURL(),e&&(this.confirmed=!0,this.signaturePad.off());let{data:w}=this.restoreFromExport(r,u,m);this.signaturePad.fromData(w)},downloadAs(r,u){let{data:m,canvasBackgroundColor:w,canvasPenColor:k}=this.prepareToExport();this.signaturePad.fromData(m),this.download(this.signaturePad.toDataURL(r,{includeBackgroundColor:!0}),`${d}.${u}`);let{data:S}=this.restoreFromExport(m,w,k);this.signaturePad.fromData(S)},watchState(){this.signaturePad.addEventListener("endStroke",r=>{this.dirty=!0,!e&&this.done()},{once:!1}),this.$watch("confirmed",r=>{e&&!r&&(this.state=null)})},watchResize(){window.addEventListener("resize",()=>this.resizeCanvas),this.resizeCanvas()},resizeCanvas(){let r=Math.max(window.devicePixelRatio||1,1);this.$refs.canvas.width=this.$refs.canvas.offsetWidth*r,this.$refs.canvas.height=this.$refs.canvas.offsetHeight*r,this.$refs.canvas.getContext("2d").scale(r,r),this.signaturePad.clear()},watchTheme(){let r;this.$store.hasOwnProperty("theme")?(window.addEventListener("theme-changed",u=>this.onThemeChanged(u.detail)),r=this.$store.theme):(window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",u=>this.onThemeChanged(u.matches?"dark":"light")),r=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),this.onThemeChanged(r)},onThemeChanged(r){if(this.signaturePad.penColor=r==="dark"?p??c:c,this.signaturePad.backgroundColor=r==="dark"?t??_:_,!this.signaturePad.toData().length)return;let u=this.signaturePad.toData();u.map(m=>(m.penColor=r==="dark"?p??c:c,m.backgroundColor=r==="dark"?t??_:_,m)),this.signaturePad.clear(),this.signaturePad.fromData(u)},prepareToExport(){let r=this.signaturePad.toData(),u=this.signaturePad.backgroundColor,m=this.signaturePad.penColor;return this.signaturePad.backgroundColor=o??this.signaturePad.backgroundColor,r.map(w=>w.penColor=s??w.penColor),{data:r,canvasBackgroundColor:u,canvasPenColor:m}},restoreFromExport(r,u,m){return this.signaturePad.backgroundColor=u,r.map(w=>w.penColor=m),{data:r}},download(r,u){let m=document.createElement("a");m.download=u,m.href=r,document.body.appendChild(m),m.click(),document.body.removeChild(m)},eventListeners:{"@reload-signature-component.window"(r){this.initSignaturePad()}}}}export{b as default};
/*! Bundled license information:

signature_pad/dist/signature_pad.js:
  (*!
   * Signature Pad v5.0.7 | https://github.com/szimek/signature_pad
   * (c) 2025 Szymon Nowak | Released under the MIT license
   *)
*/
