var _=()=>{if(document.querySelector('meta[name="csrf-token"]'))return document.querySelector('meta[name="csrf-token"]').getAttribute("content");if(document.querySelector("[data-csrf]"))return document.querySelector("[data-csrf]").getAttribute("data-csrf");if(window.livewireScriptConfig.csrf??!1)return window.livewireScriptConfig.csrf;throw new Error("No CSRF token detected")},D=e=>Array.isArray(e),K=e=>typeof e=="object"&&e!==null,I=e=>K(e)&&!D(e),h=e=>typeof e=="function",m=(e,t)=>t in e;var P={hasErrors(){return Object.keys(this.$wire.__instance?.snapshot?.memo?.errors??{}).length>0},notifyPublicKeyError(){new FilamentNotification().danger().title("Error").body("We encountered a fatal error in the key generation process. Please try again later.").send()},isValidPublicKey(e,t="rpId"){return I(e)&&m(e,"challenge")&&m(e,t)},_ajaxOptions(e={}){return{method:"POST",headers:{"Content-Type":"application/json","X-Webauthn":""},body:JSON.stringify({_token:_(),...e})}}};function u(e){let t=new Uint8Array(e),r="";for(let o of t)r+=String.fromCharCode(o);return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function p(e){let t=e.replace(/-/g,"+").replace(/_/g,"/"),r=(4-t.length%4)%4,a=t.padEnd(t.length+r,"="),o=atob(a),s=new ArrayBuffer(o.length),d=new Uint8Array(s);for(let i=0;i<o.length;i++)d[i]=o.charCodeAt(i);return s}function f(){return v.stubThis(globalThis?.PublicKeyCredential!==void 0&&typeof globalThis.PublicKeyCredential=="function")}var v={stubThis:e=>e};function b(e){let{id:t}=e;return{...e,id:p(t),transports:e.transports}}function g(e){return e==="localhost"||/^([a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,}$/i.test(e)}var n=class extends Error{constructor({message:t,code:r,cause:a,name:o}){super(t,{cause:a}),Object.defineProperty(this,"code",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=o??a.name,this.code=r}};function C({error:e,options:t}){let{publicKey:r}=t;if(!r)throw Error("options was missing required publicKey property");if(e.name==="AbortError"){if(t.signal instanceof AbortSignal)return new n({message:"Registration ceremony was sent an abort signal",code:"ERROR_CEREMONY_ABORTED",cause:e})}else if(e.name==="ConstraintError"){if(r.authenticatorSelection?.requireResidentKey===!0)return new n({message:"Discoverable credentials were required but no available authenticator supported it",code:"ERROR_AUTHENTICATOR_MISSING_DISCOVERABLE_CREDENTIAL_SUPPORT",cause:e});if(t.mediation==="conditional"&&r.authenticatorSelection?.userVerification==="required")return new n({message:"User verification was required during automatic registration but it could not be performed",code:"ERROR_AUTO_REGISTER_USER_VERIFICATION_FAILURE",cause:e});if(r.authenticatorSelection?.userVerification==="required")return new n({message:"User verification was required but no available authenticator supported it",code:"ERROR_AUTHENTICATOR_MISSING_USER_VERIFICATION_SUPPORT",cause:e})}else{if(e.name==="InvalidStateError")return new n({message:"The authenticator was previously registered",code:"ERROR_AUTHENTICATOR_PREVIOUSLY_REGISTERED",cause:e});if(e.name==="NotAllowedError")return new n({message:e.message,code:"ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY",cause:e});if(e.name==="NotSupportedError")return r.pubKeyCredParams.filter(o=>o.type==="public-key").length===0?new n({message:'No entry in pubKeyCredParams was of type "public-key"',code:"ERROR_MALFORMED_PUBKEYCREDPARAMS",cause:e}):new n({message:"No available authenticator supported any of the specified pubKeyCredParams algorithms",code:"ERROR_AUTHENTICATOR_NO_SUPPORTED_PUBKEYCREDPARAMS_ALG",cause:e});if(e.name==="SecurityError"){let a=globalThis.location.hostname;if(g(a)){if(r.rp.id!==a)return new n({message:`The RP ID "${r.rp.id}" is invalid for this domain`,code:"ERROR_INVALID_RP_ID",cause:e})}else return new n({message:`${globalThis.location.hostname} is an invalid domain`,code:"ERROR_INVALID_DOMAIN",cause:e})}else if(e.name==="TypeError"){if(r.user.id.byteLength<1||r.user.id.byteLength>64)return new n({message:"User ID was not between 1 and 64 characters",code:"ERROR_INVALID_USER_ID_LENGTH",cause:e})}else if(e.name==="UnknownError")return new n({message:"The authenticator was unable to process the specified options, or could not create a new credential",code:"ERROR_AUTHENTICATOR_GENERAL_ERROR",cause:e})}return e}var w=class{constructor(){Object.defineProperty(this,"controller",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}createNewAbortSignal(){if(this.controller){let r=new Error("Cancelling existing WebAuthn API call for new one");r.name="AbortError",this.controller.abort(r)}let t=new AbortController;return this.controller=t,t.signal}cancelCeremony(){if(this.controller){let t=new Error("Manually cancelling existing WebAuthn API call");t.name="AbortError",this.controller.abort(t),this.controller=void 0}}},A=new w;var U=["cross-platform","platform"];function R(e){if(e&&!(U.indexOf(e)<0))return e}async function x(e){!e.optionsJSON&&e.challenge&&(console.warn("startRegistration() was not called correctly. It will try to continue with the provided options, but this call should be refactored to use the expected call structure instead. See https://simplewebauthn.dev/docs/packages/browser#typeerror-cannot-read-properties-of-undefined-reading-challenge for more information."),e={optionsJSON:e});let{optionsJSON:t,useAutoRegister:r=!1}=e;if(!f())throw new Error("WebAuthn is not supported in this browser");let a={...t,challenge:p(t.challenge),user:{...t.user,id:p(t.user.id)},excludeCredentials:t.excludeCredentials?.map(b)},o={};r&&(o.mediation="conditional"),o.publicKey=a,o.signal=A.createNewAbortSignal();let s;try{s=await navigator.credentials.create(o)}catch(l){throw C({error:l,options:o})}if(!s)throw new Error("Registration was not completed");let{id:d,rawId:i,response:c,type:N}=s,E;typeof c.getTransports=="function"&&(E=c.getTransports());let S;if(typeof c.getPublicKeyAlgorithm=="function")try{S=c.getPublicKeyAlgorithm()}catch(l){y("getPublicKeyAlgorithm()",l)}let O;if(typeof c.getPublicKey=="function")try{let l=c.getPublicKey();l!==null&&(O=u(l))}catch(l){y("getPublicKey()",l)}let T;if(typeof c.getAuthenticatorData=="function")try{T=u(c.getAuthenticatorData())}catch(l){y("getAuthenticatorData()",l)}return{id:d,rawId:u(i),response:{attestationObject:u(c.attestationObject),clientDataJSON:u(c.clientDataJSON),transports:E,publicKeyAlgorithm:S,publicKey:O,authenticatorData:T},type:N,clientExtensionResults:s.getClientExtensionResults(),authenticatorAttachment:R(s.authenticatorAttachment)}}function y(e,t){console.warn(`The browser extension that intercepted this WebAuthn API call incorrectly implemented ${e}. You should report this error to them.
`,t)}var L=({before:e=void 0,registerData:t={},registerUrl:r=void 0,publicKey:a=void 0,verifyKeyMethod:o="verifyKey"})=>({before:e,registerData:t,registerUrl:r,publicKey:a,verifyKeyMethod:o,error:null,processing:!1,browserSupportsWebAuthn:f,...P,async register(){if(this.error=null,!this.browserSupportsWebAuthn()||h(this.before)&&!await this.before.bind(this)())return;let s=this.publicKey;this.processing=!0;let d=h(this.registerData)?this.registerData():this.registerData;if(this.registerUrl){let i=await fetch(this.registerUrl,this._ajaxOptions(d));if(!i.ok)return this.processing=!1,this.notifyPublicKeyError();s=await i.json()}return this.isValidPublicKey(s,"rp")?x({optionsJSON:s}).then(i=>this.$wire.call(this.verifyKeyMethod,i)).catch(i=>this.error=i?.response?.data?.message??i).finally(()=>this.processing=!1):(this.processing=!1,this.notifyPublicKeyError())}}),Ve=L;export{Ve as default};
