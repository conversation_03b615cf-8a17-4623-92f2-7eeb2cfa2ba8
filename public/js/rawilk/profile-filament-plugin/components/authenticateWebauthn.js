var E=()=>{if(document.querySelector('meta[name="csrf-token"]'))return document.querySelector('meta[name="csrf-token"]').getAttribute("content");if(document.querySelector("[data-csrf]"))return document.querySelector("[data-csrf]").getAttribute("data-csrf");if(window.livewireScriptConfig.csrf??!1)return window.livewireScriptConfig.csrf;throw new Error("No CSRF token detected")},K=e=>Array.isArray(e),D=e=>typeof e=="object"&&e!==null,S=e=>D(e)&&!K(e),O=e=>typeof e=="function",d=(e,t)=>t in e;var T={hasErrors(){return Object.keys(this.$wire.__instance?.snapshot?.memo?.errors??{}).length>0},notifyPublicKeyError(){new FilamentNotification().danger().title("Error").body("We encountered a fatal error in the key generation process. Please try again later.").send()},isValidPublicKey(e,t="rpId"){return S(e)&&d(e,"challenge")&&d(e,t)},_ajaxOptions(e={}){return{method:"POST",headers:{"Content-Type":"application/json","X-Webauthn":""},body:JSON.stringify({_token:E(),...e})}}};function u(e){let t=new Uint8Array(e),r="";for(let n of t)r+=String.fromCharCode(n);return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function p(e){let t=e.replace(/-/g,"+").replace(/_/g,"/"),r=(4-t.length%4)%4,i=t.padEnd(t.length+r,"="),n=atob(i),o=new ArrayBuffer(n.length),c=new Uint8Array(o);for(let a=0;a<n.length;a++)c[a]=n.charCodeAt(a);return o}function l(){return U.stubThis(globalThis?.PublicKeyCredential!==void 0&&typeof globalThis.PublicKeyCredential=="function")}var U={stubThis:e=>e};function h(e){let{id:t}=e;return{...e,id:p(t),transports:e.transports}}function m(e){return e==="localhost"||/^([a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,}$/i.test(e)}var s=class extends Error{constructor({message:t,code:r,cause:i,name:n}){super(t,{cause:i}),Object.defineProperty(this,"code",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=n??i.name,this.code=r}};var b=class{constructor(){Object.defineProperty(this,"controller",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}createNewAbortSignal(){if(this.controller){let r=new Error("Cancelling existing WebAuthn API call for new one");r.name="AbortError",this.controller.abort(r)}let t=new AbortController;return this.controller=t,t.signal}cancelCeremony(){if(this.controller){let t=new Error("Manually cancelling existing WebAuthn API call");t.name="AbortError",this.controller.abort(t),this.controller=void 0}}},g=new b;var v=["cross-platform","platform"];function w(e){if(e&&!(v.indexOf(e)<0))return e}function _(){if(!l())return A.stubThis(new Promise(t=>t(!1)));let e=globalThis.PublicKeyCredential;return e?.isConditionalMediationAvailable===void 0?A.stubThis(new Promise(t=>t(!1))):A.stubThis(e.isConditionalMediationAvailable())}var A={stubThis:e=>e};function I({error:e,options:t}){let{publicKey:r}=t;if(!r)throw Error("options was missing required publicKey property");if(e.name==="AbortError"){if(t.signal instanceof AbortSignal)return new s({message:"Authentication ceremony was sent an abort signal",code:"ERROR_CEREMONY_ABORTED",cause:e})}else{if(e.name==="NotAllowedError")return new s({message:e.message,code:"ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY",cause:e});if(e.name==="SecurityError"){let i=globalThis.location.hostname;if(m(i)){if(r.rpId!==i)return new s({message:`The RP ID "${r.rpId}" is invalid for this domain`,code:"ERROR_INVALID_RP_ID",cause:e})}else return new s({message:`${globalThis.location.hostname} is an invalid domain`,code:"ERROR_INVALID_DOMAIN",cause:e})}else if(e.name==="UnknownError")return new s({message:"The authenticator was unable to process the specified options, or could not create a new assertion signature",code:"ERROR_AUTHENTICATOR_GENERAL_ERROR",cause:e})}return e}async function P(e){!e.optionsJSON&&e.challenge&&(console.warn("startAuthentication() was not called correctly. It will try to continue with the provided options, but this call should be refactored to use the expected call structure instead. See https://simplewebauthn.dev/docs/packages/browser#typeerror-cannot-read-properties-of-undefined-reading-challenge for more information."),e={optionsJSON:e});let{optionsJSON:t,useBrowserAutofill:r=!1,verifyBrowserAutofillInput:i=!0}=e;if(!l())throw new Error("WebAuthn is not supported in this browser");let n;t.allowCredentials?.length!==0&&(n=t.allowCredentials?.map(h));let o={...t,challenge:p(t.challenge),allowCredentials:n},c={};if(r){if(!await _())throw Error("Browser does not support WebAuthn autofill");if(document.querySelectorAll("input[autocomplete$='webauthn']").length<1&&i)throw Error('No <input> with "webauthn" as the only or last value in its `autocomplete` attribute was detected');c.mediation="conditional",o.allowCredentials=[]}c.publicKey=o,c.signal=g.createNewAbortSignal();let a;try{a=await navigator.credentials.get(c)}catch(y){throw I({error:y,options:c})}if(!a)throw new Error("Authentication was not completed");let{id:C,rawId:x,response:f,type:N}=a,R;return f.userHandle&&(R=u(f.userHandle)),{id:C,rawId:u(x),response:{authenticatorData:u(f.authenticatorData),clientDataJSON:u(f.clientDataJSON),signature:u(f.signature),userHandle:R},type:N,clientExtensionResults:a.getClientExtensionResults(),authenticatorAttachment:w(a.authenticatorAttachment)}}var L=({publicKey:e=void 0,publicKeyUrl:t=void 0,loginMethod:r="authenticate",loginUsing:i=void 0})=>({publicKey:e,publicKeyUrl:t,loginMethod:r,loginUsing:i,error:null,processing:!1,browserSupportsWebAuthn:l,...T,async login(){let n=this.publicKey;if(this.processing=!0,this.error=null,this.publicKeyUrl){let o=await fetch(this.publicKeyUrl,this._ajaxOptions());if(!o.ok)return this.processing=!1,this.notifyPublicKeyError();n=await o.json()}return this.isValidPublicKey(n)?P({optionsJSON:n}).then(async o=>{if(O(this.loginUsing)){await this.loginUsing.bind(this)(o);return}await this.$wire.call(this.loginMethod,o)}).catch(o=>{this.error=o?.response?.data?.message??o,this.$wire.call("$refresh")}).finally(()=>this.processing=!1):(this.processing=!1,this.notifyPublicKeyError())}}),We=L;export{We as default};
