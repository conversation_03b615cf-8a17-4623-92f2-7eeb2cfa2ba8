.col-span-1{grid-column:span 1 / span 1}.col-span-3{grid-column:span 3 / span 3}.flex-auto{flex:1 1 auto}.flex-none{flex:none}.gap-x-8{-moz-column-gap:2rem;column-gap:2rem}.divide-y > :not([hidden]) ~ :not([hidden]){--tw-divide-y-reverse: 0;border-top-width:calc(1px * calc(1 - var(--tw-divide-y-reverse)));border-bottom-width:calc(1px * var(--tw-divide-y-reverse))}.divide-gray-200 > :not([hidden]) ~ :not([hidden]){--tw-divide-opacity: 1;border-color:rgba(var(--gray-200), var(--tw-divide-opacity))}.diff-wrapper.diff{width:100%;border-collapse:separate;--tw-border-spacing-y: 0px;--tw-border-spacing-x: 0.25rem;border-spacing:var(--tw-border-spacing-x) var(--tw-border-spacing-y);word-break:break-all}.diff-wrapper.diff td{padding-left:0.75rem;padding-right:0.75rem;padding-top:0.5rem;padding-bottom:0.5rem;vertical-align:top;line-height:1.625}.diff-wrapper.diff.diff-html .change.change-eq .old,
.diff-wrapper.diff.diff-html .change.change-eq .new{width:50%;background-color:transparent}.diff-wrapper.diff.diff-html .change-rep .old,
.diff-wrapper.diff.diff-html .change-del .old{width:50%;--tw-bg-opacity: 1;background-color:rgb(252 240 241 / var(--tw-bg-opacity))}:is(.dark .diff-wrapper.diff.diff-html .change-rep .old),:is(.dark 
.diff-wrapper.diff.diff-html .change-del .old){background-color:#ef535033}.diff-wrapper.diff.diff-html .change-rep .new,
.diff-wrapper.diff.diff-html .change-ins .new{width:50%;--tw-bg-opacity: 1;background-color:rgb(237 250 239 / var(--tw-bg-opacity))}:is(.dark .diff-wrapper.diff.diff-html .change-rep .new),:is(.dark 
.diff-wrapper.diff.diff-html .change-ins .new){--tw-bg-opacity: 1;background-color:rgb(29 58 38 / var(--tw-bg-opacity))}.diff-wrapper.diff.diff-html .change ins,
.diff-wrapper.diff.diff-html .change del{text-decoration:none}.diff-wrapper.diff.diff-html .change ins{--tw-bg-opacity: 1;background-color:rgb(104 222 124 / var(--tw-bg-opacity))}:is(.dark .diff-wrapper.diff.diff-html .change ins){--tw-bg-opacity: 1;background-color:rgb(48 130 76 / var(--tw-bg-opacity))}.diff-wrapper.diff.diff-html .change del{--tw-bg-opacity: 1;background-color:rgb(255 171 175 / var(--tw-bg-opacity))}:is(.dark .diff-wrapper.diff.diff-html .change del){background-color:#EF535090}.diff-wrapper.diff.diff-html .change .old.none,
.diff-wrapper.diff.diff-html .change .new.none,
.diff-wrapper.diff.diff-html .change .rep.none{background:repeating-linear-gradient(-40deg, transparent, transparent 0.5em, #f3f4f6 0em, #f3f4f6 1em);cursor:not-allowed}:is(.dark .diff-wrapper.diff.diff-html .change .old.none),:is(.dark 
.diff-wrapper.diff.diff-html .change .new.none),:is(.dark 
.diff-wrapper.diff.diff-html .change .rep.none){background:repeating-linear-gradient(-40deg, transparent, transparent .5em, #374151 0, #374151 1em)}.group:hover .group-hover\:text-primary-600{--tw-text-opacity: 1;color:rgba(var(--primary-600), var(--tw-text-opacity))}:is(.dark .dark\:divide-white\/10) > :not([hidden]) ~ :not([hidden]){border-color:rgb(255 255 255 / 0.1)}:is(.dark .dark\:text-gray-400){--tw-text-opacity: 1;color:rgba(var(--gray-400), var(--tw-text-opacity))}@media (min-width: 1024px){.lg\:grid-cols-4{grid-template-columns:repeat(4, minmax(0, 1fr))}}