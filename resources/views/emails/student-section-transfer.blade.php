<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Section Transfer Notification</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.5;
            color: #374151;
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9fafb;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }
        .content {
            padding: 24px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 16px;
            color: #111827;
            font-weight: 500;
        }
        .main-message {
            background-color: #f3f4f6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            text-align: center;
        }
        .subject-code {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }
        .section-change {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 12px;
        }
        .section-badge {
            display: inline-block;
            background-color: #dbeafe;
            color: #1e40af;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            margin: 0 4px;
        }
        .arrow {
            color: #9ca3af;
            margin: 0 8px;
            font-weight: bold;
        }
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .action-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
            text-decoration: none;
            color: white;
        }
        .button-container {
            text-align: center;
            margin: 24px 0;
        }
        .help-section {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 16px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .help-section h4 {
            margin: 0 0 8px 0;
            color: #92400e;
            font-size: 14px;
            font-weight: 600;
        }
        .help-section p {
            margin: 0;
            color: #92400e;
            font-size: 14px;
        }
        .footer {
            background-color: #f9fafb;
            padding: 16px;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
            border-top: 1px solid #e5e7eb;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .content {
                padding: 20px;
            }
            .action-button {
                display: block;
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>📚 Section Transfer</h1>
        </div>

        <div class="content">
            <div class="greeting">
                Hi {{ $student_name }},
            </div>

            <p>You have been moved to a different section for the following subject:</p>

            <div class="main-message">
                <div class="subject-code">{{ $subject_code }}</div>
                <div class="section-change">
                    <span class="section-badge">{{ $old_section }}</span>
                    <span class="arrow">→</span>
                    <span class="section-badge">{{ $new_section }}</span>
                </div>
                <div style="font-size: 12px; color: #6b7280; margin-top: 8px;">
                    Effective: {{ $transfer_date }}
                </div>
            </div>

            <p style="text-align: center; margin: 20px 0; font-size: 14px;">
                <strong>Please check your updated schedule for any changes in class times, rooms, or instructors.</strong>
            </p>

            @if($portal_url)
            <div class="button-container">
                <a href="{{ $portal_url }}" class="action-button">
                    View My Schedule
                </a>
            </div>
            @endif

            <div class="help-section">
                <h4>Need Help?</h4>
                <p>For any questions about your schedule or this transfer, please visit the <strong>Library Office</strong> during business hours.</p>
            </div>
        </div>

        <div class="footer">
            <p>This is an automated notification from the Academic Affairs Office.</p>
        </div>
    </div>
</body>
</html>
