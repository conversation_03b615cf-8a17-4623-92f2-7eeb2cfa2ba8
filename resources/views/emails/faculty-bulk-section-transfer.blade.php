<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Students Added to Your Class</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.5;
            color: #374151;
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9fafb;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }
        .content {
            padding: 24px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 16px;
            color: #111827;
            font-weight: 500;
        }
        .summary-card {
            background-color: #fef3c7;
            border: 1px solid #fcd34d;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            text-align: center;
        }
        .student-count {
            font-size: 24px;
            font-weight: 700;
            color: #92400e;
            margin-bottom: 4px;
        }
        .subject-info {
            font-size: 14px;
            color: #92400e;
        }
        .section-badge {
            display: inline-block;
            background-color: #dbeafe;
            color: #1e40af;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 12px;
            margin: 0 4px;
        }
        .students-list {
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            max-height: 250px;
            overflow-y: auto;
        }
        .students-list h4 {
            margin: 0 0 12px 0;
            color: #374151;
            font-size: 14px;
            font-weight: 600;
        }
        .student-item {
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .student-item:last-child {
            margin-bottom: 0;
        }
        .student-info {
            flex: 1;
        }
        .student-name {
            font-weight: 600;
            color: #111827;
            font-size: 14px;
        }
        .student-details {
            font-size: 12px;
            color: #6b7280;
        }
        .from-section {
            background-color: #fee2e2;
            color: #991b1b;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }
        .action-items {
            background-color: #f0fdf4;
            border-left: 4px solid #10b981;
            padding: 16px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .action-items h4 {
            margin: 0 0 12px 0;
            color: #065f46;
            font-size: 14px;
            font-weight: 600;
        }
        .action-items ul {
            margin: 0;
            padding-left: 16px;
            color: #065f46;
            font-size: 14px;
        }
        .action-items li {
            margin-bottom: 4px;
        }
        .footer {
            background-color: #f9fafb;
            padding: 16px;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
            border-top: 1px solid #e5e7eb;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .content {
                padding: 20px;
            }
            .student-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>👥 Multiple Students Added</h1>
        </div>

        <div class="content">
            <div class="greeting">
                Hi {{ $faculty_name }},
            </div>

            <p><strong>{{ $student_count }}</strong> students have been transferred to your class section:</p>

            <div class="summary-card">
                <div class="student-count">{{ $student_count }}</div>
                <div class="subject-info">
                    {{ $subject_code }} - Section <span class="section-badge">{{ $new_section }}</span>
                </div>
                <div style="font-size: 12px; color: #92400e; margin-top: 8px;">
                    Transfer Date: {{ $transfer_date }}
                </div>
            </div>

            <div class="students-list">
                <h4>New Students:</h4>
                @foreach($students as $student)
                <div class="student-item">
                    <div class="student-info">
                        <div class="student-name">{{ $student['name'] }}</div>
                        <div class="student-details">
                            ID: {{ $student['id'] }} | {{ $student['course'] }}
                        </div>
                    </div>
                    <div class="from-section">
                        From {{ $student['old_section'] }}
                    </div>
                </div>
                @endforeach
            </div>

            <div class="action-items">
                <h4>Action Required:</h4>
                <ul>
                    <li>Add all students to attendance records</li>
                    <li>Include in ongoing assignments and projects</li>
                    <li>Update your gradebook</li>
                    <li>Ensure access to class materials</li>
                    <li>Consider welcoming them to the class</li>
                </ul>
            </div>

            <p style="font-size: 14px; color: #6b7280; text-align: center;">
                Questions? Contact the <strong>Library Office</strong> during business hours.
            </p>
        </div>

        <div class="footer">
            <p>Academic Affairs Office - Automated Notification</p>
        </div>
    </div>
</body>
</html>
