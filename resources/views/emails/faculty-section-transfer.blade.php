<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Student Added to Your Class</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.5;
            color: #374151;
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9fafb;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }
        .content {
            padding: 24px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 16px;
            color: #111827;
            font-weight: 500;
        }
        .student-card {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .student-name {
            font-size: 18px;
            font-weight: 700;
            color: #065f46;
            margin-bottom: 8px;
        }
        .student-details {
            font-size: 14px;
            color: #374151;
            margin-bottom: 4px;
        }
        .transfer-info {
            background-color: #f3f4f6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .subject-code {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }
        .section-change {
            font-size: 14px;
            color: #6b7280;
        }
        .section-badge {
            display: inline-block;
            background-color: #dbeafe;
            color: #1e40af;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 12px;
            margin: 0 4px;
        }
        .arrow {
            color: #9ca3af;
            margin: 0 8px;
        }
        .action-items {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 16px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .action-items h4 {
            margin: 0 0 12px 0;
            color: #92400e;
            font-size: 14px;
            font-weight: 600;
        }
        .action-items ul {
            margin: 0;
            padding-left: 16px;
            color: #92400e;
            font-size: 14px;
        }
        .action-items li {
            margin-bottom: 4px;
        }
        .footer {
            background-color: #f9fafb;
            padding: 16px;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
            border-top: 1px solid #e5e7eb;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>👨‍🏫 New Student Added</h1>
        </div>

        <div class="content">
            <div class="greeting">
                Hi {{ $faculty_name }},
            </div>

            <p>A student has been transferred to your class section:</p>

            <div class="student-card">
                <div class="student-name">{{ $student_name }}</div>
                <div class="student-details"><strong>ID:</strong> {{ $student_id }}</div>
                <div class="student-details"><strong>Course:</strong> {{ $student_course }}</div>
            </div>

            <div class="transfer-info">
                <div class="subject-code">{{ $subject_code }}</div>
                <div class="section-change">
                    From Section <span class="section-badge">{{ $old_section }}</span>
                    <span class="arrow">→</span>
                    To Section <span class="section-badge">{{ $new_section }}</span>
                </div>
                <div style="font-size: 12px; color: #6b7280; margin-top: 8px;">
                    Transfer Date: {{ $transfer_date }}
                </div>
            </div>

            <div class="action-items">
                <h4>Action Required:</h4>
                <ul>
                    <li>Add student to your attendance records</li>
                    <li>Include in ongoing assignments and projects</li>
                    <li>Update your gradebook</li>
                    <li>Ensure access to class materials</li>
                </ul>
            </div>

            <p style="font-size: 14px; color: #6b7280; text-align: center;">
                Questions? Contact the <strong>Library Office</strong> during business hours.
            </p>
        </div>

        <div class="footer">
            <p>Academic Affairs Office - Automated Notification</p>
        </div>
    </div>
</body>
</html>
