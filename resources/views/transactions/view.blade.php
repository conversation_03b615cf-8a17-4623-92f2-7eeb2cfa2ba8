<?php
use Hashids\Hashids;
use App\Models\Transaction;
use App\Models\GeneralSetting;
use App\Models\GeneralSettings;
// $hashids = new Hashids();

$transaction = Transaction::where('transaction_number', $transaction_number)->first();
$semester = GeneralSetting::first()->semester;


?>
{{-- @dd($transaction) --}}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Invoice #{{ $transaction->invoicenumber }}</title>
        <script src="https://cdn.tailwindcss.com"></script>
    {{-- @vite(['resources/css/filament/admin/theme.css', 'resources/js/app.js']) --}}
</head>

<body>


    <div class="bg-white p-8 shadow-lg rounded-lg m-auto">
        <div class="flex justify-between items-center mb-8">
            <div>
                <img src="{{ asset('logo.svg') }}" alt="Company Logo" width="100" height="100"
                    style="aspect-ratio: 100 / 100; object-fit: cover;" />
                <h1 class="text-2xl font-bold">DATA CENTER COLLEGE of the Philippines of Baguio City, Inc</h1>
                <p class="text-slate-500"> 118 Bonifacio Street, Holyghost Proper, Baguio City</p>
                <p class="text-slate-500"> Tel: 444-5389/442-4160</p>
                <p class="text-slate-500">Website: www.dccp.edu.ph</p>
                <div class="mt-4">
                    <p class="font-bold">Student Information:</p>
                    <p>{{ $transaction->student_full_name }}</p>
                    <p> {{ $transaction->student_course }} - {{ $semester }}</p>
                    <p> Student ID: {{ $transaction->student_id }}</p>
                    <p> Email: {{ $transaction->student_email }}</p>
                    <p> Contact: {{ $transaction->student_personal_contact }}</p>
                </div>
            </div>
            <div class="text-right">
                <h2 class="text-2xl font-bold">Invoice</h2>
                <p class="text-slate-500">O.R no #: {{ $transaction->invoicenumber }}</p>
                <p class="text-slate-500">Date: {{ $transaction->created_at->format('F d, Y') }}</p>
            </div>
        </div>
        <table class="w-full border-collapse">
            <thead>
                <tr class="bg-blue-600/70 text-slate-900">
                    <th class="py-2 px-4 text-left">Particular</th>
                    <th class="py-2 px-4 text-right">Amount</th>

                </tr>
            </thead>
            <tbody>
                @php
                    $total = 0;
                    $settlements = $transaction->settlements;
                @endphp
                @foreach ($settlements as $key => $value)
                    @if ($value > 0)
                        {{--                            <tr class="item"> --}}
                        {{--                                <td>{{ ucwords(str_replace('_', ' ', $key)) }}</td> --}}
                        {{--                                <td>₱{{ number_format($value, 2) }}</td> --}}
                        {{--                            </tr> --}}
                        <tr class="border-b">
                            <td class="py-2 px-4">{{ ucwords(str_replace('_', ' ', $key)) }}</td>
                            <td class="py-2 px-4 text-right">₱{{ number_format($value, 2) }}</td>

                        </tr>
                        @php
                            $total += $value;
                        @endphp
                    @endif
                @endforeach

            </tbody>
        </table>
        <div class="mt-8 mb-5 flex justify-end">
            <div class="w-1/2 space-y-2">
                <div class="flex justify-between">
                    <span class="font-bold">Subtotal:</span>
                    <span>₱{{ number_format($total, 2) }}</span>
                </div>
                {{--            <div class="flex justify-between"> --}}
                {{--                <span class="font-bold">Tax (8%):</span> --}}
                {{--                <span>$18.00</span> --}}
                {{--            </div> --}}
                {{--            <div data-orientation="horizontal" role="none" class="shrink-0 bg-border h-[1px] w-full"></div> --}}
                {{--            <div class="flex justify-between"> --}}
                {{--                <span class="font-bold">Total:</span> --}}
                {{--                <span>$243.00</span> --}}
                {{--            </div> --}}
            </div>
        </div>
        <div class="mt-10 flex justify-between items-end">
            <div></div>
            <div class="w-1/2 mt-10 border-b text-center pt-4">
                <img src="{{ $transaction->signature }}" class="rounded-lg m-auto">
            </div>
        </div>
    </div>
</body>

</html>
