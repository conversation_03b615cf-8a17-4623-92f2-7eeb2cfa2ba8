<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <div 
        @if ($isSearchable() && $hasOptions())
        x-data="{
            state: $wire.{{ $applyStateBindingModifiers("\$entangle('{$getStatePath()}')") }},
            options: {{ json_encode($getOptions()) }},
            search: '',
            filteredOptions: null,
            isOpen: false,
            init() {
                this.filteredOptions = this.options;
                this.$watch('search', (value) => {
                    if (value === '') {
                        this.filteredOptions = this.options;
                        return;
                    }
                    
                    this.filteredOptions = Object.fromEntries(
                        Object.entries(this.options)
                            .filter(([id, option]) => {
                                const label = typeof option === 'object' ? option.label : option;
                                return label.toLowerCase().includes(value.toLowerCase());
                            })
                    );
                });
            }
        }"
        @endif
        class="filament-forms-subject-select-component"
    >
        @if (!$hasOptions() && !$getState())
            <div class="flex items-center text-sm text-gray-500 bg-gray-100 dark:bg-gray-800 rounded-lg px-3 py-2">
                <svg class="w-5 h-5 mr-2 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
                Please select a course and student first
            </div>
            <input type="hidden" {{ $applyStateBindingModifiers('wire:model') }}="{{ $getStatePath() }}" id="{{ $getId() }}" />
        @elseif (!$hasOptions() && $getState())
            <div class="flex items-center justify-between bg-white dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600 shadow-sm p-2">
                <div class="text-gray-900 dark:text-white">{{ $getSelectedOptionLabel() }}</div>
                <div>
                    <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>
            <input type="hidden" {{ $applyStateBindingModifiers('wire:model') }}="{{ $getStatePath() }}" id="{{ $getId() }}" />
        @elseif ($isSearchable())
            <div class="relative">
                <div @click="isOpen = ! isOpen" class="flex items-center justify-between bg-white dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600 shadow-sm cursor-pointer p-2">
                    <div x-text="state ? (typeof options[state] === 'object' ? options[state].label : options[state]) || '{{ $getSelectedOptionLabel() }}' : '{{ $getPlaceholder() ?? 'Select a subject' }}'"></div>
                    <div>
                        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </div>
                </div>
                
                <div 
                    x-show="isOpen" 
                    x-transition
                    @click.away="isOpen = false"
                    class="absolute z-10 mt-1 w-full rounded-md bg-white dark:bg-gray-700 shadow-lg border border-gray-300 dark:border-gray-600"
                >
                    <div class="p-2">
                        <input 
                            type="text" 
                            x-model="search" 
                            placeholder="Search subjects..." 
                            class="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-sm"
                        >
                    </div>
                    
                    <ul class="max-h-60 overflow-y-auto p-2 space-y-1">
                        <template x-for="(option, value) in filteredOptions" :key="value">
                            <li>
                                <button
                                    type="button"
                                    @click="state = value; isOpen = false"
                                    x-bind:disabled="option.disabled"
                                    x-bind:class="{
                                        'w-full text-left px-3 py-2 rounded-md text-sm': true,
                                        'bg-primary-500 text-white': state === value,
                                        'hover:bg-gray-100 dark:hover:bg-gray-800': state !== value && !option.disabled,
                                        'text-gray-400 cursor-not-allowed': option.disabled,
                                        'font-semibold': option.label && option.label.includes('⭐')
                                    }"
                                    x-text="typeof option === 'object' ? option.label : option"
                                ></button>
                            </li>
                        </template>
                        
                        <li x-show="Object.keys(filteredOptions).length === 0" class="px-3 py-2 text-sm text-gray-500">
                            No matching subjects found.
                        </li>
                    </ul>
                </div>
            </div>
            
            <input
                type="hidden"
                {{ $applyStateBindingModifiers('wire:model') }}="{{ $getStatePath() }}"
                id="{{ $getId() }}"
            />
        @else
            <select
                {{ $applyStateBindingModifiers('wire:model') }}="{{ $getStatePath() }}"
                id="{{ $getId() }}"
                {{ $getExtraInputAttributeBag()->class([
                    'text-gray-900 block w-full rounded-lg shadow-sm outline-none transition duration-75 border border-gray-300 focus:border-primary-500 focus:ring-1 focus:ring-inset focus:ring-primary-500 disabled:opacity-70',
                    'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:border-primary-500' => config('forms.dark_mode'),
                ]) }}
            >
                @if ($getPlaceholder())
                    <option value="">{{ $getPlaceholder() }}</option>
                @endif
                
                @php
                    $currentValue = $getState();
                    $hasCurrentValueInOptions = false;
                @endphp
                
                @foreach ($getOptions() as $value => $option)
                    @php
                        $label = $option;
                        $isDisabled = false;
                        
                        if (is_array($option)) {
                            $label = $option['label'] ?? '';
                            $isDisabled = $option['disabled'] ?? false;
                        }
                        
                        $isHighlighted = $isHighlightEnabled() && strpos($label, '⭐') !== false;
                        
                        if ((string) $value === (string) $currentValue) {
                            $hasCurrentValueInOptions = true;
                        }
                    @endphp
                    
                    <option
                        value="{{ $value }}"
                        {{ $isDisabled && $isDisableEmptyOptionsEnabled() ? 'disabled' : '' }}
                        @selected((string) $value === (string) $currentValue)
                        @class([
                            'font-semibold' => $isHighlighted,
                            'text-gray-400' => $isDisabled,
                        ])
                    >
                        {{ $label }}
                    </option>
                @endforeach
                
                @if ($currentValue && !$hasCurrentValueInOptions)
                    <option value="{{ $currentValue }}" selected>
                        {{ $getSelectedOptionLabel() }}
                    </option>
                @endif
            </select>
        @endif
    </div>
</x-dynamic-component> 