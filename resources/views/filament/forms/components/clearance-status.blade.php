@php
    $viewData = $getState();
    $status = $viewData['status'] ?? 'no_student';
    $student = $viewData['student'] ?? null;
    $isCleared = $viewData['is_cleared'] ?? false;
    $studentId = $viewData['student_id'] ?? null;
    
    // Debug logging (remove in production)
    \Log::info('Clearance Status Debug', [
        'viewData' => $viewData,
        'status' => $status,
        'student_id' => $studentId,
        'student_exists' => $student ? true : false
    ]);
@endphp

<div x-data="{
    clearing: false,
    async clearStudent() {
        if (this.clearing) return;
        this.clearing = true;
        
        try {
            await $wire.call('clearStudent', {{ $student->id ?? 'null' }});
            // Show success and refresh
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } catch (error) {
            console.error('Error clearing student:', error);
        } finally {
            this.clearing = false;
        }
    }
}" wire:key="clearance-status-{{ $student->id ?? 'no-student' }}">
    {{-- Debug info (remove in production) --}}
    @if (config('app.debug'))
        <div class="text-xs text-gray-400 mb-2">
            Debug: Status="{{ $status }}", StudentID="{{ $studentId }}", HasStudent="{{ $student ? 'yes' : 'no' }}"
        </div>
    @endif
    
    @if ($status === 'no_student')
        <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-sm text-gray-500 dark:text-gray-400">Select a student first</span>
        </div>
    @elseif ($status === 'student_not_found')
        <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-sm text-red-600 dark:text-red-400 font-medium">Student not found</span>
        </div>
    @elseif ($status === 'cleared')
        <div class="flex items-center space-x-2">
            <div class="flex items-center justify-center w-6 h-6 bg-green-100 dark:bg-green-900/20 rounded-full">
                <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border border-green-200 dark:border-green-800">
                ✓ Cleared
            </span>
        </div>
    @elseif ($status === 'not_cleared')
        <div class="flex items-center space-x-3">
            <div class="flex items-center space-x-2">
                <div class="flex items-center justify-center w-6 h-6 bg-red-100 dark:bg-red-900/20 rounded-full">
                    <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border border-red-200 dark:border-red-800">
                    ✗ Not cleared
                </span>
            </div>
            
            @if ($student)
                <button
                    type="button"
                    @click="clearStudent()"
                    :disabled="clearing"
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                >
                    <svg x-show="!clearing" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <svg x-show="clearing" class="w-3 h-3 mr-1 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span x-text="clearing ? 'Clearing...' : 'Clear Student'"></span>
                </button>
            @endif
        </div>
    @endif
</div>
