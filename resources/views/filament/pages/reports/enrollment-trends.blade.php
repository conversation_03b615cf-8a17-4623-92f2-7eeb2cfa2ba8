<!-- Enrollment Trends Report -->
<div class="space-y-6">
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Enrollment Trends Analysis</h3>
            
            @if(isset($data['enrollment_trends']) && $data['enrollment_trends']->isNotEmpty())
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">School Year</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Semester</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enrollment Count</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($data['enrollment_trends'] as $trend)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $trend['period'] }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $trend['school_year'] }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $trend['semester'] }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($trend['enrollment_count']) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <p class="text-gray-500">No enrollment trends data available.</p>
            @endif
        </div>
    </div>

    <!-- Growth Analysis -->
    @if(isset($data['growth_analysis']) && !empty($data['growth_analysis']))
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Growth Analysis</h3>
                <div class="space-y-3">
                    @foreach($data['growth_analysis'] as $growth)
                        <div class="flex items-center justify-between p-3 border rounded">
                            <div>
                                <div class="font-medium">{{ $growth['period'] }}</div>
                                <div class="text-sm text-gray-500">
                                    {{ number_format($growth['previous_enrollment']) }} → {{ number_format($growth['current_enrollment']) }}
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="font-bold {{ $growth['growth_rate'] > 0 ? 'text-green-600' : ($growth['growth_rate'] < 0 ? 'text-red-600' : 'text-gray-600') }}">
                                    {{ $growth['growth_rate'] > 0 ? '+' : '' }}{{ number_format($growth['growth_rate'], 1) }}%
                                </div>
                                <div class="text-xs text-gray-500">{{ ucfirst($growth['growth_direction']) }}</div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Forecasting -->
    @if(isset($data['forecasting']) && !empty($data['forecasting']))
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Enrollment Forecast</h3>
                <div class="bg-blue-50 rounded-lg p-4">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600">{{ number_format($data['forecasting']['next_period_forecast'] ?? 0) }}</div>
                        <div class="text-sm text-gray-600 mt-1">Predicted Next Period Enrollment</div>
                        <div class="text-xs text-gray-500 mt-2">
                            Trend: {{ ucfirst($data['forecasting']['trend_direction'] ?? 'stable') }} | 
                            Confidence: {{ $data['forecasting']['confidence'] ?? 'Medium' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Report Metadata -->
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-sm text-gray-600">
            <strong>Report Generated:</strong> {{ $data['generated_at'] ?? now()->format('Y-m-d H:i:s') }}
        </div>
    </div>
</div>
