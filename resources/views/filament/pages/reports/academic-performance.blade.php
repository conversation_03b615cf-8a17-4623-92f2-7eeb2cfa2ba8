<!-- Academic Performance Report -->
<div class="space-y-6">
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Academic Performance Analysis</h3>
            
            <!-- Grade Distribution -->
            @if(isset($data['grade_distribution']))
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Overall Grade Distribution</h4>
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                        @if(isset($data['grade_distribution']['grade_distribution']))
                            @foreach($data['grade_distribution']['grade_distribution'] as $grade => $count)
                                <div class="bg-gray-50 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-gray-900">{{ number_format($count) }}</div>
                                    <div class="text-sm text-gray-500">{{ $grade }}</div>
                                </div>
                            @endforeach
                        @endif
                    </div>
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ number_format($data['grade_distribution']['average_grade'] ?? 0, 2) }}</div>
                            <div class="text-sm text-blue-500">Overall Average Grade</div>
                            <div class="text-xs text-gray-500 mt-1">
                                Based on {{ number_format($data['grade_distribution']['total_graded_enrollments'] ?? 0) }} graded enrollments
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Program Performance -->
            @if(isset($data['program_performance']) && !empty($data['program_performance']))
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Performance by Program</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Program</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average Grade</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Enrollments</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Passing Rate</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($data['program_performance'] as $program)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ $program['program_code'] }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ number_format($program['average_grade'] ?? 0, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ number_format($program['total_enrollments']) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ number_format($program['passing_rate'] ?? 0, 1) }}%
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @php
                                                $passingRate = $program['passing_rate'] ?? 0;
                                                $color = $passingRate >= 80 ? 'green' : ($passingRate >= 60 ? 'yellow' : 'red');
                                                $label = $passingRate >= 80 ? 'Excellent' : ($passingRate >= 60 ? 'Good' : 'Needs Improvement');
                                            @endphp
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $color }}-100 text-{{ $color }}-800">
                                                {{ $label }}
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif

            <!-- Year Level Performance -->
            @if(isset($data['year_level_performance']) && !empty($data['year_level_performance']))
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Performance by Year Level</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        @foreach($data['year_level_performance'] as $yearLevel)
                            <div class="border rounded-lg p-4">
                                <div class="text-center">
                                    <h5 class="font-medium text-gray-900 mb-2">{{ $yearLevel['year_label'] }}</h5>
                                    <div class="text-2xl font-bold text-blue-600">{{ number_format($yearLevel['average_grade'] ?? 0, 2) }}</div>
                                    <div class="text-sm text-gray-500">Average Grade</div>
                                    <div class="text-xs text-gray-400 mt-1">
                                        {{ number_format($yearLevel['passing_rate'] ?? 0, 1) }}% passing rate
                                    </div>
                                    <div class="text-xs text-gray-400">
                                        {{ number_format($yearLevel['total_enrollments']) }} enrollments
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Completion Rates -->
            @if(isset($data['completion_rates']))
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Course Completion Rates</h4>
                    <div class="bg-green-50 rounded-lg p-6">
                        <div class="text-center">
                            <div class="text-4xl font-bold text-green-600">{{ number_format($data['completion_rates']['completion_rate'] ?? 0, 1) }}%</div>
                            <div class="text-lg text-green-500 mt-2">Overall Completion Rate</div>
                            <div class="text-sm text-gray-600 mt-2">
                                {{ number_format($data['completion_rates']['completed_enrollments'] ?? 0) }} completed out of 
                                {{ number_format($data['completion_rates']['total_enrollments'] ?? 0) }} total enrollments
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Performance Insights -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">Performance Insights</h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li>Academic performance data is based on completed class enrollments with recorded grades</li>
                                <li>Passing rate is calculated based on grades of 75 and above</li>
                                <li>Completion rate indicates the percentage of enrollments with recorded final grades</li>
                                <li>Performance trends can help identify programs or year levels that may need additional support</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Metadata -->
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-sm text-gray-600">
            <strong>Report Generated:</strong> {{ $data['generated_at'] ?? now()->format('Y-m-d H:i:s') }}
            @if(isset($data['filters']))
                <br><strong>Filters:</strong> 
                School Year: {{ $data['filters']['school_year'] ?? 'N/A' }}, 
                Semester: {{ $data['filters']['semester'] ?? 'N/A' }}
            @endif
        </div>
    </div>
</div>
