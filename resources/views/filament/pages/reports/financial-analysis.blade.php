<!-- Financial Analysis Report -->
<div class="space-y-6">
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Financial Analysis</h3>
            
            <!-- Financial Overview -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Financial Analysis Under Development</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>Financial analysis features are currently being developed and require integration with the following models:</p>
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li>StudentTuition model for tuition fee analysis</li>
                                <li>Transaction model for payment tracking</li>
                                <li>Financial records for outstanding balances</li>
                                <li>Payment gateway integration for collection rates</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tuition Analysis Placeholder -->
            @if(isset($data['tuition_analysis']))
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Tuition Fee Analysis</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-green-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-green-600">₱{{ number_format($data['tuition_analysis']['total_tuition_fees'] ?? 0, 2) }}</div>
                            <div class="text-sm text-green-500">Total Tuition Fees</div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-blue-600">₱{{ number_format($data['tuition_analysis']['average_tuition_per_student'] ?? 0, 2) }}</div>
                            <div class="text-sm text-blue-500">Average per Student</div>
                        </div>
                        <div class="bg-purple-50 rounded-lg p-4 text-center">
                            <div class="text-lg font-bold text-purple-600">{{ count($data['tuition_analysis']['tuition_by_program'] ?? []) }}</div>
                            <div class="text-sm text-purple-500">Programs Analyzed</div>
                        </div>
                    </div>
                    <div class="mt-4 text-sm text-gray-500 text-center">
                        {{ $data['tuition_analysis']['note'] ?? 'Tuition analysis requires StudentTuition model integration' }}
                    </div>
                </div>
            @endif

            <!-- Payment Patterns Placeholder -->
            @if(isset($data['payment_patterns']))
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Payment Patterns</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-indigo-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-indigo-600">₱{{ number_format($data['payment_patterns']['total_payments'] ?? 0, 2) }}</div>
                            <div class="text-sm text-indigo-500">Total Payments</div>
                        </div>
                        <div class="bg-teal-50 rounded-lg p-4 text-center">
                            <div class="text-lg font-bold text-teal-600">{{ count($data['payment_patterns']['payment_methods'] ?? []) }}</div>
                            <div class="text-sm text-teal-500">Payment Methods</div>
                        </div>
                        <div class="bg-orange-50 rounded-lg p-4 text-center">
                            <div class="text-lg font-bold text-orange-600">{{ count($data['payment_patterns']['payment_timeline'] ?? []) }}</div>
                            <div class="text-sm text-orange-500">Payment Records</div>
                        </div>
                    </div>
                    <div class="mt-4 text-sm text-gray-500 text-center">
                        {{ $data['payment_patterns']['note'] ?? 'Payment analysis requires Transaction model integration' }}
                    </div>
                </div>
            @endif

            <!-- Outstanding Balances Placeholder -->
            @if(isset($data['outstanding_balances']))
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Outstanding Balances</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-red-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-red-600">₱{{ number_format($data['outstanding_balances']['total_outstanding'] ?? 0, 2) }}</div>
                            <div class="text-sm text-red-500">Total Outstanding</div>
                        </div>
                        <div class="bg-yellow-50 rounded-lg p-4 text-center">
                            <div class="text-lg font-bold text-yellow-600">{{ count($data['outstanding_balances']['outstanding_by_program'] ?? []) }}</div>
                            <div class="text-sm text-yellow-500">Programs with Balances</div>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4 text-center">
                            <div class="text-lg font-bold text-gray-600">{{ count($data['outstanding_balances']['outstanding_by_year_level'] ?? []) }}</div>
                            <div class="text-sm text-gray-500">Year Levels</div>
                        </div>
                    </div>
                    <div class="mt-4 text-sm text-gray-500 text-center">
                        {{ $data['outstanding_balances']['note'] ?? 'Outstanding balance analysis requires financial models integration' }}
                    </div>
                </div>
            @endif

            <!-- Collection Rates Placeholder -->
            @if(isset($data['collection_rates']))
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Collection Rates</h4>
                    <div class="bg-emerald-50 rounded-lg p-6">
                        <div class="text-center">
                            <div class="text-4xl font-bold text-emerald-600">{{ number_format($data['collection_rates']['collection_rate'] ?? 0, 1) }}%</div>
                            <div class="text-lg text-emerald-500 mt-2">Overall Collection Rate</div>
                            <div class="text-sm text-gray-600 mt-2">
                                Based on {{ count($data['collection_rates']['collection_by_program'] ?? []) }} programs analyzed
                            </div>
                        </div>
                    </div>
                    <div class="mt-4 text-sm text-gray-500 text-center">
                        {{ $data['collection_rates']['note'] ?? 'Collection rate analysis requires financial models integration' }}
                    </div>
                </div>
            @endif

            <!-- Implementation Roadmap -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">Implementation Roadmap</h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <p class="mb-2">To enable comprehensive financial analysis, the following integrations are planned:</p>
                            <ol class="list-decimal list-inside space-y-1">
                                <li>StudentTuition model integration for fee structure analysis</li>
                                <li>Transaction model integration for payment tracking</li>
                                <li>Financial dashboard with real-time collection rates</li>
                                <li>Outstanding balance management and reporting</li>
                                <li>Payment method analysis and trends</li>
                                <li>Revenue forecasting and budget planning tools</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Metadata -->
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-sm text-gray-600">
            <strong>Report Generated:</strong> {{ $data['generated_at'] ?? now()->format('Y-m-d H:i:s') }}
            @if(isset($data['filters']))
                <br><strong>Filters:</strong> 
                School Year: {{ $data['filters']['school_year'] ?? 'N/A' }}, 
                Semester: {{ $data['filters']['semester'] ?? 'N/A' }}
            @endif
        </div>
    </div>
</div>
