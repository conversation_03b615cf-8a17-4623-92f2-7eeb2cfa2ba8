<!-- Demographic Analysis Report -->
<div class="space-y-6">
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Demographic Analysis</h3>
            
            <!-- Gender Analysis -->
            @if(isset($data['gender_analysis']))
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Gender Distribution</h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="bg-blue-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ number_format($data['gender_analysis']['male_count']) }}</div>
                            <div class="text-sm text-blue-500">Male Students</div>
                            <div class="text-xs text-gray-500">{{ number_format($data['gender_analysis']['male_percentage'], 1) }}%</div>
                        </div>
                        <div class="bg-pink-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-pink-600">{{ number_format($data['gender_analysis']['female_count']) }}</div>
                            <div class="text-sm text-pink-500">Female Students</div>
                            <div class="text-xs text-gray-500">{{ number_format($data['gender_analysis']['female_percentage'], 1) }}%</div>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-gray-600">{{ number_format($data['gender_analysis']['total_students']) }}</div>
                            <div class="text-sm text-gray-500">Total Students</div>
                        </div>
                        <div class="bg-purple-50 rounded-lg p-4 text-center">
                            <div class="text-lg font-bold text-purple-600">{{ $data['gender_analysis']['gender_ratio'] }}</div>
                            <div class="text-sm text-purple-500">M:F Ratio</div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Age Analysis -->
            @if(isset($data['age_analysis']))
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Age Distribution</h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div class="bg-green-50 rounded-lg p-4 text-center">
                            <div class="text-xl font-bold text-green-600">{{ number_format($data['age_analysis']['average_age'], 1) }}</div>
                            <div class="text-sm text-green-500">Average Age</div>
                        </div>
                        <div class="bg-yellow-50 rounded-lg p-4 text-center">
                            <div class="text-xl font-bold text-yellow-600">{{ number_format($data['age_analysis']['median_age'], 1) }}</div>
                            <div class="text-sm text-yellow-500">Median Age</div>
                        </div>
                        <div class="bg-red-50 rounded-lg p-4 text-center">
                            <div class="text-xl font-bold text-red-600">{{ $data['age_analysis']['min_age'] }}</div>
                            <div class="text-sm text-red-500">Youngest</div>
                        </div>
                        <div class="bg-indigo-50 rounded-lg p-4 text-center">
                            <div class="text-xl font-bold text-indigo-600">{{ $data['age_analysis']['max_age'] }}</div>
                            <div class="text-sm text-indigo-500">Oldest</div>
                        </div>
                    </div>
                    
                    @if(isset($data['age_analysis']['age_groups']) && !empty($data['age_analysis']['age_groups']))
                        <div class="grid grid-cols-2 md:grid-cols-5 gap-2">
                            @foreach($data['age_analysis']['age_groups'] as $ageGroup => $count)
                                <div class="bg-gray-50 rounded p-3 text-center">
                                    <div class="text-lg font-medium">{{ $count }}</div>
                                    <div class="text-xs text-gray-500">{{ $ageGroup }}</div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            @endif

            <!-- Program Demographics -->
            @if(isset($data['program_demographics']) && !empty($data['program_demographics']))
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Demographics by Program</h4>
                    <div class="space-y-4">
                        @foreach($data['program_demographics'] as $program)
                            <div class="border rounded-lg p-4">
                                <h5 class="font-medium text-gray-900 mb-2">{{ $program['program_code'] }}</h5>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div>
                                        <div class="text-sm text-gray-500">Total Students</div>
                                        <div class="text-lg font-medium">{{ number_format($program['total_students']) }}</div>
                                    </div>
                                    <div>
                                        <div class="text-sm text-gray-500">Average Age</div>
                                        <div class="text-lg font-medium">{{ number_format($program['average_age'], 1) }}</div>
                                    </div>
                                    <div>
                                        <div class="text-sm text-gray-500">Gender Split</div>
                                        <div class="text-sm">
                                            M: {{ $program['gender_distribution']['Male'] ?? 0 }} | 
                                            F: {{ $program['gender_distribution']['Female'] ?? 0 }}
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-sm text-gray-500">Year Levels</div>
                                        <div class="text-sm">
                                            @if(isset($program['year_level_distribution']))
                                                @foreach($program['year_level_distribution'] as $year => $count)
                                                    {{ $year }}Y:{{ $count }}{{ !$loop->last ? ', ' : '' }}
                                                @endforeach
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Geographic Analysis -->
            @if(isset($data['geographic_analysis']))
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Geographic Distribution</h4>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800">Geographic Analysis</h3>
                                <div class="mt-2 text-sm text-yellow-700">
                                    <p>{{ $data['geographic_analysis']['address_analysis'] ?? 'Geographic analysis requires more detailed address parsing' }}</p>
                                    <p class="mt-1">Students with address data: {{ number_format($data['geographic_analysis']['total_with_address'] ?? 0) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Report Metadata -->
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-sm text-gray-600">
            <strong>Report Generated:</strong> {{ $data['generated_at'] ?? now()->format('Y-m-d H:i:s') }}
            @if(isset($data['filters']))
                <br><strong>Filters:</strong> 
                School Year: {{ $data['filters']['school_year'] ?? 'N/A' }}, 
                Semester: {{ $data['filters']['semester'] ?? 'N/A' }}
            @endif
        </div>
    </div>
</div>
