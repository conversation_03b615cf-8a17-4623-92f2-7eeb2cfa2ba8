<!-- Year Level Analysis Report -->
<div class="space-y-6">
    <!-- Year Level Overview -->
    @if(isset($data['year_level_details']) && $data['year_level_details']->isNotEmpty())
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Year Level Analysis</h3>
                
                <!-- Year Level Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    @foreach($data['year_level_details'] as $yearLevel)
                        <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-green-600">{{ number_format($yearLevel['total_students']) }}</div>
                                <div class="text-sm text-gray-600 mt-1">{{ $yearLevel['year_label'] }}</div>
                                <div class="text-xs text-gray-500 mt-2">Avg Age: {{ number_format($yearLevel['average_age'] ?? 0, 1) }}</div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Detailed Year Level Table -->
                <div class="overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year Level</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Students</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Program Distribution</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender Split</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average Age</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($data['year_level_details'] as $yearLevel)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ $yearLevel['year_label'] }}</div>
                                        <div class="text-sm text-gray-500">Year {{ $yearLevel['year_level'] }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format($yearLevel['total_students']) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        @if(isset($yearLevel['program_breakdown']) && !empty($yearLevel['program_breakdown']))
                                            <div class="flex flex-wrap gap-1">
                                                @foreach($yearLevel['program_breakdown'] as $program => $count)
                                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                        {{ $program }}: {{ $count }}
                                                    </span>
                                                @endforeach
                                            </div>
                                        @else
                                            N/A
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        @if(isset($yearLevel['gender_breakdown']))
                                            <div class="flex space-x-2">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    M: {{ $yearLevel['gender_breakdown']['Male'] ?? 0 }}
                                                </span>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                                                    F: {{ $yearLevel['gender_breakdown']['Female'] ?? 0 }}
                                                </span>
                                            </div>
                                        @else
                                            N/A
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format($yearLevel['average_age'] ?? 0, 1) }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    <!-- Student Progression Analysis -->
    @if(isset($data['progression_analysis']) && !empty($data['progression_analysis']))
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Student Progression Analysis</h3>
                <div class="space-y-4">
                    @foreach($data['progression_analysis'] as $progression)
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-900">
                                        {{ $progression['from_year'] }}{{ $progression['from_year'] == 1 ? 'st' : ($progression['from_year'] == 2 ? 'nd' : ($progression['from_year'] == 3 ? 'rd' : 'th')) }} Year 
                                        → 
                                        {{ $progression['to_year'] }}{{ $progression['to_year'] == 1 ? 'st' : ($progression['to_year'] == 2 ? 'nd' : ($progression['to_year'] == 3 ? 'rd' : 'th')) }} Year
                                    </h4>
                                    <p class="text-sm text-gray-600">
                                        {{ number_format($progression['current_count']) }} students → {{ number_format($progression['next_count']) }} students
                                    </p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold {{ $progression['progression_rate'] >= 80 ? 'text-green-600' : ($progression['progression_rate'] >= 60 ? 'text-yellow-600' : 'text-red-600') }}">
                                        {{ number_format($progression['progression_rate'], 1) }}%
                                    </div>
                                    <div class="text-sm text-gray-500">Progression Rate</div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Retention Rates -->
    @if(isset($data['retention_rates']) && !empty($data['retention_rates']))
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Retention Rates by Program</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @foreach($data['retention_rates'] as $retention)
                        <div class="border rounded-lg p-4">
                            <div class="text-center">
                                <h4 class="font-medium text-gray-900 mb-2">{{ $retention['program'] }}</h4>
                                <div class="text-3xl font-bold {{ $retention['retention_rate'] >= 80 ? 'text-green-600' : ($retention['retention_rate'] >= 60 ? 'text-yellow-600' : 'text-red-600') }}">
                                    {{ number_format($retention['retention_rate'], 1) }}%
                                </div>
                                <div class="text-sm text-gray-500 mt-2">
                                    {{ number_format($retention['current_count']) }} current / {{ number_format($retention['previous_count']) }} previous
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Enrollment Status by Year Level -->
    @if(isset($data['year_level_details']) && $data['year_level_details']->isNotEmpty())
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Enrollment Status by Year Level</h3>
                <div class="space-y-4">
                    @foreach($data['year_level_details'] as $yearLevel)
                        @if(isset($yearLevel['enrollment_status']) && !empty($yearLevel['enrollment_status']))
                            <div class="border rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">{{ $yearLevel['year_label'] }}</h4>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                                    @foreach($yearLevel['enrollment_status'] as $status => $count)
                                        <div class="bg-gray-50 rounded p-2 text-center">
                                            <div class="text-sm font-medium">{{ $count }}</div>
                                            <div class="text-xs text-gray-500">{{ ucfirst($status) }}</div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Report Metadata -->
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-sm text-gray-600">
            <strong>Report Generated:</strong> {{ $data['generated_at'] ?? now()->format('Y-m-d H:i:s') }}
            @if(isset($data['filters']))
                <br><strong>Filters:</strong> 
                School Year: {{ $data['filters']['school_year'] ?? 'N/A' }}, 
                Semester: {{ $data['filters']['semester'] ?? 'N/A' }}
            @endif
        </div>
    </div>
</div>
