<!-- Program Analysis Report -->
<div class="space-y-6">
    <!-- Program Details -->
    @if(isset($data['program_details']) && $data['program_details']->isNotEmpty())
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Program Analysis (BSIT, BSBA, BSHM)</h3>
                
                <!-- Program Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    @foreach($data['program_details'] as $program)
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-xl font-bold text-gray-900">{{ $program['program_code'] }}</h4>
                                    <p class="text-sm text-gray-600">{{ $program['program_title'] }}</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-3xl font-bold text-blue-600">{{ number_format($program['total_students']) }}</div>
                                    <div class="text-sm text-gray-500">Students</div>
                                </div>
                            </div>
                            
                            <div class="mt-4 grid grid-cols-2 gap-4">
                                <div>
                                    <div class="text-sm text-gray-500">Average Age</div>
                                    <div class="text-lg font-semibold">{{ number_format($program['average_age'] ?? 0, 1) }}</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">Year Levels</div>
                                    <div class="text-lg font-semibold">{{ count($program['year_level_breakdown'] ?? []) }}</div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Detailed Analysis Table -->
                <div class="overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Program</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Students</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Male/Female</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year Level Distribution</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Age</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($data['program_details'] as $program)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ $program['program_code'] }}</div>
                                        <div class="text-sm text-gray-500">{{ $program['program_title'] }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format($program['total_students']) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        @if(isset($program['gender_breakdown']))
                                            <div class="flex space-x-2">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    M: {{ $program['gender_breakdown']['Male'] ?? 0 }}
                                                </span>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                                                    F: {{ $program['gender_breakdown']['Female'] ?? 0 }}
                                                </span>
                                            </div>
                                        @else
                                            N/A
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        @if(isset($program['year_level_breakdown']))
                                            <div class="flex flex-wrap gap-1">
                                                @foreach($program['year_level_breakdown'] as $year => $count)
                                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                                        {{ $year }}Y: {{ $count }}
                                                    </span>
                                                @endforeach
                                            </div>
                                        @else
                                            N/A
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format($program['average_age'] ?? 0, 1) }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    <!-- Age Distribution Analysis -->
    @if(isset($data['program_details']) && $data['program_details']->isNotEmpty())
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Age Distribution by Program</h3>
                <div class="space-y-4">
                    @foreach($data['program_details'] as $program)
                        @if(isset($program['age_distribution']) && !empty($program['age_distribution']))
                            <div class="border rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">{{ $program['program_code'] }}</h4>
                                <div class="grid grid-cols-2 md:grid-cols-5 gap-2">
                                    @foreach($program['age_distribution'] as $ageRange => $count)
                                        <div class="bg-gray-50 rounded p-2 text-center">
                                            <div class="text-sm font-medium">{{ $count }}</div>
                                            <div class="text-xs text-gray-500">{{ $ageRange }}</div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Enrollment Status by Program -->
    @if(isset($data['program_details']) && $data['program_details']->isNotEmpty())
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Enrollment Status by Program</h3>
                <div class="space-y-4">
                    @foreach($data['program_details'] as $program)
                        @if(isset($program['enrollment_status_breakdown']) && !empty($program['enrollment_status_breakdown']))
                            <div class="border rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">{{ $program['program_code'] }}</h4>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                                    @foreach($program['enrollment_status_breakdown'] as $status => $count)
                                        <div class="bg-gray-50 rounded p-2 text-center">
                                            <div class="text-sm font-medium">{{ $count }}</div>
                                            <div class="text-xs text-gray-500">{{ ucfirst($status) }}</div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Program Comparison Chart Data -->
    @if(isset($data['comparison_chart']) && !empty($data['comparison_chart']))
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Program Comparison</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Program</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Students</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Male Students</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Female Students</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender Ratio</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($data['comparison_chart'] as $comparison)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $comparison['program'] }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format($comparison['total_students']) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format($comparison['male_students']) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format($comparison['female_students']) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        @php
                                            $total = $comparison['male_students'] + $comparison['female_students'];
                                            $malePercent = $total > 0 ? ($comparison['male_students'] / $total) * 100 : 0;
                                            $femalePercent = $total > 0 ? ($comparison['female_students'] / $total) * 100 : 0;
                                        @endphp
                                        <div class="flex items-center space-x-2">
                                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $malePercent }}%"></div>
                                            </div>
                                            <span class="text-xs">{{ number_format($malePercent, 1) }}% M</span>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    <!-- Report Metadata -->
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-sm text-gray-600">
            <strong>Report Generated:</strong> {{ $data['generated_at'] ?? now()->format('Y-m-d H:i:s') }}
            @if(isset($data['filters']))
                <br><strong>Filters:</strong> 
                School Year: {{ $data['filters']['school_year'] ?? 'N/A' }}, 
                Semester: {{ $data['filters']['semester'] ?? 'N/A' }}
            @endif
        </div>
    </div>
</div>
