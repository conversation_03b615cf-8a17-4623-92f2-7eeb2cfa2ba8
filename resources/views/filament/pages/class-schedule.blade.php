<x-filament-panels::page>
    <x-filament-panels::form wire:submit.prevent="submit">
        {{ $this->form }}
        <x-filament-panels::form.actions :actions="$this->getFormActions()" />
    </x-filament-panels::form>

    @if (!empty($this->noScheduleMessage))
        <div class="mt-4 p-4 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                {{ $this->noScheduleMessage }}
            </div>
        </div>
    @elseif (!empty($this->data['schedules']))
        <x-filament-tables::container>
            <div class="mb-4">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                    Schedule for {{ $this->data['course']['code'] ?? '' }}
                    (Year {{ $this->selectedAcademicYear }})
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Semester {{ $this->data['semester'] ?? 'N/A' }} -
                    School Year {{ $this->data['school_year'] ?? 'N/A' }}
                </p>
            </div>
            <x-filament-tables::table class="w-full">
                <x-slot name="header">
                    <x-filament-tables::header-cell>
                        Time
                    </x-filament-tables::header-cell>
                    @foreach (['Mon', 'Tue', 'Wed', 'Thur', 'Fri', 'Sat'] as $day)
                        <x-filament-tables::header-cell>
                            {{ $day }}
                        </x-filament-tables::header-cell>
                    @endforeach
                </x-slot>
                @php
                    // Ensure we have valid schedule data
                    $schedules = collect($this->data['schedules'] ?? []);
                    if ($schedules->isEmpty()) {
                        $schedules = collect();
                    }

                    $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

                    $subjectColors = [
                        'bg-red-700/50 text-white border border-red-400',
                        'bg-blue-700/50 text-white border border-blue-400',
                        'bg-green-700/50 text-white border border-green-400',
                        'bg-yellow-700/50 text-white border border-yellow-400',
                        'bg-purple-700/50 text-white border border-purple-400',
                        'bg-pink-700/50 text-white border border-pink-400',
                        'bg-indigo-700/50 text-white border border-indigo-400',
                    ];

                    $subjectColorMap = collect();
                    if (!$schedules->isEmpty() && count($subjectColors) > 0) {
                        $subjectColorMap = $schedules
                            ->pluck('class.subject_code')
                            ->filter() // Remove null/empty values
                            ->unique()
                            ->mapWithKeys(fn($code, $i) => [$code => $subjectColors[$i % count($subjectColors)]]);
                    }

                    $timeSlots = collect();
                    $start = \Carbon\Carbon::createFromTime(8, 0);
                    $end = \Carbon\Carbon::createFromTime(19, 0);
                    while ($start < $end) {
                        $timeSlots->push($start->format('H:i'));
                        $start->addMinutes(30);
                    }

                    $schedulesMap = $schedules
                        ->groupBy('day_of_week')
                        ->map(fn($daySchedules) => $daySchedules->keyBy('start_time'));

                    foreach ($days as $day) {
                        if (!isset($schedulesMap[$day])) {
                            $schedulesMap[$day] = collect();
                        }
                    }

                    $rowspans = array_fill_keys($days, []);
                @endphp

                @if ($schedules->isEmpty())
                    <x-filament-tables::row>
                        <x-filament-tables::cell colspan="7" class="text-center py-8 text-gray-500">
                            No schedules found for the selected criteria.
                        </x-filament-tables::cell>
                    </x-filament-tables::row>
                @else
                    @foreach ($timeSlots as $index => $time)
                    <x-filament-tables::row>
                        <x-filament-tables::cell class="py-2">
                            @php
                                $startTime = \Carbon\Carbon::createFromFormat('H:i', $time);
                                $endTime = $startTime->copy()->addMinutes(30);
                            @endphp
                            {{ $startTime->format('g:i A') }} - {{ $endTime->format('g:i A') }}
                        </x-filament-tables::cell>
                        @foreach ($days as $day)
                            @if (!isset($rowspans[$day][$time]))
                                @php
                                $schedule = $schedulesMap[$day]->first(function ($schedule) use ($time) {
                                    if (!$schedule || !is_array($schedule)) return false;
                                    if (!isset($schedule['start_time']) || !isset($schedule['end_time'])) return false;

                                    try {
                                        $scheduleStart = \Carbon\Carbon::createFromFormat('H:i', $schedule['start_time']);
                                        $scheduleEnd = \Carbon\Carbon::createFromFormat('H:i', $schedule['end_time']);
                                        $currentTime = \Carbon\Carbon::createFromFormat('H:i', $time);
                                        return $currentTime >= $scheduleStart && $currentTime < $scheduleEnd;
                                    } catch (\Exception $e) {
                                        return false;
                                    }
                                });
                                @endphp
                                @if ($schedule && is_array($schedule))
                                    @php
                                    try {
                                        $scheduleStart = \Carbon\Carbon::createFromFormat('H:i', $schedule['start_time'] ?? '00:00');
                                        $scheduleEnd = \Carbon\Carbon::createFromFormat('H:i', $schedule['end_time'] ?? '00:00');
                                        $rowspan = ceil($scheduleStart->diffInMinutes($scheduleEnd) / 30);
                                    } catch (\Exception $e) {
                                        $scheduleStart = \Carbon\Carbon::createFromTime(0, 0);
                                        $scheduleEnd = \Carbon\Carbon::createFromTime(0, 30);
                                        $rowspan = 1;
                                    }
                                    for ($i = 0; $i < $rowspan; $i++) {
                                        $rowspans[$day][
                                            $scheduleStart
                                                ->copy()
                                                ->addMinutes(30 * $i)
                                                ->format('H:i')
                                        ] = true;
                                    }
                                    // Get subject title based on classification
                                    $subjectTitle = 'N/A';
                                    if (isset($schedule['class']['subject']['title'])) {
                                        $subjectTitle = $schedule['class']['subject']['title'];
                                    } elseif (isset($schedule['class']['shs_subject']['title'])) {
                                        $subjectTitle = $schedule['class']['shs_subject']['title'];
                                    }

                                    $tooltipContent = "Subject: " . ($schedule['class']['subject_code'] ?? 'N/A') . "\n" .
                                        "Title: " . $subjectTitle . "\n" .
                                        "Room: " . ($schedule['room']['name'] ?? 'N/A') . "\n" .
                                        "Time: " . ($schedule['start_time'] ?? 'N/A') . " - " . ($schedule['end_time'] ?? 'N/A') . "\n" .
                                        "Faculty: " . ($schedule['class']['faculty']['full_name'] ?? 'N/A') . "\n" .
                                        "Classification: " . ucfirst($schedule['class']['classification'] ?? 'college') . "\n" .
                                        "Class ID: " . ($schedule['class']['id'] ?? 'N/A');
                                    @endphp
                                    @php
                                        $subjectCode = $schedule['class']['subject_code'] ?? null;
                                        $colorClass = '';
                                        if ($subjectCode && isset($subjectColorMap[$subjectCode])) {
                                            $colorClass = $subjectColorMap[$subjectCode];
                                        }
                                    @endphp
                                    <x-filament-tables::cell
                                        class="text-center {{ $colorClass }} rounded-lg p-4 font-semibold cursor-pointer hover:opacity-80 transition-opacity"
                                        rowspan="{{ $rowspan }}"
                                        x-tooltip.raw="{{ $tooltipContent }}"
                                        x-on:click="window.location.href = '{{ \App\Filament\Resources\ClassResource::getUrl('view', ['record' => $schedule['class']['id'] ?? 0]) }}'">
                                        <div class="text-sm font-bold">
                                            {{ $schedule['class']['subject_code'] ?? 'N/A' }}
                                        </div>
                                        <div class="text-xs">
                                            Section: {{ $schedule['class']['section'] ?? 'N/A' }}
                                        </div>
                                        <div class="text-xs">
                                            {{ $schedule['room']['name'] ?? 'TBA' }}
                                        </div>
                                        @if (isset($schedule['class']['classification']) && $schedule['class']['classification'] === 'shs')
                                            <div class="text-xs text-blue-200">SHS</div>
                                        @endif
                                    </x-filament-tables::cell>
                                @else
                                    <x-filament-tables::cell class="pt-5"></x-filament-tables::cell>
                                @endif
                            @endif
                        @endforeach
                    </x-filament-tables::row>
                    @endforeach
                @endif
            </x-filament-tables::table>
        </x-filament-tables::container>
    @else
        <div class="mt-8 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-800">
                <svg class="h-6 w-6 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No schedule selected</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Please select a course and academic year to view the class schedule.
            </p>
            @php
                $academicInfo = $this->getAcademicPeriodInfo();
            @endphp
            <div class="mt-4 text-xs text-gray-400 dark:text-gray-500">
                Current Period: {{ $academicInfo['semester_label'] ?? 'N/A' }} - {{ $academicInfo['school_year'] ?? 'N/A' }}
            </div>
        </div>
    @endif
</x-filament-panels::page>
