<x-filament-panels::page>

    {{-- Display newly generated token (only once) --}}
    @if ($newToken)
        <x-filament::section>
            <x-slot name="heading">
                New API Token Generated
            </x-slot>

            <p class="mb-4">
                Please copy your new API token. For security reasons, you will not be able to see it again.
            </p>

            <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg font-mono text-sm break-all">
                {{ $newToken }}
            </div>

            {{-- Add a button to dismiss this section if desired, or it will disappear on next page load/refresh --}}
             <div class="mt-4 text-right">
                 <x-filament::button wire:click="$set('newToken', null)" color="gray" size="sm">
                     Dismiss
                 </x-filament::button>
             </div>
        </x-filament::section>
    @endif

    {{-- List existing tokens --}}
    <x-filament::section>
        <x-slot name="heading">
            Manage API Tokens
        </x-slot>

        @if ($tokens->isEmpty())
            <p>You have not generated any API tokens yet.</p>
        @else
            <x-filament-tables::table> {{-- Corrected component --}}
                <x-slot name="header">
                    <x-filament-tables::header-cell>Name</x-filament-tables::header-cell> {{-- Corrected component --}}
                    <x-filament-tables::header-cell>Created At</x-filament-tables::header-cell> {{-- Corrected component --}}
                    {{-- <x-filament-tables::header-cell>Last Used</x-filament-tables::header-cell> --}}
                    {{-- <x-filament-tables::header-cell>Abilities</x-filament-tables::header-cell> --}}
                    <x-filament-tables::header-cell class="text-right">Actions</x-filament-tables::header-cell> {{-- Corrected component --}}
                </x-slot>

                @foreach ($tokens as $token)
                    <x-filament-tables::row> {{-- Corrected component --}}
                        <x-filament-tables::cell>{{ $token->name }}</x-filament-tables::cell> {{-- Corrected component --}}
                        <x-filament-tables::cell>{{ $token->created_at->format('Y-m-d H:i:s') }}</x-filament-tables::cell> {{-- Corrected component --}}
                        {{-- <x-filament-tables::cell>{{ $token->last_used_at ? $token->last_used_at->diffForHumans() : 'Never' }}</x-filament-tables::cell> --}}
                        {{-- <x-filament-tables::cell>{{ $token->abilities ? implode(', ', $token->abilities) : 'N/A' }}</x-filament-tables::cell> --}}
                        <x-filament-tables::cell class="text-right"> {{-- Corrected component --}}
                            <x-filament::button {{-- Button component is likely correct --}}
                                color="danger"
                                wire:click="deleteToken({{ $token->id }})"
                                wire:confirm="Are you sure you want to delete the token '{{ $token->name }}'?"
                                size="sm"
                            >
                                Delete
                            </x-filament::button>
                        </x-filament-tables::cell> {{-- Corrected component --}}
                    </x-filament-tables::row> {{-- Corrected component --}}
                @endforeach
            </x-filament-tables::table> {{-- Corrected component --}}
        @endif
    </x-filament::section>

</x-filament-panels::page>
