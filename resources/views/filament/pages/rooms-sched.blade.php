<x-filament-panels::page>
    <x-filament-panels::form wire:submit.prevent="submit">
        {{ $this->form }}
        <x-filament-panels::form.actions :actions="$this->getFormActions()" />
    </x-filament-panels::form>

    @if (!empty($this->data['schedules']))
        <x-filament-tables::container>

            <x-filament-tables::table class="w-full">
                <x-slot name="header">
                    <x-filament-tables::header-cell>
                        Time
                    </x-filament-tables::header-cell>
                    @foreach (['Mon', 'Tue', 'Wed', 'Thur', 'Fri', 'Sat'] as $day)
                        <x-filament-tables::header-cell>
                            {{ $day }}
                        </x-filament-tables::header-cell>
                    @endforeach
                </x-slot>
                @php
                    // dd($this->data['schedules']);
                    $schedules = collect($this->data['schedules']);
                    $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

                    $subjectColors = [
                        'bg-red-700/50 text-white border border-red-400',
                        'bg-blue-700/50 text-white border border-blue-400',
                        'bg-green-700/50 text-white border border-green-400',
                        'bg-yellow-700/50 text-white border border-yellow-400',
                        'bg-purple-700/50 text-white border border-purple-400',
                        'bg-pink-700/50 text-white border border-pink-400',
                        'bg-indigo-700/50 text-white border border-indigo-400',
                    ];

                    $subjectColorMap = $schedules
                        ->pluck('class.subject_code')
                        ->unique()
                        ->mapWithKeys(fn($code, $i) => [$code => $subjectColors[$i % count($subjectColors)]]);

                    $timeSlots = collect();
                    $start = \Carbon\Carbon::createFromTime(8, 0);
                    $end = \Carbon\Carbon::createFromTime(19, 0);
                    while ($start < $end) {
                        $timeSlots->push($start->format('H:i'));
                        $start->addMinutes(30);
                    }

                    $schedulesMap = $schedules
        ->groupBy('day_of_week')
        ->map(fn($daySchedules) => $daySchedules->keyBy('start_time'));

        foreach ($days as $day) {
        if (!isset($schedulesMap[$day])) {
            $schedulesMap[$day] = collect();
        }
    }

    $rowspans = array_fill_keys($days, []);
                @endphp

                @foreach ($timeSlots as $index => $time)
                    <x-filament-tables::row>
                        <x-filament-tables::cell class="py-2">
                            @php
                                $startTime = \Carbon\Carbon::createFromFormat('H:i', $time);
                                $endTime = $startTime->copy()->addMinutes(30);
                            @endphp
                            {{ $startTime->format('g:i A') }} - {{ $endTime->format('g:i A') }}
                        </x-filament-tables::cell>
                        @foreach ($days as $day)
                            @if (!isset($rowspans[$day][$time]))
                            @php
                            $schedule = $schedulesMap[$day]->first(function ($schedule) use ($time) {
                                if (!$schedule) return false;
                                $scheduleStart = \Carbon\Carbon::createFromFormat('H:i', $schedule['start_time']);
                                $scheduleEnd = \Carbon\Carbon::createFromFormat('H:i', $schedule['end_time']);
                                $currentTime = \Carbon\Carbon::createFromFormat('H:i', $time);
                                return $currentTime >= $scheduleStart && $currentTime < $scheduleEnd;
                            });
                        @endphp
                               @if ($schedule)
                               @php
                                   $scheduleStart = \Carbon\Carbon::createFromFormat('H:i', $schedule['start_time'] ?? '00:00');
                                   $scheduleEnd = \Carbon\Carbon::createFromFormat('H:i', $schedule['end_time'] ?? '00:00');
                                   $rowspan = ceil($scheduleStart->diffInMinutes($scheduleEnd) / 30);
                                   for ($i = 0; $i < $rowspan; $i++) {
                                       $rowspans[$day][
                                           $scheduleStart
                                               ->copy()
                                               ->addMinutes(30 * $i)
                                               ->format('H:i')
                                       ] = true;
                                   }
                                   $tooltipContent = "<strong>Subject:</strong> " . ($schedule['class']['subject_code'] ?? 'N/A') . "<br>" .
                                       "<strong>Room:</strong> " . ($schedule['room']['name'] ?? 'N/A') . "<br>" .
                                       "<strong>Time:</strong> " . ($schedule['start_time'] ?? 'N/A') . " - " . ($schedule['end_time'] ?? 'N/A') . "<br>" .
                                       "<strong>Subject Title:</strong> " . ($schedule['class']['subject_code'] ?? 'N/A') . "<br>" .
                                       "<strong>Semester:</strong> " . ($schedule['class']['semester']) . "<br>" .
                                       "<strong>Class ID:</strong> " . ($schedule['class']['id']);

                               @endphp
                               <x-filament-tables::cell
                                   class="text-center {{ $subjectColorMap[$schedule['class']['subject_code'] ?? ''] ?? '' }} rounded-lg p-4 font-semibold cursor-pointer hover:opacity-80 transition-opacity"
                                   rowspan="{{ $rowspan }}"
                                   x-tooltip="{
                                    content: () => `{{ $tooltipContent }}`,
                                    allowHTML: true
                                   }"
                                   x-on:click="window.location.href = '{{ \App\Filament\Resources\ClassResource::getUrl('view', ['record' => $schedule['class']['id'] ?? 0]) }}'">
                                   {{ $schedule['class']['subject_code'] ?? 'N/A' }} ({{ $schedule['class']['section'] ?? 'N/A' }})
                                   <br>
                                    Year Level: {{ $schedule['class']['academic_year'] ?? 'N/A' }}
                               </x-filament-tables::cell>
                           @else
                               <x-filament-tables::cell class="pt-5"></x-filament-tables::cell>
                           @endif
                            @endif
                        @endforeach
                    </x-filament-tables::row>
                @endforeach
            </x-filament-tables::table>
        </x-filament-tables::container>
    @endif
</x-filament-panels::page>
