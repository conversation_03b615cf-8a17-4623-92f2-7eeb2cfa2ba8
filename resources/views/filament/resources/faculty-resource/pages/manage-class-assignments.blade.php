<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Page Header -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Class Assignment Management
                    </h2>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        Efficiently assign faculty members to classes for the current academic period.
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    @php
                        $settingsService = app(\App\Services\GeneralSettingsService::class);
                        $currentYear = $settingsService->getCurrentSchoolYearString();
                        $currentSemester = $settingsService->getCurrentSemester();
                    @endphp
                    <div class="text-right">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $currentYear }}
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            {{ $currentSemester == 1 ? '1st' : '2nd' }} Semester
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            @php
                $totalClasses = \App\Models\Classes::currentAcademicPeriod()->count();
                $assignedClasses = \App\Models\Classes::currentAcademicPeriod()->whereNotNull('faculty_id')->count();
                $unassignedClasses = \App\Models\Classes::currentAcademicPeriod()->whereNull('faculty_id')->count();
                $activeFaculty = \App\Models\Faculty::where('status', 'active')->count();
            @endphp
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                            <x-heroicon-o-academic-cap class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Classes</p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $totalClasses }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                            <x-heroicon-o-check-circle class="w-5 h-5 text-green-600 dark:text-green-400" />
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Assigned</p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $assignedClasses }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
                            <x-heroicon-o-exclamation-circle class="w-5 h-5 text-red-600 dark:text-red-400" />
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Unassigned</p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $unassignedClasses }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                            <x-heroicon-o-user-group class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Active Faculty</p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $activeFaculty }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assignment Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="p-6">
                <form wire:submit="saveAssignments">
                    {{ $this->form }}
                </form>
            </div>
        </div>

        <!-- Quick Actions -->
        @if($unassignedClasses > 0)
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div class="flex items-center">
                    <x-heroicon-o-exclamation-triangle class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" />
                    <div class="flex-1">
                        <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                            {{ $unassignedClasses }} classes need faculty assignment
                        </h3>
                        <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                            Use the "Auto-Assign Classes" button to automatically distribute these classes among active faculty members.
                        </p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Recent Assignments (Optional) -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Recent Class Assignments
                </h3>
                <div class="space-y-3">
                    @php
                        $recentAssignments = \App\Models\Classes::currentAcademicPeriod()
                            ->whereNotNull('faculty_id')
                            ->with(['Faculty', 'Subject', 'ShsSubject'])
                            ->latest('updated_at')
                            ->limit(5)
                            ->get();
                    @endphp
                    
                    @forelse($recentAssignments as $class)
                        <div class="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $class->subject_title }} - {{ $class->section }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $class->isShs() ? 'SHS' : 'College' }} • 
                                    {{ $class->display_info }}
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-900 dark:text-white">
                                    {{ $class->Faculty?->full_name }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $class->updated_at->diffForHumans() }}
                                </div>
                            </div>
                        </div>
                    @empty
                        <p class="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
                            No recent assignments found.
                        </p>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
