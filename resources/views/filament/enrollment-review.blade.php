@php
    $data = $record->data ?? [];
    $course = optional(App\Models\Course::find($data['course_id'] ?? null));
@endphp
<div class="p-6 space-y-8 bg-gray-50 rounded-xl shadow-lg">
    <!-- Header: Name, Email, Status -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div class="flex items-center gap-4">
            <div class="bg-primary-100 rounded-full p-3">
                <x-heroicon-o-user class="w-10 h-10 text-primary-600" />
            </div>
            <div>
                <div class="text-2xl font-extrabold text-primary-800 flex items-center gap-2">
                    {{ $data['first_name'] ?? '-' }} {{ $data['middle_name'] ?? '' }} {{ $data['last_name'] ?? '-' }}
                    <x-heroicon-o-check class="w-6 h-6 text-green-500" title="Verified Name" />
                </div>
                <div class="flex items-center gap-2 mt-1">
                    <x-heroicon-o-envelope class="w-5 h-5 text-primary-400" />
                    <span class="text-lg font-medium text-primary-700">{{ $data['email'] ?? '-' }}</span>
                </div>
            </div>
        </div>
        <div class="flex flex-col items-start md:items-end gap-2">
            <span class="inline-flex items-center px-4 py-2 rounded-full text-base font-bold bg-primary-200 text-primary-900 shadow-md">
                <x-heroicon-o-academic-cap class="w-5 h-5 mr-1 text-primary-600" />
                {{ $course->code ?? '-' }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-base font-semibold bg-gray-200 text-gray-800 mt-1">
                <x-heroicon-o-calendar class="w-5 h-5 mr-1 text-primary-500" />
                Academic Year: <span class="ml-1">{{ $data['academic_year'] ?? '-' }}</span>
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-base font-semibold
                {{ $record->status === 'approved' ? 'bg-green-200 text-green-900' : ($record->status === 'pending' ? 'bg-yellow-200 text-yellow-900' : 'bg-red-200 text-red-900') }} mt-1">
                <x-heroicon-o-information-circle class="w-5 h-5 mr-1" />
                Status: <span class="ml-1 font-bold uppercase">{{ $record->status }}</span>
            </span>
        </div>
    </div>
    <!-- Divider -->
    <hr class="my-4 border-primary-200">
    <!-- Main Content: Two Columns -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Personal Info Card -->
        <div class="bg-white rounded-lg shadow p-6 space-y-6 border border-primary-100">
            <h2 class="text-xl font-bold flex items-center gap-2 text-primary-700 mb-4">
                <x-heroicon-o-identification class="w-6 h-6" /> Personal Information
            </h2>
            <div class="space-y-3">
                <div class="flex items-center gap-2">
                    <x-heroicon-o-cake class="w-5 h-5 text-pink-400" />
                    <span class="font-semibold">Birth Date:</span>
                    <span class="ml-2">{{ $data['birth_date'] ?? '-' }}</span>
                </div>
                <div class="flex items-center gap-2">
                    <x-heroicon-o-user-group class="w-5 h-5 text-blue-400" />
                    <span class="font-semibold">Gender:</span>
                    <span class="ml-2">{{ $data['gender'] ?? '-' }}</span>
                </div>
                <div class="flex items-center gap-2">
                    <x-heroicon-o-map-pin class="w-5 h-5 text-green-400" />
                    <span class="font-semibold">Birthplace:</span>
                    <span class="ml-2">{{ $data['birthplace'] ?? '-' }}</span>
                </div>
                <div class="flex items-center gap-2">
                    <x-heroicon-o-heart class="w-5 h-5 text-red-400" />
                    <span class="font-semibold">Civil Status:</span>
                    <span class="ml-2">{{ $data['civil_status'] ?? '-' }}</span>
                </div>
                <div class="flex items-center gap-2">
                    <x-heroicon-o-flag class="w-5 h-5 text-yellow-500" />
                    <span class="font-semibold">Citizenship:</span>
                    <span class="ml-2">{{ $data['citizenship'] ?? '-' }}</span>
                </div>
                <div class="flex items-center gap-2">
                    <x-heroicon-o-sparkles class="w-5 h-5 text-indigo-400" />
                    <span class="font-semibold">Religion:</span>
                    <span class="ml-2">{{ $data['religion'] ?? '-' }}</span>
                </div>
                <div class="flex items-center gap-2">
                    <x-heroicon-o-arrow-trending-up class="w-5 h-5 text-primary-400" />
                    <span class="font-semibold">Height:</span>
                    <span class="ml-2">{{ $data['height'] ?? '-' }} cm</span>
                </div>
                <div class="flex items-center gap-2">
                    <x-heroicon-o-arrow-trending-down class="w-5 h-5 text-primary-400" />
                    <span class="font-semibold">Weight:</span>
                    <span class="ml-2">{{ $data['weight'] ?? '-' }} kg</span>
                </div>
                <div class="flex items-center gap-2">
                    <x-heroicon-o-home-modern class="w-5 h-5 text-primary-500" />
                    <span class="font-semibold">Current Address:</span>
                    <span class="ml-2">{{ $data['current_adress'] ?? '-' }}</span>
                </div>
                <div class="flex items-center gap-2">
                    <x-heroicon-o-home class="w-5 h-5 text-primary-500" />
                    <span class="font-semibold">Permanent Address:</span>
                    <span class="ml-2">{{ $data['permanent_address'] ?? '-' }}</span>
                </div>
            </div>
        </div>
        <!-- Academic & Contact Info Card -->
        <div class="bg-white rounded-lg shadow p-6 space-y-6 border border-primary-100">
            <h2 class="text-xl font-bold flex items-center gap-2 text-primary-700 mb-4">
                <x-heroicon-o-academic-cap class="w-6 h-6" /> Academic & Contact Information
            </h2>
            <div class="space-y-3">
                <div class="flex items-center gap-2">
                    <x-heroicon-o-academic-cap class="w-5 h-5 text-primary-500" />
                    <span class="font-semibold">Course:</span>
                    <span class="ml-2">{{ $course->code ?? '-' }} <span class="text-xs text-gray-500">{{ $course->name ?? '' }}</span></span>
                </div>
                <div class="flex items-center gap-2">
                    <x-heroicon-o-calendar class="w-5 h-5 text-primary-400" />
                    <span class="font-semibold">Academic Year:</span>
                    <span class="ml-2">{{ $data['academic_year'] ?? '-' }}</span>
                </div>
                <div class="flex items-center gap-2">
                    <x-heroicon-o-identification class="w-5 h-5 text-primary-400" />
                    <span class="font-semibold">LRN:</span>
                    <span class="ml-2">{{ $data['lrn'] ?? '-' }}</span>
                </div>
                <div class="flex items-center gap-2">
                    <x-heroicon-o-envelope class="w-5 h-5 text-primary-400" />
                    <span class="font-semibold">Enrollment Email:</span>
                    <span class="ml-2">{{ $data['enrollment_google_email'] ?? $data['email'] ?? '-' }}</span>
                </div>
            </div>
            <div class="mt-6">
                <h3 class="font-semibold text-primary-600 mb-2 flex items-center gap-2">
                    <x-heroicon-o-phone class="w-5 h-5" /> Contacts
                </h3>
                <div class="space-y-2">
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-device-phone-mobile class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Personal Contact:</span>
                        <span class="ml-2">{{ $data['personal_contact'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-globe-alt class="w-5 h-5 text-blue-400" />
                        <span class="font-semibold">Facebook:</span>
                        <span class="ml-2">{{ $data['facebook_contact'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-user class="w-5 h-5 text-green-400" />
                        <span class="font-semibold">Emergency Contact Name:</span>
                        <span class="ml-2">{{ $data['emergency_contact_name'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-phone-arrow-up-right class="w-5 h-5 text-red-400" />
                        <span class="font-semibold">Emergency Contact Phone:</span>
                        <span class="ml-2">{{ $data['emergency_contact_phone'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-map class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Emergency Contact Address:</span>
                        <span class="ml-2">{{ $data['emergency_contact_address'] ?? '-' }}</span>
                    </div>
                </div>
            </div>
            <div class="mt-6">
                <h3 class="font-semibold text-primary-600 mb-2 flex items-center gap-2">
                    <x-heroicon-o-users class="w-5 h-5" /> Family & Guardian
                </h3>
                <div class="space-y-2">
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-user class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Father's Name:</span>
                        <span class="ml-2">{{ $data['fathers_name'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-user class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Mother's Name:</span>
                        <span class="ml-2">{{ $data['mothers_name'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-user class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Guardian Name:</span>
                        <span class="ml-2">{{ $data['guardianName'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-hand-thumb-up class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Guardian Relationship:</span>
                        <span class="ml-2">{{ $data['guardianRelationship'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-phone class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Guardian Contact:</span>
                        <span class="ml-2">{{ $data['guardianContact'] ?? '-' }}</span>
                    </div>
                </div>
            </div>
            <div class="mt-6">
                <h3 class="font-semibold text-primary-600 mb-2 flex items-center gap-2">
                    <x-heroicon-o-book-open class="w-5 h-5" /> Education
                </h3>
                <div class="space-y-2">
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-building-library class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Elementary School:</span>
                        <span class="ml-2">{{ $data['elementary_school'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-map class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Elementary Address:</span>
                        <span class="ml-2">{{ $data['elementary_school_address'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-calendar class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Elementary Graduate Year:</span>
                        <span class="ml-2">{{ $data['elementary_graduate_year'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-building-library class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Junior High School:</span>
                        <span class="ml-2">{{ $data['junior_high_school_name'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-map class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Junior High Address:</span>
                        <span class="ml-2">{{ $data['junior_high_school_address'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-calendar class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Junior High Graduation Year:</span>
                        <span class="ml-2">{{ $data['junior_high_graduation_year'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-building-library class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Senior High School:</span>
                        <span class="ml-2">{{ $data['senior_high_name'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-map class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Senior High Address:</span>
                        <span class="ml-2">{{ $data['senior_high_address'] ?? '-' }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-calendar class="w-5 h-5 text-primary-400" />
                        <span class="font-semibold">Senior High Graduate Year:</span>
                        <span class="ml-2">{{ $data['senior_high_graduate_year'] ?? '-' }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="mt-8 flex flex-col md:flex-row gap-4 items-center justify-between">
        <div class="text-lg font-semibold text-primary-700 flex items-center gap-2">
            <x-heroicon-o-clock class="w-5 h-5" />
            Submitted: {{ $record->created_at?->format('M d, Y H:i') ?? '-' }}
        </div>
    </div>
</div> 