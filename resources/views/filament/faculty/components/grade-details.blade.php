<div class="p-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
            <h3 class="text-lg font-medium">Student Information</h3>
            <div class="mt-2">
                <p><span class="font-semibold">Name:</span> {{ $record->student->full_name }}</p>
                <p><span class="font-semibold">ID:</span> {{ $record->student->student_id }}</p>
                <p><span class="font-semibold">Course:</span> {{ $record->student->course->name ?? 'N/A' }}</p>
            </div>
        </div>

        <div>
            <h3 class="text-lg font-medium">Grade Information</h3>
            <div class="mt-2">
                <p><span class="font-semibold">Prelim Grade:</span> {{ number_format($record->prelim_grade, 2) }}%</p>
                <p><span class="font-semibold">Midterm Grade:</span> {{ number_format($record->midterm_grade, 2) }}%</p>
                <p><span class="font-semibold">Finals Grade:</span> {{ number_format($record->finals_grade, 2) }}%</p>
                <p><span class="font-semibold">Total Average:</span>
                    <span class="{{ $record->total_average >= 75 \Pages\ListRecords;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;

class ListGradeApprovals extends ListRecords
{
    protected static string $resource = GradeApprovalResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('bulk_approve')
                ->label('Approve All Pending')
                ->icon('heroicon-o-clipboard-check')
                ->color('success')
                ->requiresConfirmation()
                ->action(function () {
                    $query = ClassEnrollment::query()
                        ->where('is_grades_finalized', true)
                        ->where('is_grades_verified', false);

                    // Apply any current filters from the table
                    if (request()->has('class_id')) {
                        $query->whereHas('class', function ($q) {
                            $q->where('id', request('class_id'));
                        });
                    }

                    $query->update([
                        'is_grades_verified' => true,
                        'verified_by' => auth()->id(),
                        'verified_at' => now(),
                    ]);

                    $this->notify('success', 'All pending grades have been approved.');
                    $this->refreshTable();
                }),
        ];
    }

    protected function getTableFilters(): array
    {
        return [
            ...parent::getTableFilters(),

            SelectFilter::make('class_id')
                ->label('Class')
                ->relationship('class', 'id', function (Builder $query) {
                    return $query->with('subject')->get()->mapWithKeys(function ($class) {
                        return [$class->id => "{$class->subject->code} - Section {$class->section}"];
                    });
                })
                ->searchable()
                ->preload(),
        ];
    }
}
