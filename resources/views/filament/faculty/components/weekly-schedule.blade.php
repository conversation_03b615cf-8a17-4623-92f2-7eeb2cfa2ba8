

        <div class="overflow-x-auto rounded-lg">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-800">
                        @foreach ($days as $day)
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                            {{ $day }}
                        </th>
                        @endforeach
                    </tr>
                </thead>

                <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-700 dark:divide-gray-600">
                    <tr>
                        @foreach ($days as $day)
                        <td class="px-4 py-3 whitespace-nowrap">
                            @if(isset($schedule[strtolower($day)]) && count($schedule[strtolower($day)]) > 0)
                                @foreach($schedule[strtolower($day)] as $slot)
                                    <x-filament::card class="mb-2 p-2.5 bg-primary-50 border border-primary-200 shadow-sm dark:bg-primary-950/50 dark:border-primary-800">
                                        <div class="font-medium text-primary-700 dark:text-primary-400">
                                            {{ $slot['time_range'] ?? ($slot['start_time'] . ' - ' . $slot['end_time']) }}
                                        </div>
                                        @if($showRoomDetails && isset($slot['room']))
                                            <div class="flex items-center mt-1 text-primary-600 text-xs dark:text-primary-500">
                                                <x-filament::icon
                                                    name="heroicon-o-building-office"
                                                    class="h-3.5 w-3.5 mr-1"
                                                />
                                                {{ $slot['room']['name'] }}
                                            </div>
                                        @endif
                                    </x-filament::card>
                                @endforeach
                            @else
                                <div class="text-gray-400 text-sm py-2 italic dark:text-gray-500">
                                    No schedule
                                </div>
                            @endif
                        </td>
                        @endforeach
                    </tr>
                </tbody>
            </table>
        </div>

        @if(isset($class) && $class->schedules->isEmpty())
            <x-filament::section.heading class="text-center py-4 text-gray-500 dark:text-gray-400">
                No schedule has been assigned to this class yet.
            </x-filament::section.heading>
        @endif
