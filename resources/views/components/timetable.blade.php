@php
use Carbon\Carbon;
$weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
$startTime = Carbon::createFromTime(7, 0);
$endTime = Carbon::createFromTime(18, 0);
$timeslots = [];

while ($startTime->lt($endTime)) {
$timeslots[] = ['start' => $startTime->copy(), 'end' => $startTime->copy()->addHour()];
$startTime->addHour();
}
@endphp

<div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden p-4">
    <div class="px-6 py-4">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">Class Timetable</h2>
    </div>
    <table class="min-w-full bg-white dark:bg-gray-800 rounded-lg ">
        <thead>
            <tr>
                <th class="px-4 py-2"></th>
                <th class="px-4 py-2">Monday</th>
                <th class="px-4 py-2">Tuesday</th>
                <th class="px-4 py-2">Wednesday</th>
                <th class="px-4 py-2">Thursday</th>
                <th class="px-4 py-2">Friday</th>
                <th class="px-4 py-2">Saturday</th>

            </tr>
        </thead>
        <tbody class="">
            @foreach ($timeslots as $timeslot)
            <tr>
                <td class="border rounded-md border-gray-200 dark:border-gray-700 px-4 py-2 bg-gray-100 dark:bg-gray-900 font-semibold">
                    {{ $timeslot['start']->format('g:i a') }} - {{ $timeslot['end']->format('g:i a') }}
                </td>
                @foreach ($weekdays as $day)
                <td class="border border-gray-200 dark:border-gray-700 px-4 py-2 bg-gray-100 dark:bg-gray-900">
                    @foreach ($schedules as $schedule)
                    @if (strtolower($schedule->day_of_week) === $day && Carbon::parse($schedule->start_time)->format('g:i a') === $timeslot['start']->format('g:i a'))
                    <p class="text-gray-900 dark:text-white bg-primary-700/50 border border-primary-400 p-2 rounded-lg font-semibold">{{ $code }} - {{ $schedule->room->name ?? ''}}</p>
                    @endif
                    @endforeach
                </td>
                @endforeach
            </tr>
            @endforeach
        </tbody>
    </table>
</div>
