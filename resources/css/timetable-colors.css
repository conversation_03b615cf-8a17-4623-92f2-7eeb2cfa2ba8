/* Timetable Color Coding System with Enhanced Dark/Light Mode Support */

/* CSS Variables for theming */
:root {
    --timetable-bg-light: #ffffff;
    --timetable-bg-dark: #1f2937;
    --timetable-border-light: #e5e7eb;
    --timetable-border-dark: #374151;
    --timetable-text-light: #374151;
    --timetable-text-dark: #f9fafb;
    --timetable-header-light: #f9fafb;
    --timetable-header-dark: #111827;
    --timetable-shadow-light: rgba(0, 0, 0, 0.1);
    --timetable-shadow-dark: rgba(0, 0, 0, 0.3);
}

/* Dark mode toggle button */
.dark-mode-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.dark .dark-mode-toggle {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

/* Enhanced timetable container */
.timetable-container {
    background: var(--timetable-bg-light);
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px var(--timetable-shadow-light);
    transition: all 0.3s ease;
}

.dark .timetable-container {
    background: var(--timetable-bg-dark);
    box-shadow: 0 4px 6px -1px var(--timetable-shadow-dark);
}

/* Base schedule entry styles */
.schedule-entry {
    position: absolute;
    left: 4px;
    right: 4px;
    border-radius: 8px;
    padding: 8px;
    font-size: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    transition: all 0.2s ease-in-out;
    z-index: 10;
}

/* College courses - Blue theme */
.schedule-college {
    background-color: #dbeafe;
    border-color: #bfdbfe;
    color: #1e40af;
}

.schedule-college:hover {
    background-color: #bfdbfe;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.schedule-college .schedule-title {
    color: #1e40af;
    font-weight: 500;
}

.schedule-college .schedule-details {
    color: #2563eb;
}

/* Dark mode for college */
@media (prefers-color-scheme: dark) {
    .schedule-college {
        background-color: rgba(30, 64, 175, 0.2);
        border-color: rgba(59, 130, 246, 0.3);
        color: #93c5fd;
    }

    .schedule-college:hover {
        background-color: rgba(30, 64, 175, 0.3);
    }

    .schedule-college .schedule-title {
        color: #93c5fd;
    }

    .schedule-college .schedule-details {
        color: #bfdbfe;
    }
}

/* SHS courses - Green theme */
.schedule-shs {
    background-color: #dcfce7;
    border-color: #bbf7d0;
    color: #166534;
}

.schedule-shs:hover {
    background-color: #bbf7d0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.schedule-shs .schedule-title {
    color: #166534;
    font-weight: 500;
}

.schedule-shs .schedule-details {
    color: #15803d;
}

/* Dark mode for SHS */
@media (prefers-color-scheme: dark) {
    .schedule-shs {
        background-color: rgba(22, 101, 52, 0.2);
        border-color: rgba(34, 197, 94, 0.3);
        color: #86efac;
    }

    .schedule-shs:hover {
        background-color: rgba(22, 101, 52, 0.3);
    }

    .schedule-shs .schedule-title {
        color: #86efac;
    }

    .schedule-shs .schedule-details {
        color: #bbf7d0;
    }
}

/* Default/Unknown classification - Primary theme */
.schedule-default {
    background-color: #f3f4f6;
    border-color: #d1d5db;
    color: #374151;
}

.schedule-default:hover {
    background-color: #e5e7eb;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.schedule-default .schedule-title {
    color: #374151;
    font-weight: 500;
}

.schedule-default .schedule-details {
    color: #6b7280;
}

/* Dark mode for default */
@media (prefers-color-scheme: dark) {
    .schedule-default {
        background-color: rgba(55, 65, 81, 0.2);
        border-color: rgba(107, 114, 128, 0.3);
        color: #d1d5db;
    }

    .schedule-default:hover {
        background-color: rgba(55, 65, 81, 0.3);
    }

    .schedule-default .schedule-title {
        color: #d1d5db;
    }

    .schedule-default .schedule-details {
        color: #9ca3af;
    }
}

/* Conflict highlighting - Red theme */
.schedule-conflict {
    background-color: #fef2f2;
    border-color: #fca5a5;
    border-width: 2px;
    color: #dc2626;
    animation: pulse-conflict 2s infinite;
}

.schedule-conflict:hover {
    background-color: #fee2e2;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.schedule-conflict .schedule-title {
    color: #dc2626;
    font-weight: 500;
}

.schedule-conflict .schedule-details {
    color: #ef4444;
}

/* Dark mode for conflicts */
@media (prefers-color-scheme: dark) {
    .schedule-conflict {
        background-color: rgba(220, 38, 38, 0.2);
        border-color: rgba(248, 113, 113, 0.4);
        color: #fca5a5;
    }

    .schedule-conflict:hover {
        background-color: rgba(220, 38, 38, 0.3);
    }

    .schedule-conflict .schedule-title {
        color: #fca5a5;
    }

    .schedule-conflict .schedule-details {
        color: #fecaca;
    }
}

/* Conflict pulse animation */
@keyframes pulse-conflict {
    0%, 100% {
        border-color: #fca5a5;
    }
    50% {
        border-color: #f87171;
    }
}

@media (prefers-color-scheme: dark) {
    @keyframes pulse-conflict {
        0%, 100% {
            border-color: rgba(248, 113, 113, 0.4);
        }
        50% {
            border-color: rgba(248, 113, 113, 0.6);
        }
    }
}

/* Faculty-specific colors for faculty view */
.schedule-faculty-1 {
    @apply bg-purple-50 dark:bg-purple-950/40 border-purple-200 dark:border-purple-800;
}

.schedule-faculty-1:hover {
    @apply bg-purple-100 dark:bg-purple-900/60;
}

.schedule-faculty-1 .schedule-title {
    @apply text-purple-700 dark:text-purple-300;
}

.schedule-faculty-1 .schedule-details {
    @apply text-purple-600 dark:text-purple-200;
}

.schedule-faculty-2 {
    @apply bg-indigo-50 dark:bg-indigo-950/40 border-indigo-200 dark:border-indigo-800;
}

.schedule-faculty-2:hover {
    @apply bg-indigo-100 dark:bg-indigo-900/60;
}

.schedule-faculty-2 .schedule-title {
    @apply text-indigo-700 dark:text-indigo-300;
}

.schedule-faculty-2 .schedule-details {
    @apply text-indigo-600 dark:text-indigo-200;
}

.schedule-faculty-3 {
    @apply bg-pink-50 dark:bg-pink-950/40 border-pink-200 dark:border-pink-800;
}

.schedule-faculty-3:hover {
    @apply bg-pink-100 dark:bg-pink-900/60;
}

.schedule-faculty-3 .schedule-title {
    @apply text-pink-700 dark:text-pink-300;
}

.schedule-faculty-3 .schedule-details {
    @apply text-pink-600 dark:text-pink-200;
}

/* Room-specific colors for room view */
.schedule-room-lab {
    @apply bg-orange-50 dark:bg-orange-950/40 border-orange-200 dark:border-orange-800;
}

.schedule-room-lab:hover {
    @apply bg-orange-100 dark:bg-orange-900/60;
}

.schedule-room-lab .schedule-title {
    @apply text-orange-700 dark:text-orange-300;
}

.schedule-room-lab .schedule-details {
    @apply text-orange-600 dark:text-orange-200;
}

.schedule-room-lecture {
    @apply bg-cyan-50 dark:bg-cyan-950/40 border-cyan-200 dark:border-cyan-800;
}

.schedule-room-lecture:hover {
    @apply bg-cyan-100 dark:bg-cyan-900/60;
}

.schedule-room-lecture .schedule-title {
    @apply text-cyan-700 dark:text-cyan-300;
}

.schedule-room-lecture .schedule-details {
    @apply text-cyan-600 dark:text-cyan-200;
}

/* Time slot indicators */
.time-slot-morning {
    background: linear-gradient(to right, #fefce8, #fef3c7);
}

.time-slot-afternoon {
    background: linear-gradient(to right, #eff6ff, #dbeafe);
}

.time-slot-evening {
    background: linear-gradient(to right, #faf5ff, #f3e8ff);
}

/* Dark mode time slots */
@media (prefers-color-scheme: dark) {
    .time-slot-morning {
        background: linear-gradient(to right, rgba(254, 252, 232, 0.1), rgba(254, 243, 199, 0.1));
    }

    .time-slot-afternoon {
        background: linear-gradient(to right, rgba(239, 246, 255, 0.1), rgba(219, 234, 254, 0.1));
    }

    .time-slot-evening {
        background: linear-gradient(to right, rgba(250, 245, 255, 0.1), rgba(243, 232, 255, 0.1));
    }
}

/* Conflict indicators */
.conflict-indicator {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 12px;
    height: 12px;
    background-color: #ef4444;
    border-radius: 50%;
    border: 2px solid #ffffff;
    animation: pulse 2s infinite;
}

@media (prefers-color-scheme: dark) {
    .conflict-indicator {
        border-color: #1f2937;
    }
}

.conflict-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
    background-color: #fee2e2;
    color: #991b1b;
}

@media (prefers-color-scheme: dark) {
    .conflict-badge {
        background-color: rgba(153, 27, 27, 0.5);
        color: #fca5a5;
    }
}

/* Accessibility improvements */
.schedule-entry:focus {
    outline: none;
    box-shadow: 0 0 0 2px #3b82f6, 0 0 0 4px rgba(59, 130, 246, 0.2);
}

.schedule-entry[aria-selected="true"] {
    box-shadow: 0 0 0 2px #3b82f6;
}

@media (prefers-color-scheme: dark) {
    .schedule-entry:focus {
        box-shadow: 0 0 0 2px #60a5fa, 0 0 0 4px rgba(96, 165, 250, 0.2);
    }

    .schedule-entry[aria-selected="true"] {
        box-shadow: 0 0 0 2px #60a5fa;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .schedule-entry {
        border-width: 2px;
    }

    .schedule-conflict {
        border-width: 4px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .schedule-entry {
        transition: none;
    }

    .schedule-conflict {
        animation: none;
    }

    .conflict-indicator {
        animation: none;
    }
}

/* Print styles */
@media print {
    .schedule-entry {
        box-shadow: none;
        border: 1px solid #9ca3af;
    }

    .schedule-conflict {
        background-color: #e5e7eb;
        border: 2px solid #6b7280;
    }
}

/* Legend styles */
.color-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px;
    background-color: #f9fafb;
    border-radius: 8px;
}

@media (prefers-color-scheme: dark) {
    .color-legend {
        background-color: #1f2937;
    }
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    border: 1px solid;
}

.legend-label {
    font-size: 14px;
    color: #374151;
}

@media (prefers-color-scheme: dark) {
    .legend-label {
        color: #d1d5db;
    }
}
