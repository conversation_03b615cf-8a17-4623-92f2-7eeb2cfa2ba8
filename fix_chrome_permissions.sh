#!/bin/bash

# This script fixes permissions for Chrome headless PDF generation

# Create necessary directories
mkdir -p /home/<USER>/Projects/DccpAdminV2/storage/app/chrome-data
mkdir -p /home/<USER>/Projects/DccpAdminV2/storage/app/chrome-data-win
mkdir -p /home/<USER>/Projects/DccpAdminV2/storage/app/temp

# Set permissions to be writable by the web server
chmod -R 777 /home/<USER>/Projects/DccpAdminV2/storage/app/chrome-data
chmod -R 777 /home/<USER>/Projects/DccpAdminV2/storage/app/chrome-data-win
chmod -R 777 /home/<USER>/Projects/DccpAdminV2/storage/app/temp

# Change ownership to the web server user (http on Arch Linux)
chown -R http:http /home/<USER>/Projects/DccpAdminV2/storage/app/chrome-data
chown -R http:http /home/<USER>/Projects/DccpAdminV2/storage/app/chrome-data-win
chown -R http:http /home/<USER>/Projects/DccpAdminV2/storage/app/temp

echo "Chrome directories created and permissions set"
