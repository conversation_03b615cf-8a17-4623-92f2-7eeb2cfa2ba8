# Student Section Transfer Testing Guide

This guide provides comprehensive testing scenarios for the new student section transfer functionality using both Artisan commands and Tinker.

## Prerequisites

1. Ensure queue workers are running:
   ```bash
   php artisan queue:work --queue=student-transfers,bulk-student-transfers
   ```

2. Run the database migration for optimized indexes:
   ```bash
   php artisan migrate
   ```

## Testing with Artisan Commands

### 1. List Available Classes
```bash
php artisan test:student-transfer list-classes
```

### 2. List Class Enrollments
```bash
php artisan test:student-transfer list-enrollments --class-id=1
```

### 3. Test Single Student Transfer (Background Job)
```bash
php artisan test:student-transfer test-single --student-id=123 --target-class-id=2
```

### 4. Test Bulk Student Transfer (Background Job)
```bash
php artisan test:student-transfer test-bulk --class-id=1 --target-class-id=2 --limit=5
```

### 5. Test Service Directly (Synchronous)
```bash
php artisan test:student-transfer test-service --student-id=123 --target-class-id=2
```

## Testing with Tinker

### Basic Setup and Data Exploration

```php
// Start tinker
php artisan tinker

// Import required classes
use App\Models\Classes;
use App\Models\ClassEnrollment;
use App\Models\Student;
use App\Services\StudentSectionTransferService;
use App\Jobs\MoveStudentToSectionJob;
use App\Jobs\BulkMoveStudentsToSectionJob;

// Get available classes for the same subject
$classes = Classes::where('subject_code', 'CS101')
    ->where('school_year', '2024-2025')
    ->where('semester', 1)
    ->with(['class_enrollments'])
    ->get();

$classes->each(function($class) {
    echo "Class ID: {$class->id}, Section: {$class->section}, Enrolled: {$class->class_enrollments->count()}\n";
});
```

### Test Service Functionality

```php
// Initialize the service
$transferService = app(StudentSectionTransferService::class);

// Get a student's class enrollment
$classEnrollment = ClassEnrollment::with(['student', 'class'])->first();
echo "Student: {$classEnrollment->student->full_name}\n";
echo "Current Class: {$classEnrollment->class->subject_code} - Section {$classEnrollment->class->section}\n";

// Get available target classes
$availableClasses = $transferService->getAvailableTargetClasses($classEnrollment->class_id);
$availableClasses->each(function($class) {
    echo "Available: Class ID {$class->id}, Section {$class->section}, Slots: {$class->available_slots}/{$class->maximum_slots}\n";
});

// Test transfer validation
$targetClassId = $availableClasses->first()->id;
try {
    $result = $transferService->transferStudent($classEnrollment, $targetClassId);
    echo "✅ Transfer successful!\n";
    print_r($result);
} catch (Exception $e) {
    echo "❌ Transfer failed: {$e->getMessage()}\n";
}
```

### Test Background Jobs

```php
// Test single student transfer job
$classEnrollment = ClassEnrollment::with(['student'])->first();
$targetClassId = 2; // Replace with actual target class ID

MoveStudentToSectionJob::dispatch(
    $classEnrollment->id,
    $targetClassId,
    1 // User ID
);

echo "Single transfer job dispatched for {$classEnrollment->student->full_name}\n";

// Test bulk transfer job
$classEnrollments = ClassEnrollment::where('class_id', 1)->limit(3)->get();
$enrollmentIds = $classEnrollments->pluck('id')->toArray();

BulkMoveStudentsToSectionJob::dispatch(
    $enrollmentIds,
    $targetClassId,
    1 // User ID
);

echo "Bulk transfer job dispatched for " . count($enrollmentIds) . " students\n";
```

### Monitor Job Progress

```php
// Check job status
use Illuminate\Support\Facades\Queue;

// Get failed jobs
$failedJobs = \Illuminate\Support\Facades\DB::table('failed_jobs')->get();
$failedJobs->each(function($job) {
    echo "Failed Job: {$job->queue}, Exception: " . substr($job->exception, 0, 100) . "...\n";
});

// Check notifications
use App\Models\User;
$user = User::find(1);
$notifications = $user->notifications()->latest()->limit(5)->get();
$notifications->each(function($notification) {
    echo "Notification: {$notification->data['title']} - {$notification->created_at}\n";
});
```

### Database Analysis and Optimization Testing

```php
// Analyze query performance
use Illuminate\Support\Facades\DB;

// Enable query logging
DB::enableQueryLog();

// Perform a transfer
$transferService = app(StudentSectionTransferService::class);
$classEnrollment = ClassEnrollment::first();
$targetClassId = 2;

try {
    $result = $transferService->transferStudent($classEnrollment, $targetClassId);
    
    // Check executed queries
    $queries = DB::getQueryLog();
    echo "Executed " . count($queries) . " queries:\n";
    foreach ($queries as $query) {
        echo "- {$query['query']} ({$query['time']}ms)\n";
    }
} catch (Exception $e) {
    echo "Error: {$e->getMessage()}\n";
}

DB::disableQueryLog();
```

### Test Error Scenarios

```php
// Test invalid class transfer
try {
    $classEnrollment = ClassEnrollment::first();
    $result = $transferService->transferStudent($classEnrollment, 99999); // Non-existent class
} catch (Exception $e) {
    echo "Expected error: {$e->getMessage()}\n";
}

// Test full class scenario
$fullClass = Classes::whereHas('class_enrollments', function($query) {
    $query->havingRaw('COUNT(*) >= classes.maximum_slots');
})->first();

if ($fullClass) {
    try {
        $classEnrollment = ClassEnrollment::where('class_id', '!=', $fullClass->id)->first();
        $result = $transferService->transferStudent($classEnrollment, $fullClass->id);
    } catch (Exception $e) {
        echo "Expected full class error: {$e->getMessage()}\n";
    }
}
```

### Performance Testing

```php
// Test bulk transfer performance
$startTime = microtime(true);

$classEnrollments = ClassEnrollment::where('class_id', 1)->limit(10)->get();
$results = $transferService->transferMultipleStudents($classEnrollments, 2);

$endTime = microtime(true);
$executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

echo "Bulk transfer of {$results['total_students']} students:\n";
echo "- Success: {$results['success_count']}\n";
echo "- Errors: {$results['error_count']}\n";
echo "- Execution time: {$executionTime}ms\n";
echo "- Average per student: " . ($executionTime / $results['total_students']) . "ms\n";
```

## Monitoring and Debugging

### Check Queue Status
```bash
# Monitor queue workers
php artisan queue:monitor

# Check failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all
```

### Database Queries
```sql
-- Check recent transfers
SELECT ce.*, s.first_name, s.last_name, c.subject_code, c.section 
FROM class_enrollments ce
JOIN students s ON ce.student_id = s.id
JOIN classes c ON ce.class_id = c.id
WHERE ce.updated_at > NOW() - INTERVAL 1 HOUR
ORDER BY ce.updated_at DESC;

-- Check subject enrollment updates
SELECT se.*, s.first_name, s.last_name, sub.code as subject_code
FROM subject_enrollments se
JOIN students s ON se.student_id = s.id
JOIN subject sub ON se.subject_id = sub.id
WHERE se.updated_at > NOW() - INTERVAL 1 HOUR
ORDER BY se.updated_at DESC;
```

### Log Files
```bash
# Monitor application logs
tail -f storage/logs/laravel.log | grep -E "(transfer|move|student)"

# Monitor queue logs
tail -f storage/logs/laravel.log | grep -E "(job|queue)"
```

## Expected Results

1. **Single Transfer**: Student should be moved from one section to another with both class_enrollments and subject_enrollments updated
2. **Bulk Transfer**: Multiple students should be processed with progress notifications
3. **Notifications**: Users should receive real-time notifications about transfer status
4. **Error Handling**: Invalid transfers should be caught and logged appropriately
5. **Performance**: Transfers should complete efficiently with optimized database queries

## Troubleshooting

1. **Jobs not processing**: Ensure queue workers are running
2. **Database errors**: Check foreign key constraints and data integrity
3. **Notification issues**: Verify user permissions and notification settings
4. **Performance issues**: Check database indexes and query optimization
