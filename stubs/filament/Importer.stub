<?php

namespace {{ namespace }};

use App\Models\{{ model }};
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;

class {{ importerClass }} extends Importer
{
    protected static ?string $model = {{ modelClass }}::class;

    public static function getColumns(): array
    {
        return [
{{ columns }}
        ];
    }

    public function resolveRecord(): ?{{ modelClass }}
    {
        // return {{ modelClass }}::firstOrNew([
        //     // Update existing records, matching them by `$this->data['column_name']`
        //     'email' => $this->data['email'],
        // ]);

        return new {{ modelClass }}();
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your {{ modelLabel }} import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}
