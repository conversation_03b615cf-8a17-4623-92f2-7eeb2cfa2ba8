<?php

declare(strict_types=1);

use App\Models\GeneralSetting;
use App\Models\StudentEnrollment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use HeadlessChromium\BrowserFactory;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Filament\Notifications\Notification;
use Vormkracht10\FilamentMails\Facades\FilamentMails;

FilamentMails::routes();

Route::middleware(['web'])->group(function (): void {
    //Login named route
    Route::get('/login', function () {
        return redirect()->to('/admin/login');
    })->name('login');
    // WebAuthn routes - uncomment when webauthn package is properly configured
    // Route::webauthn();
});

Route::get('/', function () {
    return redirect()->to('/admin');
});

// Export download route
Route::get('/export/download/{exportJob}', function (\App\Models\ExportJob $exportJob) {
    // Check if user can access this export
    $user = auth()->user();
    if ($user && ($user->id === $exportJob->user_id || $user->hasRole('admin'))) {
        if ($exportJob->status === 'completed' && $exportJob->file_content) {
            $headers = [
                'Content-Type' => $exportJob->format === 'pdf' ? 'text/html' : 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $exportJob->file_name . '"',
            ];

            return response($exportJob->file_content, 200, $headers);
        }
    }

    abort(404);
})->name('export.download');

// Health check route for Docker container
Route::get('/health', function () {
    try {
        // Check database connection
        DB::connection()->getPdo();

        // Check Redis connection
        Illuminate\Support\Facades\Cache::store('redis')->put(
            'health_check',
            'ok',
            10
        );
        $redis_status = Illuminate\Support\Facades\Cache::store('redis')->get(
            'health_check'
        );

        return response()->json(
            [
                'status' => 'healthy',
                'timestamp' => now()->toISOString(),
                'services' => [
                    'database' => 'connected',
                    'redis' => $redis_status === 'ok' ? 'connected' : 'disconnected',
                ],
            ],
            200
        );
    } catch (Exception $e) {
        return response()->json(
            [
                'status' => 'unhealthy',
                'timestamp' => now()->toISOString(),
                'error' => $e->getMessage(),
            ],
            503
        );
    }
});

// Octane-specific health check route
Route::get('/octane-health', function () {
    try {
        $octaneInfo = [
            'server' => config('octane.server'),
            'is_octane' => app()->bound('octane'),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
        ];

        // Add FrankenPHP specific info if available
        if (config('octane.server') === 'frankenphp') {
            $octaneInfo['frankenphp_binary'] = env(
                'FRANKENPHP_BIN',
                '/usr/local/bin/frankenphp'
            );
            $octaneInfo['frankenphp_available'] = file_exists(
                env('FRANKENPHP_BIN', '/usr/local/bin/frankenphp')
            );
        }

        return response()->json(
            [
                'status' => 'healthy',
                'timestamp' => now()->toISOString(),
                'octane' => $octaneInfo,
                'performance' => [
                    'memory_usage_mb' => round(
                        memory_get_usage(true) / 1024 / 1024,
                        2
                    ),
                    'memory_peak_mb' => round(
                        memory_get_peak_usage(true) / 1024 / 1024,
                        2
                    ),
                    'uptime' => app()->hasBeenBootstrapped()
                        ? 'running'
                        : 'starting',
                ],
            ],
            200
        );
    } catch (Exception $e) {
        return response()->json(
            [
                'status' => 'unhealthy',
                'timestamp' => now()->toISOString(),
                'error' => $e->getMessage(),
                'trace' => config('app.debug')
                    ? $e->getTraceAsString()
                    : 'Debug mode disabled',
            ],
            503
        );
    }
});
Route::get('assessment/download/{record}', function ($record) {
    $student = StudentEnrollment::withTrashed()->where('id', $record)->first();
    if (! $student) {
        Notification::make('ERROR')
            ->title('ERROR')
            ->body('The requested student enrollment record does not exist.')
            ->danger()
            ->send();

        return redirect()->back();
    }

    $resource = $student
        ->resources()
        ->where('type', 'assessment')
        ->latest()
        ->first();

    if (! $resource) {
        Notification::make('ERROR')
            ->title('ERROR')
            ->body('The requested Assessment does not exist.')
            ->danger()
            ->send();

        return redirect()->back();
    }

    try {
        $fileUrl = Storage::disk('public')->url($resource->file_name);

        return redirect()->away($fileUrl);
    } catch (Exception $e) {
        Notification::make('ERROR')
            ->title('ERROR')
            ->body('Error accessing the file: '.$e->getMessage())
            ->danger()
            ->send();

        return redirect()->back();
    }
})->name('assessment.download');
Route::get('pdf/assesment-form/{record}', function ($record) {
    $general_settings = GeneralSetting::first();
    $student = StudentEnrollment::withTrashed()->where('id', $record)->first();
    if (! $student) {
        return redirect()->back()->with('error', 'Student not found');
    }
    $data = [
        'student' => $student,
        'subjects' => $student->SubjectsEnrolled,
        'school_year' => $general_settings->getSchoolYearString(),
        'semester' => $general_settings->getSemester(),
        'logo' => asset('storage/'.$general_settings->school_portal_logo),
        'tuition' => $student->studentTuition,
    ];

    return view('pdf.assesment-form', $data);
});

Route::get('view-resource/{resource}', function ($resourceId) {
    $resource = App\Models\Resource::findOrFail($resourceId);

    // Log the resource details
    Log::info('View resource route accessed', [
        'resource_id' => $resourceId,
        'file_path' => $resource->file_path,
        'disk' => $resource->disk,
        'file_name' => $resource->file_name,
        'file_exists_at_path' => file_exists($resource->file_path),
    ]);

    // First, try to serve the file directly from the file_path
    if (file_exists($resource->file_path)) {
        Log::info('Serving file directly from file_path', [
            'file_path' => $resource->file_path,
        ]);

        return response()->file($resource->file_path, [
            'Content-Type' => $resource->mime_type ?? 'application/pdf',
            'Content-Disposition' => 'inline; filename="'.$resource->file_name.'"',
        ]);
    }

    // Try to get the file from the configured disk using file_name
    try {
        $disk = $resource->disk === 'local' ? 'public' : $resource->disk;

        if (Storage::disk($disk)->exists($resource->file_name)) {
            Log::info('File found on disk, serving via storage', [
                'disk' => $disk,
                'file_name' => $resource->file_name,
            ]);

            return Storage::disk($disk)->response($resource->file_name, $resource->file_name, [
                'Content-Type' => $resource->mime_type ?? 'application/pdf',
            ]);
        }
    } catch (Exception $e) {
        Log::error('Error accessing file on disk', [
            'error' => $e->getMessage(),
            'disk' => $resource->disk,
            'file_name' => $resource->file_name,
        ]);
    }

    // Try alternative paths in storage/app/public
    $alternativePaths = [
        'assessments/'.$resource->file_name,
        'pdfs/'.$resource->file_name,
        $resource->file_name,
    ];

    foreach ($alternativePaths as $path) {
        $fullPath = storage_path('app/public/'.$path);
        if (file_exists($fullPath)) {
            Log::info('Found file at alternative path', [
                'path' => $fullPath,
            ]);

            return response()->file($fullPath, [
                'Content-Type' => $resource->mime_type ?? 'application/pdf',
                'Content-Disposition' => 'inline; filename="'.$resource->file_name.'"',
            ]);
        }
    }

    Log::error('Resource file not found anywhere', [
        'resource_id' => $resourceId,
        'file_path' => $resource->file_path,
        'file_name' => $resource->file_name,
        'disk' => $resource->disk,
        'checked_paths' => array_merge([$resource->file_path], $alternativePaths),
    ]);

    // File not found
    abort(404, 'Resource file not found');
})->name('view-resource');

// Test route for Chrome-PHP PDF generation with Tailwind CSS
Route::get('/test-pdf-chrome', function () {
    // Create a simple HTML content with Tailwind CSS
    $html =
        '<!DOCTYPE html>
    <html>
    <head>
        <title>Chrome-PHP PDF Test with Tailwind</title>
        <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio"></script>
        <style>
            @import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");
            * {
                font-family: "Inter", sans-serif;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        </style>
    </head>
    <body class="bg-gray-100 p-8">
        <div class="max-w-3xl mx-auto bg-white p-8 rounded-lg shadow-lg">
            <h1 class="text-3xl font-bold text-blue-600 mb-4">Chrome-PHP PDF Test</h1>
            <p class="text-gray-700 mb-4">This is a test PDF generated with Chrome-PHP and styled with Tailwind CSS.</p>
            <div class="bg-blue-100 border-l-4 border-blue-500 p-4 mb-4">
                <p class="text-blue-700">Generated at: '.
        now().
        '</p>
            </div>
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="bg-green-100 p-4 rounded">
                    <h2 class="text-green-800 font-bold">Feature 1</h2>
                    <p class="text-green-700">Description of feature 1</p>
                </div>
                <div class="bg-purple-100 p-4 rounded">
                    <h2 class="text-purple-800 font-bold">Feature 2</h2>
                    <p class="text-purple-700">Description of feature 2</p>
                </div>
            </div>
            <table class="w-full border-collapse border border-gray-300 mb-4">
                <thead>
                    <tr class="bg-gray-200">
                        <th class="border border-gray-300 p-2">Item</th>
                        <th class="border border-gray-300 p-2">Description</th>
                        <th class="border border-gray-300 p-2">Price</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="border border-gray-300 p-2">Item 1</td>
                        <td class="border border-gray-300 p-2">Description of item 1</td>
                        <td class="border border-gray-300 p-2">$100</td>
                    </tr>
                    <tr class="bg-gray-50">
                        <td class="border border-gray-300 p-2">Item 2</td>
                        <td class="border border-gray-300 p-2">Description of item 2</td>
                        <td class="border border-gray-300 p-2">$200</td>
                    </tr>
                </tbody>
            </table>
            <div class="flex justify-end">
                <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Download PDF
                </button>
            </div>
        </div>
    </body>
    </html>';

    // Create temp directory if it doesn't exist
    $tempDir = storage_path('app/temp');
    if (! file_exists($tempDir)) {
        mkdir($tempDir, 0755, true);
    }

    // Create a custom temp directory for Chrome with proper permissions
    $chromeUserDataDir = storage_path('app/chrome-data-test');
    if (! file_exists($chromeUserDataDir)) {
        mkdir($chromeUserDataDir, 0755, true);
    }
    chmod($chromeUserDataDir, 0777);

    // Generate unique filename
    $outputPath = storage_path('app/test-chrome-pdf-'.time().'.pdf');

    try {
        // Initialize BrowserFactory and Browser with custom options
        $browserFactory = new BrowserFactory;
        $browser = $browserFactory->createBrowser([
            'userDataDir' => $chromeUserDataDir,
            'ignoreCertificateErrors' => true,
            'noSandbox' => true,
            'keepAlive' => false,
            'sendSyncDefaultTimeout' => 60000,
            'windowSize' => [1920, 1080],
            'enableImages' => true,
            'debugLogger' => true,
            'args' => [
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-extensions',
                '--disable-software-rasterizer',
                '--disable-default-apps',
                '--disable-setuid-sandbox',
                '--no-first-run',
                '--no-sandbox',
                '--no-zygote',
                '--single-process',
                '--homedir='.$chromeUserDataDir,
                '--crash-dumps-dir='.$chromeUserDataDir,
                '--user-data-dir='.$chromeUserDataDir,
                '--allow-running-insecure-content',
                '--allow-file-access-from-files',
                '--disable-web-security',
            ],
        ]);

        // Create a page and set the HTML content
        $page = $browser->createPage();
        $page->setHtml($html);

        // Give time for Tailwind to process
        sleep(2);

        // Generate PDF
        $page
            ->pdf([
                'printBackground' => true,
                'marginTop' => 10,
                'marginBottom' => 10,
                'marginLeft' => 10,
                'marginRight' => 10,
                'format' => 'A4',
                'displayHeaderFooter' => false,
                'preferCSSPageSize' => true,
                'scale' => 1.0,
                'viewportWidth' => 1920,
                'viewportHeight' => 1080,
            ])
            ->saveToFile($outputPath);

        // Close the browser
        $browser->close();

        // Return the PDF file
        return response()->file($outputPath)->deleteFileAfterSend(true);
    } catch (Exception $e) {
        return response()->json(
            [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ],
            500
        );
    }
});

Route::get('/transactions/{transaction_number}', function (
    $transaction_number
) {
    return view('transactions.view', [
        'transaction_number' => $transaction_number,
    ]);
});

// Add a print route for transactions
Route::get('/transactions/{transaction_number}/print', function (
    $transaction_number
) {
    return view('transactions.print', [
        'transaction_number' => $transaction_number,
    ]);
})->name('transactions.print');

// Job progress API endpoint
Route::get('/api/job-progress/{jobId}', function ($jobId) {
    $progressData = cache()->get("assessment_job_progress:{$jobId}");

    if (! $progressData) {
        return response()->json(
            [
                'success' => false,
                'message' => 'Job not found or expired',
                'progress' => null,
            ],
            404
        );
    }

    return response()->json([
        'success' => true,
        'progress' => $progressData,
    ]);
})->name('api.job.progress');
