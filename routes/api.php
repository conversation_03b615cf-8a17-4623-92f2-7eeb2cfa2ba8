<?php

declare(strict_types=1);

use App\Http\Controllers\Api\ClassesApiController;
use App\Http\Controllers\Api\GeneralSettingsApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Public API routes
Route::prefix('v1')->middleware('api')->group(function (): void {
    // General settings
    Route::get('/settings', [GeneralSettingsApiController::class, 'index']);

    // Classes and academic information
    Route::get('/classes', [ClassesApiController::class, 'index']);
    Route::get('/classes/{id}', [ClassesApiController::class, 'show']);
    Route::get('/schedules', [ClassesApiController::class, 'schedules']);
    Route::get('/students', [ClassesApiController::class, 'students']);
    Route::get('/course-schedules', [ClassesApiController::class, 'courseSchedules']);
});
