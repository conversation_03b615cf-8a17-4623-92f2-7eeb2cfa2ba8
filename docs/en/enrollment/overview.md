---
title: Enrollment System Overview
icon: heroicon-o-academic-cap
order: 1
group: Enrollment
---

# Enrollment System Overview

The ==DCCP Admin enrollment system== is designed to handle the complete student enrollment lifecycle, from initial application to class assignment and tuition calculation.

> 💡 **Quick Start:** New to the system? Start with the [Student Enrollment Process](enrollment.process) guide for step-by-step instructions.

## Key Features

| Feature | Description | Benefits |
|---------|-------------|----------|
| **Student Management** | Complete student profiles and academic tracking | Centralized student information |
| **Tuition Calculation** | Real-time fee computation with discounts | Accurate and instant calculations |
| **Subject Management** | Course enrollment with prerequisites | Organized academic planning |
| **Real-time Updates** | Live form calculations and validation | Immediate feedback and error prevention |

### 🎓 Comprehensive Student Management
- ==Student registration== and profile management
- ==Academic history== tracking across semesters
- Course and program assignment
- Support for ==multiple student types== (College, SHS)

### 💰 Automated Tuition Calculation
- ==Real-time fee calculation== based on enrolled subjects
- ==Automatic discount application== (0% to 100% in 5% increments)
- ==Special pricing== for NSTP (50% discount) and modular courses (₱2,400 fixed)
- Payment tracking and balance management

### 📚 Subject and Class Management
- Subject enrollment with ==lecture and laboratory== components
- Class scheduling and ==capacity management==
- ==Prerequisite checking== and validation
- ==Conflict detection== for overlapping schedules

### 📊 Real-time Updates
- ==Live form calculations== as you type
- ==Instant fee updates== when subjects change
- ==Automatic balance recalculation== when payments change
- ==Dynamic form validation== with immediate feedback

## Enrollment Workflow

> 📋 **Process Overview:** The enrollment system follows a ==4-step workflow== that ensures accurate and complete student registration.

| Step | Action | Key Points |
|------|--------|------------|
| **1** | Student Selection | Verify student information and academic standing |
| **2** | Academic Period | Set semester, school year, and academic level |
| **3** | Subject Selection | Choose courses with appropriate options |
| **4** | Fee Calculation | Review totals and set payment amounts |

### 1. Student Selection
**Select Student → Verify Information → Set Academic Period**

The enrollment process begins by ==selecting an existing student== or creating a new student record. The system automatically populates:

| Information Type | Details |
|------------------|---------|
| **Personal** | Full name and contact details |
| **Academic** | Current course and academic year |
| **Communication** | Email address and phone |
| **Status** | Academic standing and eligibility |

### 2. Academic Period Setup
**Set Semester → Set School Year → Configure Settings**

Each enrollment is tied to a ==specific academic period==:

| Setting | Options | Example |
|---------|---------|---------|
| **Semester** | 1st Semester, 2nd Semester, Summer | 1st Semester |
| **School Year** | Academic year format | 2023-2024 |
| **Academic Level** | Based on student's current year | 2nd Year |

### 3. Subject Selection
**Browse Subjects → Select Courses → Configure Options**

Students can enroll in ==multiple subjects== with various options:

| Subject Type | Pricing | Special Notes |
|--------------|---------|---------------|
| **Regular Subjects** | Standard rates | Lecture + Laboratory |
| **NSTP Subjects** | ==50% discount== on lecture fees | Automatic discount |
| **Modular Subjects** | ==₱2,400 fixed== fee | No additional lab fees |
| **Laboratory Only** | Lab rates only | No lecture component |

### 4. Fee Calculation
**Calculate Base Fees → Apply Discounts → Add Miscellaneous → Set Payment**

The system ==automatically calculates== all fees:

| Calculation Step | Description | Applied To |
|------------------|-------------|------------|
| **Base Tuition** | Subject units × course rates | All subjects |
| **Discounts** | Percentage reduction | ==Lecture fees only== |
| **Miscellaneous** | Course-specific fees | All enrollments |
| **Payment Plan** | Down payment + balance | Final amount |

## Student Types

> 🎓 **Important:** The system supports ==two distinct student types== with different academic structures and fee calculations.

| Student Type | Academic Structure | Fee Calculation | Special Features |
|--------------|-------------------|-----------------|------------------|
| **College** | Full degree programs | Standard rates | Prerequisite enforcement |
| **SHS** | Track-based curriculum | Modified structure | Strand-specific requirements |

### College Students
- ==Full degree programs== (BSIT, BSBA, etc.)
- ==Standard tuition calculation== using course rates
- ==Complete academic tracking== across all years
- ==Prerequisite enforcement== for advanced subjects

### Senior High School (SHS) Students
- ==Specialized curriculum== based on tracks and strands
- ==Modified fee structure== for SHS programs
- ==Track-based subject selection== (STEM, ABM, etc.)
- ==Strand-specific requirements== and scheduling

## Fee Structure Components

> 💰 **Fee Calculation:** Understanding how fees are calculated is essential for accurate enrollment processing.

### Base Rates

| Component | Calculation | Example |
|-----------|-------------|---------|
| **Lecture Fee** | Total units × course lecture rate | 3 units × ₱500 = ₱1,500 |
| **Laboratory Fee** | Lab units × course lab rate | 1 unit × ₱800 = ₱800 |
| **Miscellaneous** | Fixed course fees | ₱3,500 |

### Special Pricing

| Type | Pricing Rule | Example |
|------|--------------|---------|
| **NSTP Discount** | ==50% off== lecture fees | ₱500 → ₱250 |
| **Modular Courses** | ==₱2,400 fixed== fee | Regardless of units |
| **Laboratory Only** | ==No lecture fees== | Lab rates only |

### Discount System

> ⚠️ **Important:** Discounts apply ==only to lecture fees==. Laboratory and miscellaneous fees are never discounted.

| Discount Range | Increments | Applied To |
|----------------|------------|------------|
| **0% to 100%** | 5% steps | ==Lecture fees only== |
| **Laboratory** | Never discounted | Full rate always |
| **Miscellaneous** | Never discounted | Full rate always |

## Payment Management

### Down Payment
- Minimum payment: ₱500
- Default amount: ₱3,500
- Customizable per enrollment

### Balance Calculation
- Automatic calculation: Total fees - Down payment
- Real-time updates when fees change
- Payment status tracking

## Data Validation

### Required Information
- Student selection
- Academic period (semester and school year)
- At least one subject enrollment
- Valid down payment amount

### Automatic Checks
- Subject availability verification
- Prerequisite validation
- Schedule conflict detection
- Capacity limit enforcement

## Integration Points

### Student Records
- Links to existing student profiles
- Updates academic history
- Tracks enrollment status

### Academic Management
- Connects to class schedules
- Updates subject enrollments
- Manages academic records

### Financial System
- Generates tuition records
- Tracks payment history
- Manages account balances

## Next Steps

Ready to start enrolling students? Check out these guides:

- [Student Enrollment Process](enrollment.process) - Step-by-step enrollment guide
- [Tuition Calculation Details](tuition.calculation) - Understanding fee calculations
- [Subject Management](subjects.overview) - Managing courses and subjects
- [Troubleshooting](troubleshooting.enrollment) - Common enrollment issues

