---
title: Tuition Calculation System
icon: heroicon-o-calculator
order: 1
group: Tuition
---

# Tuition Calculation System

The ==DCCP Admin system== features a sophisticated tuition calculation engine that automatically computes student fees based on enrolled subjects, course rates, and applicable discounts.

> 🧮 **Quick Reference:** For a complete example calculation, jump to the [Detailed Example](#detailed-example) section below.

## How Tuition is Calculated

> 📊 **Process Overview:** Tuition calculation follows a ==4-step process== that ensures accurate fee computation for all student types.

| Step | Process | Key Points |
|------|---------|------------|
| **1** | Base Fee Calculation | Calculate lecture and lab fees per subject |
| **2** | Special Case Handling | Apply NSTP discounts and modular pricing |
| **3** | Discount Application | Apply percentage discounts to lecture fees only |
| **4** | Final Calculation | Add miscellaneous fees and calculate balance |

### Step 1: Base Fee Calculation

For each enrolled subject, the system calculates fees using these formulas:

| Fee Type | Formula | Notes |
|----------|---------|-------|
| **Lecture Fee** | ==Total Units== × Course Lecture Rate | Uses total units (lecture + lab) |
| **Laboratory Fee** | ==Laboratory Units== × Course Lab Rate | Uses lab units only |

> ⚠️ **Critical:** ==Lecture fees use total units==, while ==laboratory fees use lab units only==. This is essential for accurate calculations.

### Step 2: Special Case Handling

#### NSTP Subjects (50% Automatic Discount)

| Condition | Calculation | Example |
|-----------|-------------|---------|
| Subject code contains "NSTP" | Lecture Fee × ==0.5== | ₱500 → ₱250 |
| Laboratory component | Normal calculation | No discount applied |

#### Modular Subjects (Fixed Fee Structure)

| Component | Amount | Notes |
|-----------|--------|-------|
| **Lecture Fee** | ==₱2,400 fixed== | Regardless of units |
| **Laboratory Fee** | ==₱0== | No lab fees for modular |

### Step 3: Discount Application

> ⚠️ **Important:** Discounts apply ==only to lecture fees==. Laboratory and miscellaneous fees are never discounted.

| Component | Discount Applied | Calculation |
|-----------|------------------|-------------|
| **Lecture Fees** | ✅ Yes | Total Lecture × (1 - Discount%) |
| **Laboratory Fees** | ❌ Never | Full amount always |
| **Miscellaneous** | ❌ Never | Full amount always |

### Step 4: Final Calculation

| Calculation Step | Formula | Purpose |
|------------------|---------|---------|
| **Final Tuition** | Discounted Lecture + Lab Fees | Core academic fees |
| **Overall Total** | Final Tuition + Miscellaneous | Complete amount due |
| **Balance** | Overall Total - Down Payment | Remaining amount |

## Detailed Example

> 📝 **Complete Walkthrough:** This example demonstrates the entire calculation process using realistic student data.

### Student Information

| Detail | Value | Notes |
|--------|-------|-------|
| **Course** | BSIT | Bachelor of Science in Information Technology |
| **Lecture Rate** | ₱500 per unit | Course-specific rate |
| **Laboratory Rate** | ₱800 per unit | Higher rate for lab work |
| **Miscellaneous Fee** | ₱3,500 | Standard course fee |
| **Student Discount** | 10% | Applied to lecture fees only |

### Enrolled Subjects

| Subject | Lecture Units | Lab Units | Total Units | Type | Special Notes |
|---------|:-------------:|:---------:|:-----------:|------|---------------|
| Programming 1 | 3 | 1 | 4 | Regular | Standard subject |
| Mathematics | 3 | 0 | 3 | Regular | No lab component |
| NSTP 1 | 1 | 0 | 1 | ==NSTP== | Automatic 50% discount |

### Calculation Process

#### 1. Individual Subject Fees

**Programming 1 (Regular Subject):**

| Component | Calculation | Amount |
|-----------|-------------|--------|
| Lecture Fee | ==4 units== × ₱500 | ₱2,000 |
| Lab Fee | ==1 unit== × ₱800 | ₱800 |
| **Subject Total** | | **₱2,800** |

**Mathematics (Regular Subject):**

| Component | Calculation | Amount |
|-----------|-------------|--------|
| Lecture Fee | ==3 units== × ₱500 | ₱1,500 |
| Lab Fee | ==0 units== × ₱800 | ₱0 |
| **Subject Total** | | **₱1,500** |

**NSTP 1 (50% Automatic Discount):**

| Component | Calculation | Amount |
|-----------|-------------|--------|
| Lecture Fee | ==1 unit== × ₱500 × ==0.5== | ₱250 |
| Lab Fee | ==0 units== × ₱800 | ₱0 |
| **Subject Total** | | **₱250** |

#### 2. Totals Before Student Discount

| Fee Type | Calculation | Amount |
|----------|-------------|--------|
| **Total Lecture Fees** | ₱2,000 + ₱1,500 + ₱250 | ==₱3,750== |
| **Total Laboratory Fees** | ₱800 + ₱0 + ₱0 | ==₱800== |

#### 3. Apply 10% Student Discount

> ⚠️ **Remember:** Student discounts apply ==only to lecture fees==.

| Component | Calculation | Amount |
|-----------|-------------|--------|
| **Discounted Lecture** | ₱3,750 × (1 - 10/100) | ==₱3,375== |
| **Laboratory (No Discount)** | ₱800 (unchanged) | ==₱800== |
| **Final Tuition** | ₱3,375 + ₱800 | **₱4,175** |

#### 4. Add Miscellaneous and Calculate Balance

| Component | Amount | Notes |
|-----------|--------|-------|
| **Final Tuition** | ₱4,175 | Academic fees after discount |
| **Miscellaneous Fees** | ₱3,500 | Never discounted |
| **Overall Total** | ==₱7,675== | Complete amount due |
| **Down Payment** | ₱3,500 | Initial payment |
| **Remaining Balance** | ==₱4,175== | Amount still owed |

## Fee Structure Details

### Course-Based Rates

Each course defines its own fee structure:

| Component | Description | Example |
|-----------|-------------|---------|
| `lec_per_unit` | Cost per lecture unit | ₱500 |
| `lab_per_unit` | Cost per laboratory unit | ₱800 |
| `miscelaneous` | Fixed miscellaneous fees | ₱3,500 |

### Default Values

When course rates are not set:
- Lecture rate: ₱0 per unit
- Laboratory rate: ₱0 per unit
- Miscellaneous: ₱3,500 (system default)

## Discount System

### Available Discounts
- Range: 0% to 100%
- Increments: 5% (0%, 5%, 10%, 15%, ..., 100%)
- Application: Lecture fees only

### Discount Rules
1. **Lecture Fees**: Fully discountable
2. **Laboratory Fees**: Never discounted
3. **Miscellaneous Fees**: Never discounted
4. **NSTP Subjects**: Discount applies to already-reduced lecture fees

## Manual Override System

Administrators can manually override calculated amounts:

### Override Options
- Individual subject lecture fees
- Individual subject laboratory fees
- Total lecture amount
- Total laboratory amount
- Discount percentage
- Miscellaneous fees
- Down payment amount

### Override Behavior
- Manual changes set `is_manually_modified` flag
- System preserves manual overrides during recalculations
- Discounts still apply to manually set amounts
- Balance automatically recalculates

## Real-Time Calculation

### Form Interactions

The system provides instant updates when:

1. **Adding/Removing Subjects**
   - Recalculates all totals
   - Updates fee breakdown
   - Adjusts balance

2. **Changing Modular Status**
   - Switches between calculated and fixed fees
   - Updates subject totals
   - Recalculates overall total

3. **Adjusting Discount**
   - Applies new discount to lecture fees
   - Preserves laboratory fees
   - Updates final amounts

4. **Modifying Down Payment**
   - Recalculates remaining balance
   - Validates minimum payment
   - Updates payment status

### Validation Rules

- **Down Payment**: Minimum ₱500
- **Discount**: 0-100% range
- **Subject Fees**: Must be numeric
- **Required Fields**: Student, subjects, academic info

## Database Storage

### StudentTuition Table

The calculated amounts are stored in these fields:

| Field | Description |
|-------|-------------|
| `total_tuition` | Final tuition after discounts |
| `total_lectures` | Lecture fees after discount |
| `total_laboratory` | Laboratory fees (no discount) |
| `total_miscelaneous_fees` | Miscellaneous fees |
| `discount` | Discount percentage applied |
| `downpayment` | Initial payment amount |
| `overall_tuition` | Total including miscellaneous |
| `total_balance` | Remaining balance |

## Troubleshooting

### Common Issues

**Incorrect Calculations**
- Check course fee rates are set correctly
- Verify subject unit counts
- Confirm discount is applied only to lectures

**Missing Fees**
- Ensure course has defined rates
- Check if subject has proper unit assignments
- Verify miscellaneous fee is set

**Override Problems**
- Clear manual modifications if needed
- Recalculate totals using service
- Check for validation errors

## Next Steps

- [Student Enrollment Process](enrollment.process) - How to enroll students
- [Subject Management](subjects.overview) - Managing subjects and units
- [Payment Processing](payments.overview) - Handling student payments
- [Troubleshooting](troubleshooting.tuition) - Common tuition issues

