---
title: Docker Deployment Guide
icon: heroicon-o-cube
order: 2
group: Installation
---

# Docker Deployment Guide

Complete guide for deploying DCCP Admin V2 using Docker with ==production-ready configurations== and best practices.

> 🐳 **Docker Advantage:** Consistent deployment across all environments with automatic dependency management.

## Quick Start Commands

### 🚀 One-Command Production Deployment

```bash
# Complete production setup
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/docker-deploy.sh | bash
```

### ⚡ Development Setup

```bash
# Clone and start development environment
git clone https://github.com/yukazakiri/DccpAdminV2.git
cd DccpAdminV2
docker-compose up -d
```

## Docker Configurations

### Production Configuration

| Service | Image | Purpose | Resources |
|---------|-------|---------|-----------|
| **App** | `ghcr.io/yukazakiri/dccpadminv2:latest` | Laravel application | 512MB RAM |
| **Database** | `postgres:15-alpine` | PostgreSQL database | 256MB RAM |
| **Cache** | `redis:7-alpine` | Redis cache/sessions | 128MB RAM |
| **Nginx** | `nginx:alpine` | Web server | 64MB RAM |

### Development Configuration

| Service | Configuration | Purpose |
|---------|---------------|---------|
| **App** | Volume mounted source | Live code editing |
| **Database** | Persistent volume | Data persistence |
| **Cache** | Memory only | Fast development |
| **Hot Reload** | Vite dev server | Asset compilation |

## Environment Configurations

### Production Environment (.env.production)

```bash
# Application
APP_NAME="DCCP Admin V2"
APP_ENV=production
APP_DEBUG=false
APP_KEY=base64:your-generated-key-here
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=pgsql
DB_HOST=db
DB_PORT=5432
DB_DATABASE=dccpadminv2
DB_USERNAME=dccp_user
DB_PASSWORD=your-secure-password

# Cache & Sessions
CACHE_STORE=redis
SESSION_DRIVER=redis
REDIS_HOST=redis
REDIS_PORT=6379

# Queue
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database

# Mail
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-smtp-user
MAIL_PASSWORD=your-smtp-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Security
SESSION_SECURE_COOKIE=true
SANCTUM_STATEFUL_DOMAINS=your-domain.com
```

### Development Environment (.env.local)

```bash
# Application
APP_NAME="DCCP Admin V2 (Dev)"
APP_ENV=local
APP_DEBUG=true
APP_KEY=base64:your-generated-key-here
APP_URL=http://localhost:8000

# Database
DB_CONNECTION=pgsql
DB_HOST=db
DB_PORT=5432
DB_DATABASE=dccpadminv2_dev
DB_USERNAME=dccp_user
DB_PASSWORD=dev-password

# Cache & Sessions
CACHE_STORE=array
SESSION_DRIVER=file
REDIS_HOST=redis

# Queue
QUEUE_CONNECTION=sync

# Mail
MAIL_MAILER=log

# Development
VITE_DEV_SERVER_HOST=0.0.0.0
VITE_DEV_SERVER_PORT=5173
```

## Docker Compose Configurations

### Production (docker-compose.prod.yml)

```yaml
version: '3.8'

services:
  app:
    image: ghcr.io/yukazakiri/dccpadminv2:latest
    container_name: dccp-admin-app
    restart: unless-stopped
    environment:
      - DB_HOST=db
      - REDIS_HOST=redis
    volumes:
      - storage_data:/app/storage
      - ./logs:/app/storage/logs
    networks:
      - dccp-network
    depends_on:
      - db
      - redis

  nginx:
    image: nginx:alpine
    container_name: dccp-admin-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - storage_data:/app/storage:ro
    networks:
      - dccp-network
    depends_on:
      - app

  db:
    image: postgres:15-alpine
    container_name: dccp-admin-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_DATABASE}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - dccp-network

  redis:
    image: redis:7-alpine
    container_name: dccp-admin-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - dccp-network

volumes:
  db_data:
  redis_data:
  storage_data:

networks:
  dccp-network:
    driver: bridge
```

### Development (docker-compose.dev.yml)

```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: dccp-admin-app-dev
    restart: unless-stopped
    ports:
      - "8000:8000"
      - "5173:5173"  # Vite dev server
    environment:
      - DB_HOST=db
      - REDIS_HOST=redis
    volumes:
      - .:/app
      - /app/vendor
      - /app/node_modules
    networks:
      - dccp-network
    depends_on:
      - db
      - redis

  db:
    image: postgres:15-alpine
    container_name: dccp-admin-db-dev
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: ${DB_DATABASE}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - db_data_dev:/var/lib/postgresql/data
    networks:
      - dccp-network

  redis:
    image: redis:7-alpine
    container_name: dccp-admin-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - dccp-network

volumes:
  db_data_dev:

networks:
  dccp-network:
    driver: bridge
```

## Deployment Strategies

### 🏢 Production Deployment

#### 1. Single Server Deployment

```bash
# Clone repository
git clone https://github.com/yukazakiri/DccpAdminV2.git
cd DccpAdminV2

# Setup production environment
cp .env.example .env.production
# Edit .env.production with your settings

# Deploy with production compose
docker-compose -f docker-compose.prod.yml up -d

# Run initial setup
docker-compose -f docker-compose.prod.yml exec app php artisan migrate --force
docker-compose -f docker-compose.prod.yml exec app php artisan db:seed --force
```

#### 2. Load Balanced Deployment

```yaml
# docker-compose.lb.yml
version: '3.8'

services:
  nginx-lb:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/load-balancer.conf:/etc/nginx/nginx.conf
    depends_on:
      - app1
      - app2

  app1:
    image: ghcr.io/yukazakiri/dccpadminv2:latest
    environment:
      - DB_HOST=db
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis

  app2:
    image: ghcr.io/yukazakiri/dccpadminv2:latest
    environment:
      - DB_HOST=db
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
```

### 🔄 CI/CD Integration

#### GitHub Actions Deployment

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /path/to/DccpAdminV2
            git pull origin main
            docker-compose -f docker-compose.prod.yml pull
            docker-compose -f docker-compose.prod.yml up -d
            docker-compose -f docker-compose.prod.yml exec -T app php artisan migrate --force
```

## Management Commands

### Container Management

| Command | Purpose | Usage |
|---------|---------|-------|
| **Start Services** | Start all containers | `docker-compose up -d` |
| **Stop Services** | Stop all containers | `docker-compose down` |
| **Restart Services** | Restart containers | `docker-compose restart` |
| **View Logs** | Check application logs | `docker-compose logs -f app` |
| **Shell Access** | Access container shell | `docker-compose exec app bash` |

### Application Management

| Command | Purpose | Usage |
|---------|---------|-------|
| **Run Migrations** | Update database schema | `docker-compose exec app php artisan migrate` |
| **Clear Cache** | Clear application cache | `docker-compose exec app php artisan cache:clear` |
| **Queue Worker** | Start queue processing | `docker-compose exec app php artisan queue:work` |
| **Create User** | Create admin user | `docker-compose exec app php artisan make:filament-user` |

### Database Management

```bash
# Backup database
docker-compose exec db pg_dump -U dccp_user dccpadminv2 > backup.sql

# Restore database
docker-compose exec -T db psql -U dccp_user dccpadminv2 < backup.sql

# Access database shell
docker-compose exec db psql -U dccp_user dccpadminv2
```

## Monitoring and Logging

### Health Checks

```yaml
# Add to docker-compose.yml
services:
  app:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

### Log Management

```bash
# View real-time logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f app

# Export logs
docker-compose logs --no-color > application.log
```

### Resource Monitoring

```bash
# Monitor resource usage
docker stats

# View container processes
docker-compose top

# Inspect container details
docker inspect dccp-admin-app
```

## Security Best Practices

### Container Security

| Practice | Implementation | Benefit |
|----------|----------------|---------|
| **Non-root User** | Run as `appuser:appgroup` | Reduced attack surface |
| **Read-only Filesystem** | Mount volumes as read-only | Prevent tampering |
| **Resource Limits** | Set memory/CPU limits | Prevent resource exhaustion |
| **Network Isolation** | Use custom networks | Isolate services |

### Environment Security

```bash
# Use secrets for sensitive data
echo "your-secret-password" | docker secret create db_password -

# Limit container capabilities
docker run --cap-drop=ALL --cap-add=NET_BIND_SERVICE app
```

## Troubleshooting

### Common Issues

| Issue | Cause | Solution |
|-------|-------|---------|
| **Container won't start** | Port conflict | Change port mapping |
| **Database connection failed** | Wrong credentials | Check `.env` file |
| **Permission denied** | File permissions | Run `chmod -R 755 storage` |
| **Out of memory** | Insufficient resources | Increase Docker memory |

### Debug Commands

```bash
# Check container status
docker-compose ps

# Inspect container logs
docker-compose logs app

# Test database connection
docker-compose exec app php artisan migrate:status

# Check environment variables
docker-compose exec app env | grep APP_
```

---

*Continue to [Production Deployment](../deployment/production) for advanced deployment strategies.*
