---
title: Installation Guide
icon: heroicon-o-cog-6-tooth
order: 1
group: Installation
---

# DCCP Admin V2 - Installation Guide

Simple step-by-step installation guide for DCCP Admin V2.

> 🚀 **Quick Start:** Get up and running in ==5 minutes== with Docker.

## 🐳 Docker Installation (Recommended)

<details>
<summary><strong>📦 One-Command Installation</strong></summary>

**Automatic setup with all dependencies:**

```bash
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/install.sh | bash
```

This script will:
- Install Docker and Docker Compose
- Clone the repository
- Set up environment variables
- Start all services
- Run database migrations
- Create admin user

</details>

<details>
<summary><strong>🔧 Manual Docker Setup</strong></summary>

### Step 1: Clone Repository
```bash
git clone https://github.com/yukazakiri/DccpAdminV2.git
cd DccpAdminV2
```

### Step 2: Setup Environment
```bash
cp .env.example .env
```

### Step 3: Generate Application Key
```bash
docker run --rm -v $(pwd):/app php:8.4-cli php artisan key:generate --show
```
Copy the generated key and update your `.env` file.

### Step 4: Start Services
```bash
docker-compose up -d
```

### Step 5: Run Migrations
```bash
docker-compose exec app php artisan migrate --seed
```

### Step 6: Create Admin User
```bash
docker-compose exec app php artisan make:filament-user
```

</details>

## 💻 Local Development Installation

<details>
<summary><strong>🛠️ Prerequisites</strong></summary>

Make sure you have installed:
- PHP 8.4+
- Composer
- Node.js 18+
- PostgreSQL 13+

</details>

<details>
<summary><strong>📋 Step-by-Step Setup</strong></summary>

### Step 1: Clone Repository
```bash
git clone https://github.com/yukazakiri/DccpAdminV2.git
cd DccpAdminV2
```

### Step 2: Install PHP Dependencies
```bash
composer install
```

### Step 3: Install Node Dependencies
```bash
npm install
```

### Step 4: Setup Environment
```bash
cp .env.example .env
php artisan key:generate
```

### Step 5: Configure Database
Edit `.env` file with your database credentials:
```bash
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=dccpadminv2
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### Step 6: Run Migrations
```bash
php artisan migrate --seed
```

### Step 7: Build Assets
```bash
npm run build
```

### Step 8: Start Development Server
```bash
composer run dev
```

### Step 9: Create Admin User
```bash
php artisan make:filament-user
```

</details>

## 🖥️ Platform-Specific Installation

<details>
<summary><strong>🪟 Windows Installation</strong></summary>

### Option 1: Automated Script
```powershell
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/install-windows.ps1" -OutFile "install.ps1"
PowerShell -ExecutionPolicy Bypass -File install.ps1
```

### Option 2: Manual Setup
```powershell
# Install Chocolatey
Set-ExecutionPolicy Bypass -Scope Process -Force
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install Docker Desktop
choco install docker-desktop -y

# Install Git
choco install git -y

# Restart and continue with Docker setup above
```

</details>

<details>
<summary><strong>🍎 macOS Installation</strong></summary>

### Option 1: Automated Script
```bash
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/install.sh | bash
```

### Option 2: Manual Setup
```bash
# Install Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Docker Desktop
brew install --cask docker

# Install Git
brew install git

# Start Docker Desktop
open /Applications/Docker.app

# Continue with Docker setup above
```

</details>

<details>
<summary><strong>🐧 Linux Installation</strong></summary>

### Ubuntu/Debian
```bash
# Install Docker
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER

# Install Git
sudo apt update
sudo apt install -y git

# Logout and login, then continue with Docker setup
```

### CentOS/RHEL/Fedora
```bash
# Install Docker
sudo dnf install -y docker docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER

# Install Git
sudo dnf install -y git

# Logout and login, then continue with Docker setup
```

</details>

## ⚙️ Configuration

<details>
<summary><strong>🔧 Environment Variables</strong></summary>

Edit your `.env` file with these essential settings:

```bash
APP_NAME="DCCP Admin V2"
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost:8000

DB_CONNECTION=pgsql
DB_HOST=db
DB_PORT=5432
DB_DATABASE=dccpadminv2
DB_USERNAME=dccp_user
DB_PASSWORD=your_secure_password

CACHE_STORE=redis
SESSION_DRIVER=redis
REDIS_HOST=redis
```

</details>

<details>
<summary><strong>📁 File Permissions</strong></summary>

Set proper file permissions:

```bash
# For Docker
docker-compose exec app chown -R www-data:www-data storage bootstrap/cache

# For local development
chmod -R 775 storage bootstrap/cache
```

</details>

## ✅ Verification

<details>
<summary><strong>🔍 Quick Health Check</strong></summary>

Run these commands to verify your installation:

```bash
# Check application status
docker-compose exec app php artisan about

# Test database connection
docker-compose exec app php artisan migrate:status

# Access the application
curl http://localhost:8000
```

</details>

## 🌐 Access Your Application

After installation, access these URLs:

| Service | URL |
|---------|-----|
| **Admin Panel** | http://localhost:8000/admin |
| **Knowledge Base** | http://localhost:8000/kb |
| **API Documentation** | http://localhost:8000/docs |

## 🆘 Quick Troubleshooting

<details>
<summary><strong>🐛 Common Issues</strong></summary>

**Container won't start:**
```bash
docker-compose down
docker-compose up -d
```

**Permission errors:**
```bash
chmod -R 775 storage bootstrap/cache
```

**Database connection failed:**
```bash
# Check your .env file database settings
cat .env | grep DB_
```

**Port already in use:**
```bash
# Change port in docker-compose.yml
ports:
  - "8080:8000"  # Changed from 8000:8000
```

</details>

## 🎯 Next Steps

1. **Login to Admin Panel** - Use the credentials you created
2. **Configure System** - Set up courses and subjects
3. **Add Users** - Create staff accounts
4. **Import Data** - Upload student records
5. **Test Enrollment** - Try the enrollment process

---

*Continue to [Docker Guide](docker-guide) for advanced Docker configurations or [System Configuration](../configuration/overview) for post-installation setup.*
