---
title: Installation Troubleshooting
icon: heroicon-o-wrench-screwdriver
order: 3
group: Installation
---

# Installation Troubleshooting Guide

Comprehensive troubleshooting guide for ==common installation issues== and their solutions.

> 🔧 **Quick Fix:** Run our automated verification script: `curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/verify-installation.sh | bash`

## Common Installation Issues

### Docker-Related Issues

#### Issue: Docker Not Starting

| Symptom | Cause | Solution |
|---------|-------|----------|
| `docker: command not found` | Docker not installed | Install Docker: `curl -fsSL https://get.docker.com \| sh` |
| `permission denied` | User not in docker group | Add user: `sudo usermod -aG docker $USER` |
| `Cannot connect to Docker daemon` | Docker service not running | Start Docker: `sudo systemctl start docker` |

#### Issue: Docker Compose Errors

```bash
# Error: docker-compose: command not found
# Solution: Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Error: Version in "./docker-compose.yml" is unsupported
# Solution: Update Docker Compose
docker-compose --version  # Check current version
# Update to latest version using above command
```

#### Issue: Container Port Conflicts

| Error Message | Solution |
|---------------|----------|
| `port is already allocated` | Change port in `docker-compose.yml` |
| `bind: address already in use` | Stop conflicting service or use different port |

```yaml
# Change port mapping in docker-compose.yml
services:
  app:
    ports:
      - "8080:8000"  # Changed from 8000:8000
```

### Database Connection Issues

#### PostgreSQL Connection Problems

| Issue | Diagnostic Command | Solution |
|-------|-------------------|----------|
| **Connection refused** | `docker-compose logs db` | Check if PostgreSQL container is running |
| **Authentication failed** | `cat .env \| grep DB_` | Verify database credentials |
| **Database does not exist** | `docker-compose exec db psql -U postgres -l` | Create database manually |

#### Database Creation Script

```bash
# Manual database setup
docker-compose exec db psql -U postgres -c "CREATE DATABASE dccpadminv2;"
docker-compose exec db psql -U postgres -c "CREATE USER dccp_user WITH PASSWORD 'your_password';"
docker-compose exec db psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE dccpadminv2 TO dccp_user;"
```

### Environment Configuration Issues

#### Missing Environment Variables

| Variable | Error Symptom | Solution |
|----------|---------------|----------|
| `APP_KEY` | "No application encryption key" | Run `php artisan key:generate` |
| `DB_PASSWORD` | Database connection failed | Set secure password in `.env` |
| `APP_URL` | Incorrect redirects | Set to your domain/IP |

#### Environment File Template

```bash
# Copy and customize
cp .env.example .env

# Required variables checklist
APP_NAME="DCCP Admin V2"
APP_ENV=production
APP_KEY=base64:your-generated-key
APP_DEBUG=false
APP_URL=http://your-domain.com

DB_CONNECTION=pgsql
DB_HOST=db
DB_PORT=5432
DB_DATABASE=dccpadminv2
DB_USERNAME=dccp_user
DB_PASSWORD=secure_password_here
```

### Permission Issues

#### File Permission Problems

| Issue | Command to Fix | Explanation |
|-------|----------------|-------------|
| **Storage not writable** | `chmod -R 775 storage` | Make storage writable |
| **Bootstrap cache error** | `chmod -R 775 bootstrap/cache` | Fix cache permissions |
| **Log file errors** | `touch storage/logs/laravel.log && chmod 666 storage/logs/laravel.log` | Create log file |

#### Docker Permission Issues

```bash
# Fix Docker permissions (Linux)
sudo chown -R $USER:$USER .
sudo chmod -R 755 .

# Fix container permissions
docker-compose exec app chown -R www-data:www-data storage bootstrap/cache
```

### Network and Connectivity Issues

#### Port Accessibility Problems

| Test Command | Expected Result | Troubleshooting |
|--------------|-----------------|-----------------|
| `curl http://localhost:8000` | HTTP 200/302 | Check if app is running |
| `telnet localhost 8000` | Connection successful | Verify port is open |
| `docker-compose ps` | All services "Up" | Restart failed services |

#### Firewall Configuration

```bash
# Ubuntu/Debian firewall
sudo ufw allow 8000/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# CentOS/RHEL firewall
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

### Memory and Resource Issues

#### Insufficient Memory

| Symptom | Solution |
|---------|----------|
| Container keeps restarting | Increase Docker memory limit |
| "Out of memory" errors | Add swap space or upgrade RAM |
| Slow performance | Optimize Docker resource allocation |

#### Docker Resource Configuration

```yaml
# docker-compose.yml resource limits
services:
  app:
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
```

### SSL/HTTPS Issues

#### Certificate Problems

| Issue | Diagnostic | Solution |
|-------|------------|----------|
| **Self-signed certificate** | Browser warning | Install proper SSL certificate |
| **Certificate expired** | `openssl x509 -in cert.pem -text -noout` | Renew certificate |
| **Mixed content errors** | Browser console | Ensure all resources use HTTPS |

#### Let's Encrypt Setup

```bash
# Install Certbot
sudo apt install certbot

# Generate certificate
sudo certbot certonly --standalone -d your-domain.com

# Update nginx configuration
# Add certificate paths to nginx.conf
```

## Platform-Specific Issues

### Windows Issues

#### WSL2 Configuration

```powershell
# Enable WSL2
dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart

# Set WSL2 as default
wsl --set-default-version 2

# Install Ubuntu
wsl --install -d Ubuntu
```

#### Docker Desktop Issues

| Issue | Solution |
|-------|----------|
| **Docker Desktop won't start** | Restart Windows, check Hyper-V |
| **File sharing errors** | Enable file sharing in Docker settings |
| **Performance issues** | Allocate more resources to Docker |

### macOS Issues

#### Homebrew Problems

```bash
# Fix Homebrew permissions
sudo chown -R $(whoami) /usr/local/share/zsh /usr/local/share/zsh/site-functions

# Update Homebrew
brew update && brew upgrade

# Fix broken installations
brew doctor
```

#### Docker Desktop for Mac

```bash
# Reset Docker Desktop
# Go to Docker Desktop > Troubleshoot > Reset to factory defaults

# Increase memory allocation
# Docker Desktop > Preferences > Resources > Memory: 4GB+
```

### Linux Distribution Issues

#### Ubuntu/Debian Specific

```bash
# Update package lists
sudo apt update && sudo apt upgrade -y

# Install missing dependencies
sudo apt install -y curl wget git software-properties-common

# Fix broken packages
sudo apt --fix-broken install
```

#### CentOS/RHEL Specific

```bash
# Enable EPEL repository
sudo dnf install epel-release -y

# Update system
sudo dnf update -y

# Install development tools
sudo dnf groupinstall "Development Tools" -y
```

## Diagnostic Commands

### System Health Check

```bash
# Complete system verification
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/verify-installation.sh | bash

# Manual checks
docker --version
docker-compose --version
docker-compose ps
docker-compose logs app
```

### Application Health Check

```bash
# Laravel application status
docker-compose exec app php artisan about
docker-compose exec app php artisan migrate:status
docker-compose exec app php artisan config:show

# Database connectivity
docker-compose exec app php artisan tinker --execute="DB::connection()->getPdo();"
```

### Performance Diagnostics

```bash
# Resource usage
docker stats

# Container logs
docker-compose logs -f --tail=100

# Application performance
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8000
```

## Getting Additional Help

### Log Collection

```bash
# Collect all logs for support
mkdir -p support-logs
docker-compose logs > support-logs/docker-compose.log
docker-compose exec app cat storage/logs/laravel.log > support-logs/laravel.log
docker system info > support-logs/docker-info.txt
```

### Support Channels

| Channel | Purpose | Response Time |
|---------|---------|---------------|
| **GitHub Issues** | Bug reports, feature requests | 24-48 hours |
| **Documentation** | Self-service help | Immediate |
| **Community Forum** | General questions | Community-driven |

### Creating Support Tickets

When creating a support ticket, include:

1. **System Information**
   - Operating system and version
   - Docker and Docker Compose versions
   - Hardware specifications

2. **Error Details**
   - Complete error messages
   - Steps to reproduce
   - Expected vs actual behavior

3. **Log Files**
   - Docker Compose logs
   - Application logs
   - System logs (if relevant)

4. **Configuration**
   - Environment file (sanitized)
   - Docker Compose configuration
   - Any custom modifications

---

> 💡 **Pro Tip:** Most installation issues can be resolved by ensuring Docker is properly installed and running, and that all required ports are available.

*Continue to [Configuration Guide](../configuration/overview) after resolving installation issues.*
