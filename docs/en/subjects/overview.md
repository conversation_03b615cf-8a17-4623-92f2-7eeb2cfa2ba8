---
title: Subject and Class Management
icon: heroicon-o-book-open
order: 1
group: Subjects
---

# Subject and Class Management

The ==DCCP Admin system== provides comprehensive tools for managing subjects, classes, and academic schedules. This guide covers everything from creating subjects to managing class enrollments.

> 📚 **Quick Navigation:** Jump to [Class Management](#class-management) for scheduling or [Best Practices](#best-practices) for optimization tips.

## Overview

### Key Components

| Component | Purpose | Examples |
|-----------|---------|----------|
| **Subjects** | Academic courses with unit assignments | Programming 1, Mathematics |
| **Classes** | Specific instances of subjects with schedules | IT101-A, MATH101-B |
| **Enrollments** | Student registrations in classes | Student assignments |
| **Schedules** | Time and location assignments | MWF 8:00-9:00 AM |

### Management Hierarchy

> 🔄 **Flow:** Understanding the relationship between academic components is essential for effective management.

```
Course → Subject → Class → Student Enrollment
```

| Level | Description | Example |
|-------|-------------|---------|
| **Course** | Degree program | BSIT, BSBA |
| **Subject** | Academic course | Programming Fundamentals |
| **Class** | Scheduled instance | IT101-A (MWF 8:00 AM) |
| **Enrollment** | Student registration | Juan enrolled in IT101-A |

## Subject Management

### Creating Subjects

#### Required Information

| Field | Purpose | Format | Example |
|-------|---------|--------|---------|
| **Subject Code** | Unique identifier | Course prefix + number | CS101, MATH101 |
| **Subject Name** | Descriptive title | Clear, concise title | Programming Fundamentals |
| **Course Assignment** | Program eligibility | Course selection | BSIT, BSCS |
| **Unit Distribution** | Academic weight | Lecture + Lab units | 3 + 1 = 4 units |

#### Unit Configuration

> ⚖️ **Important:** Unit configuration directly affects ==tuition calculation== and academic load.

| Unit Type | Purpose | Calculation Impact |
|-----------|---------|-------------------|
| **Lecture Units** | Theoretical instruction | Used for lecture fee calculation |
| **Laboratory Units** | Practical/hands-on work | Used for lab fee calculation |
| **Total Units** | ==Automatically calculated== | Lecture + Laboratory |

#### Special Subject Types

> 🏷️ **Classification:** The system recognizes ==special subject types== with unique pricing rules.

**NSTP Subjects**

| Feature | Value | Impact |
|---------|-------|--------|
| **Identification** | Subject codes containing "NSTP" | Automatic recognition |
| **Discount** | ==50% off== lecture fees | Automatic application |
| **Structure** | Usually 1 lecture, 0 lab units | Standard configuration |

**Modular Subjects**

| Feature | Value | Impact |
|---------|-------|--------|
| **Pricing** | ==₱2,400 fixed== fee | Regardless of units |
| **Configuration** | Marked during enrollment | Manual designation |
| **Use Case** | Special programs | Alternative delivery |

### Subject Code Management

#### Naming Conventions
- **Department Prefix**: 2-4 letter department code
- **Course Number**: 3-digit number indicating level
- **Section Identifier**: Optional letter for multiple sections

Examples:
- `CS101` - Computer Science, introductory level
- `MATH201` - Mathematics, intermediate level
- `NSTP1` - National Service Training Program

#### Duplicate Code Handling
When the same subject code exists across different courses:
- Add extra spaces at the end for disambiguation
- Example: `CS101` vs `CS101 ` (with trailing space)
- This allows the same subject in multiple programs

### Subject Attributes

#### Academic Information
| Field | Description | Example |
|-------|-------------|---------|
| Code | Unique identifier | CS101 |
| Name | Subject title | Introduction to Programming |
| Description | Detailed description | Basic programming concepts |
| Units | Total credit units | 3 |
| Lecture | Lecture units | 2 |
| Laboratory | Lab units | 1 |

#### Administrative Settings
| Field | Description | Options |
|-------|-------------|---------|
| Course | Associated program | BSIT, BSCS, etc. |
| Year Level | Recommended year | 1st, 2nd, 3rd, 4th |
| Semester | When offered | 1st, 2nd, Summer |
| Prerequisites | Required subjects | CS100, MATH101 |

## Class Management

### Creating Classes

Classes are specific instances of subjects with:
- **Schedule**: Day, time, and duration
- **Room Assignment**: Physical or virtual location
- **Faculty Assignment**: Instructor
- **Capacity**: Maximum student enrollment
- **Section**: Class identifier (A, B, C, etc.)

### Class Scheduling

#### Time Slot Configuration
- **Start Time**: Class beginning time
- **End Time**: Class ending time
- **Days**: Which days of the week
- **Duration**: Calculated automatically

#### Room Assignment
- **Physical Rooms**: Classroom, laboratory, etc.
- **Virtual Rooms**: Online class identifiers
- **Capacity Limits**: Maximum occupancy

#### Faculty Assignment
- **Primary Instructor**: Main teacher
- **Co-instructors**: Additional faculty
- **Substitute Teachers**: Backup assignments

### Class Types

#### Regular Classes
- Standard lecture and laboratory sessions
- Fixed schedule throughout semester
- Traditional classroom setup

#### Laboratory Classes
- Hands-on practical sessions
- Specialized equipment requirements
- Smaller class sizes

#### Online Classes
- Virtual delivery
- Flexible scheduling options
- Digital resource requirements

## Enrollment Management

### Student Enrollment Process

#### Enrollment Workflow
1. **Subject Selection**: Choose from available subjects
2. **Class Assignment**: Select specific class sections
3. **Schedule Verification**: Check for conflicts
4. **Capacity Check**: Ensure space availability
5. **Prerequisite Validation**: Verify requirements met

#### Enrollment Restrictions
- **Capacity Limits**: Maximum students per class
- **Prerequisites**: Required completed subjects
- **Schedule Conflicts**: No overlapping classes
- **Academic Standing**: Good standing required

### Class Rosters

#### Student Lists
- **Enrolled Students**: Confirmed registrations
- **Waitlisted Students**: Pending enrollment
- **Dropped Students**: Withdrawn registrations

#### Roster Management
- **Add Students**: Manual enrollment
- **Remove Students**: Drop from class
- **Transfer Students**: Move between sections
- **Waitlist Management**: Handle overflow

## Schedule Management

### Timetable Features

#### Conflict Detection
- **Student Conflicts**: Same student, overlapping times
- **Faculty Conflicts**: Same instructor, multiple classes
- **Room Conflicts**: Same room, overlapping schedules

#### Visual Schedule Display
- **Grid View**: Traditional timetable format
- **List View**: Chronological schedule listing
- **Calendar View**: Monthly/weekly calendar format

#### Color Coding
- **By Course**: Different colors for each program
- **By Faculty**: Instructor-specific colors
- **By Room**: Location-based colors
- **By Conflict**: Highlight scheduling conflicts

### Schedule Optimization

#### Automatic Scheduling
- **Conflict Avoidance**: Prevent overlapping assignments
- **Resource Optimization**: Efficient room utilization
- **Faculty Load Balancing**: Distribute teaching loads

#### Manual Adjustments
- **Time Changes**: Modify class times
- **Room Reassignment**: Change locations
- **Faculty Swaps**: Exchange instructors

## Integration with Enrollment

### Fee Calculation Integration

#### Subject-Based Fees
- **Unit Rates**: Fees calculated from subject units
- **Course Rates**: Different rates per program
- **Special Pricing**: NSTP discounts, modular fees

#### Real-Time Updates
- **Fee Recalculation**: When subjects change
- **Discount Application**: Automatic discount handling
- **Balance Updates**: Payment tracking

### Academic Records

#### Transcript Integration
- **Grade Recording**: Link to grade management
- **Credit Tracking**: Unit completion monitoring
- **GPA Calculation**: Academic performance metrics

#### History Tracking
- **Enrollment History**: Past subject enrollments
- **Grade History**: Academic performance records
- **Progress Tracking**: Degree completion status

## Administrative Tools

### Bulk Operations

#### Mass Enrollment
- **Class Lists**: Enroll multiple students
- **Program Requirements**: Auto-enroll required subjects
- **Schedule Templates**: Apply standard schedules

#### Batch Updates
- **Schedule Changes**: Update multiple classes
- **Room Reassignments**: Bulk location changes
- **Faculty Updates**: Mass instructor changes

### Reporting Features

#### Enrollment Reports
- **Class Rosters**: Student lists per class
- **Enrollment Statistics**: Numbers and trends
- **Capacity Reports**: Room utilization

#### Schedule Reports
- **Faculty Schedules**: Individual instructor timetables
- **Room Schedules**: Location usage reports
- **Conflict Reports**: Scheduling issue identification

## Best Practices

### Subject Setup
1. **Consistent Naming**: Use standard naming conventions
2. **Accurate Units**: Ensure correct unit assignments
3. **Clear Descriptions**: Provide detailed subject information
4. **Proper Prerequisites**: Set appropriate requirements

### Class Scheduling
1. **Avoid Conflicts**: Check for scheduling overlaps
2. **Optimize Resources**: Efficient room and faculty use
3. **Consider Student Needs**: Reasonable schedule distribution
4. **Plan Capacity**: Appropriate class sizes

### Enrollment Management
1. **Monitor Capacity**: Track enrollment limits
2. **Validate Prerequisites**: Ensure requirements met
3. **Handle Conflicts**: Resolve scheduling issues
4. **Maintain Records**: Keep accurate enrollment data

## Troubleshooting

### Common Issues

**Subject Not Available**
- Check if subject is offered this semester
- Verify course assignment
- Confirm academic period settings

**Scheduling Conflicts**
- Review overlapping time slots
- Check faculty availability
- Verify room assignments

**Enrollment Problems**
- Validate student prerequisites
- Check class capacity
- Confirm academic standing

**Fee Calculation Issues**
- Verify subject unit assignments
- Check course fee rates
- Confirm special pricing rules

## Next Steps

- [Student Enrollment Process](enrollment.process) - How to enroll students
- [Tuition Calculation](tuition.calculation) - Understanding fee calculations
- [Timetable Management](schedule.timetable) - Advanced scheduling features
- [Grade Management](grades.overview) - Academic record keeping

