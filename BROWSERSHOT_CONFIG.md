# Browsershot Configuration Guide

This document describes the environment variables and configuration options for Browsershot in this Laravel application.

## Environment Variables

Add these variables to your `.env` file for proper Browsershot configuration:

```bash
# Chrome/Chromium Binary Path
# This will be automatically set by the nixpacks.toml start script
CHROME_PATH=

# Node.js Binary Path
# This will be automatically set by the nixpacks.toml start script  
NODE_BINARY_PATH=

# NPM Binary Path
# This will be automatically set by the nixpacks.toml start script
NPM_BINARY_PATH=

# Browsershot Options
BROWSERSHOT_NO_SANDBOX=true
BROWSERSHOT_DISABLE_WEB_SECURITY=false
BROWSERSHOT_IGNORE_HTTPS_ERRORS=false
BROWSERSHOT_TIMEOUT=60

# Temporary Directory
BROWSERSHOT_TEMP_DIRECTORY=

# PDF Generation Options
BROWSERSHOT_PDF_FORMAT=A4
BROWSERSHOT_PDF_MARGIN_TOP=0
BROWSERSHOT_PDF_MARGIN_BOTTOM=0
BROWSERSHOT_PDF_MARGIN_LEFT=0
BROWSERSHOT_PDF_MARGIN_RIGHT=0
BROWSERSHOT_PDF_PRINT_BACKGROUND=true

# Screenshot Options
BROWSERSHOT_SCREENSHOT_WIDTH=1920
BROWSERSHOT_SCREENSHOT_HEIGHT=1080
BROWSERSHOT_DEVICE_SCALE_FACTOR=1
BROWSERSHOT_FULL_PAGE=false
```

## Usage

### Using the BrowsershotService Helper

The application includes a `BrowsershotService` helper class that automatically configures Browsershot with the correct settings:

```php
use App\Services\BrowsershotService;

// Generate a PDF from HTML
$html = '<h1>Hello World</h1><p>This is a test PDF.</p>';
$outputPath = storage_path('app/my-file.pdf');
BrowsershotService::generatePdf($html, $outputPath);

// Generate a screenshot from URL
$url = 'https://example.com';
$outputPath = storage_path('app/screenshot.png');
BrowsershotService::generateScreenshot($url, $outputPath);

// Get a configured Browsershot instance for custom usage
$browsershot = BrowsershotService::html($html);
$browsershot->format('A4')->save($outputPath);
```

### Direct Browsershot Usage

You can also use Browsershot directly, but you'll need to configure it manually:

```php
use Spatie\Browsershot\Browsershot;

$browsershot = Browsershot::html($html)
    ->setChromePath(config('browsershot.chrome_path'))
    ->setNodeBinary(config('browsershot.node_binary_path'))
    ->setNpmBinary(config('browsershot.npm_binary_path'))
    ->noSandbox()
    ->timeout(60);
```

## Testing

### Test Commands

The application includes two Artisan commands for testing Browsershot:

```bash
# Find Chrome/Chromium paths and show environment info
php artisan find:chrome-path

# Test Browsershot PDF generation
php artisan test:browsershot

# Test with detailed system information
php artisan test:browsershot --detailed
```

### Programmatic Testing

You can also test Browsershot programmatically:

```php
use App\Services\BrowsershotService;

$result = BrowsershotService::test();
if ($result['success']) {
    echo "Browsershot is working correctly!";
} else {
    echo "Browsershot test failed: " . implode(', ', $result['errors']);
}
```

## Troubleshooting

### Common Issues

1. **"Command not found" errors**: Usually indicates that Node.js or Chrome/Chromium is not properly installed or not in the PATH.

2. **Permission errors**: Make sure the Chrome/Chromium binary has execute permissions and the temporary directory is writable.

3. **Timeout errors**: Increase the `BROWSERSHOT_TIMEOUT` value or optimize your HTML/CSS for faster rendering.

4. **Memory issues**: Consider reducing the number of concurrent processes or optimizing your content.

### Debugging Steps

1. Run `php artisan find:chrome-path` to check if all required binaries are found.

2. Run `php artisan test:browsershot --detailed` to see detailed system information and test results.

3. Check the Laravel logs for detailed error messages.

4. Verify that your nixpacks.toml configuration includes all required packages:
   ```toml
   nixPkgs = ["chromium", "nodejs_20", "npm", "python311Packages.supervisor"]
   ```

### Nix/Nixpacks Specific Notes

- The `nixpacks.toml` file automatically detects and sets the correct paths for Chrome and Node.js during container startup.
- Environment variables are passed to Laravel workers through the supervisor configuration.
- The `--no-sandbox` flag is essential in containerized environments.

## Configuration Files

- `config/browsershot.php`: Main configuration file
- `app/Services/BrowsershotService.php`: Helper service class
- `nixpacks.toml`: Container configuration with binary paths
- `app/Console/Commands/FindChromePath.php`: Path detection command
- `app/Console/Commands/TestBrowsershot.php`: Testing command

## Security Considerations

- The `--no-sandbox` flag is necessary in most containerized environments but reduces security isolation.
- Be cautious when processing untrusted HTML content.
- Consider implementing input validation and sanitization for user-provided content.
- Use temporary directories with appropriate permissions.