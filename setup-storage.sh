#!/bin/bash

# DCCP Admin Storage Setup Script
# This script creates the necessary directories for bind mounts

set -e

# Configuration
DEFAULT_STORAGE_PATH="/opt/dccp-admin/storage"
STORAGE_PATH="${STORAGE_PATH:-$DEFAULT_STORAGE_PATH}"

echo "Setting up DCCP Admin storage directories..."
echo "Storage path: $STORAGE_PATH"

# Create storage directories
mkdir -p "$STORAGE_PATH/app"
mkdir -p "$STORAGE_PATH/logs"
mkdir -p "$STORAGE_PATH/framework/cache"
mkdir -p "$STORAGE_PATH/framework/sessions"
mkdir -p "$STORAGE_PATH/framework/views"
mkdir -p "$STORAGE_PATH/app/public"

# Set proper permissions
chmod -R 755 "$STORAGE_PATH"

# Create .gitkeep files to preserve empty directories
touch "$STORAGE_PATH/app/.gitkeep"
touch "$STORAGE_PATH/logs/.gitkeep"
touch "$STORAGE_PATH/framework/cache/.gitkeep"
touch "$STORAGE_PATH/framework/sessions/.gitkeep"
touch "$STORAGE_PATH/framework/views/.gitkeep"
touch "$STORAGE_PATH/app/public/.gitkeep"

echo "✅ Storage directories created successfully!"
echo ""
echo "Created directories:"
echo "  - $STORAGE_PATH/app"
echo "  - $STORAGE_PATH/logs"
echo "  - $STORAGE_PATH/framework/cache"
echo "  - $STORAGE_PATH/framework/sessions"
echo "  - $STORAGE_PATH/framework/views"
echo "  - $STORAGE_PATH/app/public"
echo ""
echo "You can now deploy the DCCP Admin stack with bind mounts."
echo "Set STORAGE_PATH=$STORAGE_PATH in your Portainer environment variables."