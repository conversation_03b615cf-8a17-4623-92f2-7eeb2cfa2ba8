#!/bin/bash

# DCCP Admin V2 - Portainer Deployment Management Script
# This script helps manage deployment and updates in Portainer

set -e

# Configuration
STACK_NAME="${STACK_NAME:-dccp-admin}"
PORTAINER_URL="${PORTAINER_URL:-http://localhost:9000}"
IMAGE_NAME="${IMAGE_NAME:-dccp-admin}"
IMAGE_TAG="${IMAGE_TAG:-latest}"
REGISTRY="${REGISTRY:-}" # e.g., ghcr.io/your-username or docker.io/your-username
GITHUB_USERNAME="${GITHUB_USERNAME:-}" # Used by login-ghcr if not provided via prompt
CR_PAT="${CR_PAT:-}" # GitHub Personal Access Token, used by login-ghcr if not provided via prompt

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        log_error "curl is not installed"
        exit 1
    fi
    
    log_success "Requirements check passed"
}

build_image() {
    log_info "Building Docker image..."
    
    local full_image_name="$IMAGE_NAME:$IMAGE_TAG"
    if [ -n "$REGISTRY" ]; then
        full_image_name="$REGISTRY/$IMAGE_NAME:$IMAGE_TAG"
    fi
    
    docker build --target production -t "$full_image_name" .
    
    if [ $? -eq 0 ]; then
        log_success "Docker image built successfully: $full_image_name"
        echo "$full_image_name" > .last_built_image
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
}

push_image() {
    if [ -z "$REGISTRY" ]; then
        log_warning "No registry specified, skipping push."
        log_info "To push to a registry, set the REGISTRY environment variable."
        log_info "Example for Docker Hub: REGISTRY=\"your-dockerhub-username\""
        log_info "Example for GHCR: REGISTRY=\"ghcr.io/YOUR_GITHUB_USERNAME\""
        log_info "For GHCR, run './portainer-deploy.sh login-ghcr' before pushing."
        return 0
    fi
    
    log_info "Pushing image to registry..."
    
    local full_image_name="$REGISTRY/$IMAGE_NAME:$IMAGE_TAG"
    docker push "$full_image_name"
    
    if [ $? -eq 0 ]; then
        log_success "Image pushed successfully: $full_image_name"
    else
        log_error "Failed to push image"
        exit 1
    fi
}

save_image() {
    local output_file="${1:-dccp-admin-${IMAGE_TAG}.tar.gz}"
    
    log_info "Saving Docker image to file..."
    
    local full_image_name="$IMAGE_NAME:$IMAGE_TAG"
    if [ -n "$REGISTRY" ]; then
        full_image_name="$REGISTRY/$IMAGE_NAME:$IMAGE_TAG"
    fi
    
    docker save "$full_image_name" | gzip > "$output_file"
    
    if [ $? -eq 0 ]; then
        log_success "Image saved to: $output_file"
        log_info "Transfer this file to your Portainer host and load with:"
        log_info "  docker load < $output_file"
    else
        log_error "Failed to save image"
        exit 1
    fi
}

generate_env_file() {
    local env_file="${1:-portainer-stack.env}"
    
    log_info "Generating environment file template..."
    
    if [ -f "$env_file" ]; then
        log_warning "Environment file $env_file already exists"
        read -p "Overwrite? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            return 0
        fi
    fi
    
    cat > "$env_file" << 'EOF'
# Portainer Stack Environment Variables
# Configure these values before deploying your stack

# Application Configuration
APP_NAME=DCCP Admin V2
APP_KEY=
APP_URL=https://your-domain.com
APP_PORT=8000
DOMAIN=your-domain.com

# Database Configuration (External PostgreSQL)
DB_HOST=your-postgres-host
DB_PORT=5432
DB_DATABASE=dccpadminv2
DB_USERNAME=dccp_user
DB_PASSWORD=

# Redis Configuration (External Redis)
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=DCCP Admin

# Logging and Debugging
LOG_LEVEL=warning
RUN_MIGRATIONS=false

# Resource Limits
APP_CPU_LIMIT=2.0
APP_MEMORY_LIMIT=1G
EOF
    
    log_success "Environment file created: $env_file"
    log_warning "Please edit $env_file with your configuration values"
}

validate_env() {
    local env_file="${1:-portainer-stack.env}"
    
    if [ ! -f "$env_file" ]; then
        log_error "Environment file not found: $env_file"
        return 1
    fi
    
    log_info "Validating environment configuration..."
    
    local errors=0
    
    # Check required variables
    local required_vars=(
        "APP_KEY"
        "APP_URL"
        "DB_HOST"
        "DB_DATABASE"
        "DB_USERNAME"
        "DB_PASSWORD"
        "REDIS_HOST"
    )
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=.*[^[:space:]]" "$env_file"; then
            log_error "$var is not set or empty in $env_file"
            errors=$((errors + 1))
        fi
    done
    
    # Check APP_KEY format
    if grep -q "^APP_KEY=base64:" "$env_file"; then
        log_success "APP_KEY format is correct"
    else
        log_warning "APP_KEY should start with 'base64:'"
    fi
    
    if [ $errors -eq 0 ]; then
        log_success "Environment validation passed"
        return 0
    else
        log_error "Environment validation failed with $errors errors"
        return 1
    fi
}

generate_key() {
    log_info "Generating Laravel application key..."
    
    local image_name="$IMAGE_NAME:$IMAGE_TAG"
    if [ -n "$REGISTRY" ]; then
        image_name="$REGISTRY/$IMAGE_NAME:$IMAGE_TAG"
    fi
    
    local app_key=$(docker run --rm "$image_name" php artisan key:generate --show 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$app_key" ]; then
        log_success "Generated APP_KEY: $app_key"
        
        # Update environment file if it exists
        if [ -f "portainer-stack.env" ]; then
            sed -i "s/^APP_KEY=.*/APP_KEY=${app_key}/" portainer-stack.env
            log_success "Updated portainer-stack.env with new APP_KEY"
        fi
        
        echo "$app_key"
    else
        log_error "Failed to generate application key"
        exit 1
    fi
}

create_stack_config() {
    local stack_type="${1:-external}"
    local output_file="portainer-${stack_type}-stack.yml"
    
    if [ -f "$output_file" ]; then
        log_success "Stack configuration already exists: $output_file"
        return 0
    fi
    
    log_info "Creating stack configuration: $output_file"
    
    if [ "$stack_type" = "external" ]; then
        cp "portainer-external-stack.yml" "$output_file" 2>/dev/null || {
            log_error "portainer-external-stack.yml not found"
            exit 1
        }
    else
        cp "portainer-stack.yml" "$output_file" 2>/dev/null || {
            log_error "portainer-stack.yml not found"
            exit 1
        }
    fi
    
    log_success "Stack configuration created: $output_file"
}

show_deployment_instructions() {
    log_info "Portainer Deployment Instructions"
    echo "=================================="
    echo ""
    echo "1. Access your Portainer web interface at: $PORTAINER_URL"
    echo "2. Navigate to 'Stacks' section"
    echo "3. Click 'Add stack'"
    echo "4. Choose deployment method:"
    echo ""
    echo "   Method A - Web Editor:"
    echo "   - Select 'Web editor'"
    echo "   - Name: $STACK_NAME"
    echo "   - Copy contents of portainer-external-stack.yml"
    echo ""
    echo "   Method B - Git Repository:"
    echo "   - Select 'Repository'"
    echo "   - Repository URL: https://github.com/your-org/DccpAdminV2"
    echo "   - Compose path: portainer-external-stack.yml"
    echo ""
    echo "5. Configure environment variables (see portainer-stack.env)"
    echo "6. Deploy the stack"
    echo "7. Access container and run initial setup:"
    echo "   - php artisan migrate --force"
    echo "   - php artisan optimize"
    echo ""
    echo "Health check URL: http://your-domain:8000/health"
}

check_portainer_connectivity() {
    log_info "Checking Portainer connectivity..."
    
    if curl -s "$PORTAINER_URL/api/status" &>/dev/null; then
        log_success "Portainer is accessible at $PORTAINER_URL"
    else
        log_warning "Cannot reach Portainer at $PORTAINER_URL"
        log_info "Make sure Portainer is running and accessible"
    fi
}

full_preparation() {
    log_info "Starting full preparation for Portainer deployment..."
    
    check_requirements
    build_image
    
    if [ -n "$REGISTRY" ]; then
        push_image
    else
        save_image
    fi
    
    generate_env_file
    generate_key
    create_stack_config "external"
    validate_env
    check_portainer_connectivity
    
    log_success "Preparation completed successfully!"
    echo ""
    show_deployment_instructions
}

show_help() {
    echo "DCCP Admin V2 - Portainer Deployment Management"
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  prepare          Full preparation for Portainer deployment"
    echo "  build            Build Docker image"
    echo "  push             Push image to registry"
    echo "  save [file]      Save image to tar.gz file"
    echo "  generate-env     Generate environment file template"
    echo "  generate-key     Generate Laravel application key"
    echo "  validate-env     Validate environment configuration"
    echo "  create-config    Create stack configuration"
    echo "  instructions     Show deployment instructions"
    echo "  login-ghcr       Log in to GitHub Container Registry"
    echo "  help             Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  STACK_NAME       Portainer stack name (default: dccp-admin)"
    echo "  PORTAINER_URL    Portainer URL (default: http://localhost:9000)"
    echo "  IMAGE_NAME       Docker image name (default: dccp-admin)"
    echo "  IMAGE_TAG        Docker image tag (default: latest)"
    echo "  REGISTRY         Docker registry URL (e.g., ghcr.io/your-username or your-dockerhub-username)"
    echo "  GITHUB_USERNAME  GitHub username (for login-ghcr, overrides prompt)"
    echo "  CR_PAT           GitHub Personal Access Token (for login-ghcr, overrides prompt)"
    echo ""
    echo "Examples:"
    echo "  $0 prepare                    # Full preparation"
    echo "  REGISTRY=my-registry.com $0 build push"
    echo "  $0 save dccp-admin.tar.gz     # Save image to file"
    echo "  $0 generate-key               # Generate Laravel key"
    echo "  REGISTRY=ghcr.io/myuser $0 login-ghcr # Log in to GHCR"
    echo "  REGISTRY=ghcr.io/myuser $0 push       # Push to GHCR (after login)"
}

login_ghcr() {
    log_info "Attempting to log in to GitHub Container Registry (ghcr.io)..."

    # Use GITHUB_USERNAME env var if set, otherwise prompt
    local user_to_login="${GITHUB_USERNAME}"
    if [ -z "$user_to_login" ]; then
        read -p "Enter your GitHub Username for ghcr.io: " user_to_login
        if [ -z "$user_to_login" ]; then
            log_error "GitHub Username is required."
            return 1
        fi
    fi

    # Use CR_PAT env var if set, otherwise prompt
    local pat_to_use="${CR_PAT}"
    if [ -z "$pat_to_use" ]; then
        echo "Please enter your GitHub Personal Access Token (PAT) with 'write:packages' scope."
        read -s -p "PAT: " pat_to_use
        echo
        if [ -z "$pat_to_use" ]; then
            log_error "GitHub PAT is required."
            return 1
        fi
    fi

    echo "$pat_to_use" | docker login ghcr.io -u "$user_to_login" --password-stdin
    if [ $? -eq 0 ]; then
        log_success "Successfully logged in to ghcr.io as $user_to_login."
    else
        log_error "Failed to log in to ghcr.io. Check username, PAT, and PAT scopes."
        return 1
    fi
}

# Main script logic
case "${1:-help}" in
    "prepare")
        full_preparation
        ;;
    "build")
        check_requirements
        build_image
        ;;
    "push")
        push_image
        ;;
    "save")
        save_image "$2"
        ;;
    "generate-env")
        generate_env_file "$2"
        ;;
    "generate-key")
        generate_key
        ;;
    "validate-env")
        validate_env "$2"
        ;;
    "create-config")
        create_stack_config "$2"
        ;;
    "instructions")
        show_deployment_instructions
        ;;
    "login-ghcr")
        login_ghcr
        ;;
    "help"|*)
        show_help
        ;;
esac