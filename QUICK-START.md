# DCCP Admin V2 - Quick Start Guide

## 🚀 Quick Deployment (5 minutes)

### Prerequisites
- Docker & Docker Compose installed
- External PostgreSQL database
- External Redis instance (optional but recommended)

### 1. Setup Environment
```bash
# Clone and navigate to project
cd DccpAdminV2

# Copy environment template
cp .env.production .env

# Edit .env with your database and Redis settings
nano .env
```

### 2. Configure Required Variables
Update these in your `.env` file:
```bash
APP_KEY=                           # Will be generated
APP_URL=http://your-domain.com

# External PostgreSQL
DB_HOST=your-postgres-host
DB_DATABASE=dccpadminv2
DB_USERNAME=your_db_user
DB_PASSWORD=your_secure_password

# External Redis
REDIS_HOST=your-redis-host
REDIS_PASSWORD=your_redis_password
```

### 3. Deploy with Script
```bash
# Make scripts executable
chmod +x deploy.sh validate-config.sh monitor.sh

# Validate configuration
./validate-config.sh

# Deploy application
./deploy.sh deploy
```

### 4. Verify Deployment
```bash
# Check status
./deploy.sh status

# View health
curl http://localhost:8000/health

# Monitor application
./monitor.sh
```

## 🐳 Manual Docker Commands

### Build and Start
```bash
# Build production image
docker build --target production -t dccp-admin:production .

# Start with included services
docker-compose -f docker-compose.prod.yml up -d

# Start with external services only
docker-compose -f docker-compose.external.yml up -d
```

### Management
```bash
# View logs
docker-compose -f docker-compose.prod.yml logs -f app

# docker build --no-cache --target production -t dccp-admin:latest Run migrations
docker exec dccp-admin-app php artisan migrate --force

# Access shell
docker exec -it dccp-admin-app sh

# Stop services
docker-compose -f docker-compose.prod.yml down
```

## 🛠️ Using Make Commands

```bash
# Show all available commands
make help

# Full deployment
make deploy

# Deploy with external services
make deploy-external

# Individual commands
make build-prod
make start
make migrate
make optimize
make health
make status
```

## 📊 Monitoring

### Health Checks
```bash
# Basic health check
./monitor.sh health

# Full monitoring
./monitor.sh monitor

# Continuous monitoring
./monitor.sh watch

# Check specific services
./monitor.sh database
./monitor.sh redis
./monitor.sh container
```

### Log Management
```bash
# Application logs
make logs

# Container stats
docker stats dccp-admin-app

# System resources
./monitor.sh resources
```

## 🔧 Configuration Files

| File | Purpose |
|------|---------|
| `Dockerfile` | Multi-stage production build |
| `docker-compose.prod.yml` | Complete setup with services |
| `docker-compose.external.yml` | External services only |
| `.env.production` | Production environment template |
| `docker-entrypoint.sh` | Container startup script |

## 🔌 External Services Setup

### PostgreSQL Requirements
```sql
CREATE DATABASE dccpadminv2;
CREATE USER dccp_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE dccpadminv2 TO dccp_user;
```

### Redis Configuration
- Version 6+ recommended
- Memory: 512MB minimum
- Persistence: Optional but recommended
- Authentication: Optional

## 🌐 Network Access

| Service | Port | Purpose |
|---------|------|---------|
| Laravel App | 8000 | Web application |
| PostgreSQL | 5432 | Database (if included) |
| Redis | 6379 | Cache/Sessions (if included) |

## 🔍 Troubleshooting

### Common Issues

**Application won't start:**
```bash
# Check configuration
./validate-config.sh

# View container logs
docker logs dccp-admin-app

# Check service connectivity
./monitor.sh database
./monitor.sh redis
```

**Database connection failed:**
```bash
# Test connectivity
docker exec dccp-admin-app nc -z postgres-host 5432

# Check credentials
docker exec dccp-admin-app php artisan tinker
>>> DB::connection()->getPdo()
```

**Performance issues:**
```bash
# Check resources
./monitor.sh resources

# Optimize application
make optimize

# Clear caches
make clear-cache
```

## 📁 Important Directories

```
DccpAdminV2/
├── Dockerfile                 # Production build
├── docker-compose.prod.yml    # Complete setup
├── docker-compose.external.yml # External services
├── docker-entrypoint.sh       # Startup script
├── deploy.sh                  # Deployment automation
├── validate-config.sh         # Configuration validation
├── monitor.sh                 # Health monitoring
├── Makefile                   # Command shortcuts
├── .env.production           # Environment template
└── docker/
    └── php/
        └── production.ini    # PHP configuration
```

## 🚨 Production Checklist

- [ ] Environment variables configured
- [ ] APP_KEY generated
- [ ] External services accessible
- [ ] SSL certificates configured (for HTTPS)
- [ ] Firewall rules set
- [ ] Backup procedures established
- [ ] Monitoring configured
- [ ] Health checks verified

## 📞 Support

For detailed documentation: `DOCKER-DEPLOYMENT.md`

Common commands reference:
```bash
./deploy.sh help      # Deployment commands
./monitor.sh help     # Monitoring commands
make help            # Make targets
```

## 🔄 Updates

To update the application:
```bash
git pull origin main
make update
```

This will:
1. Pull latest changes
2. Rebuild Docker image
3. Restart services
4. Run migrations
5. Optimize application

---

**🎉 Your DCCP Admin V2 application is now ready for production!**

Access your application at: http://localhost:8000
