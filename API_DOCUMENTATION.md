# Public API Documentation

This API provides access to classes, schedules, and enrolled students for the current school year. All endpoints are public and don't require authentication.

## Base URL
```
/api/v1
```

## Current School Year Context
All endpoints automatically filter data based on the current school year and semester as determined by the `GeneralSettingsService`. The current school year information is included in all responses under the `school_info` key.

## Endpoints

### 1. General Settings
**GET** `/settings`

Returns general school settings including current school year and semester information.

**Response:**
```json
{
  "data": {
    "school_year": "2025-2026",
    "school_year_string": "2025 - 2026",
    "semester": "1st Semester",
    "school_portal_enabled": true,
    "online_enrollment_enabled": true
  }
}
```

### 2. Classes

#### Get All Classes
**GET** `/classes`

Returns all available classes for the current school year with pagination and filtering options.

**Query Parameters:**
- `course_code` (string): Filter by course code (e.g., "BSIT", "BSCS")
- `academic_year` (integer): Filter by academic year (1, 2, 3, 4)
- `semester` (integer): Filter by semester (1, 2)
- `section` (string): Filter by section name
- `subject_code` (string): Filter by subject code
- `faculty_id` (string): Filter by faculty ID
- `classification` (string): Filter by classification (e.g., "Regular", "shs")
- `grade_level` (string): Filter by grade level (for SHS)
- `room_id` (integer): Filter by room ID
- `with_schedules` (boolean): Include schedule information (default: true)
- `with_students` (boolean): Include enrolled students (default: false)
- `with_faculty` (boolean): Include faculty information (default: true)
- `with_subject` (boolean): Include subject information (default: true)
- `per_page` (integer): Number of results per page (default: 50, max: 100)
- `page` (integer): Page number for pagination

**Example Request:**
```bash
GET /api/v1/classes?classification=shs&grade_level=Grade%2011&per_page=10
```

**Response:**
```json
{
  "data": [
    {
      "id": 372,
      "subject_code": "CORE-ORALCOM",
      "academic_year": null,
      "semester": "1",
      "school_year": "2025 - 2026",
      "section": "C",
      "classification": "shs",
      "maximum_slots": 40,
      "enrolled_count": 0,
      "available_slots": 40,
      "grade_level": "Grade 11",
      "subject": {
        "code": "CORE-ORALCOM",
        "title": "Oral Communication"
      },
      "faculty": {
        "full_name": "John Doe",
        "email": "<EMAIL>"
      },
      "room": {
        "name": "Annex-R203",
        "class_code": "ANNEX-R203"
      },
      "schedules": [
        {
          "day_of_week": "Monday",
          "time_range": "04:00 PM - 05:00 PM",
          "room": {
            "name": "Annex-R203"
          }
        }
      ]
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 10,
    "total": 221,
    "last_page": 23
  },
  "school_info": {
    "current_school_year": "2025 - 2026",
    "current_semester": 1,
    "semester_name": "1st Semester"
  }
}
```

#### Get Specific Class
**GET** `/classes/{id}`

Returns detailed information about a specific class.

**Query Parameters:**
- `with_schedules` (boolean): Include schedule information (default: true)
- `with_students` (boolean): Include enrolled students (default: true)
- `with_faculty` (boolean): Include faculty information (default: true)
- `with_subject` (boolean): Include subject information (default: true)

**Example Request:**
```bash
GET /api/v1/classes/372?with_students=true
```

### 3. Schedules

#### Get All Schedules
**GET** `/schedules`

Returns all schedules for classes in the current school year.

**Query Parameters:**
- `day_of_week` (string): Filter by day (Monday, Tuesday, etc.)
- `room_id` (integer): Filter by room ID
- `class_id` (integer): Filter by class ID
- `start_time` (string): Filter by start time (HH:MM format)
- `end_time` (string): Filter by end time (HH:MM format)
- `per_page` (integer): Number of results per page (default: 50, max: 100)

**Example Request:**
```bash
GET /api/v1/schedules?day_of_week=Monday&start_time=08:00
```

**Response:**
```json
{
  "data": [
    {
      "id": 388,
      "day_of_week": "Monday",
      "start_time": "10:00",
      "end_time": "11:00",
      "time_range": "10:00 AM - 11:00 AM",
      "room": {
        "name": "Room 501",
        "class_code": "room501"
      }
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 50,
    "total": 92
  },
  "school_info": {
    "current_school_year": "2025 - 2026",
    "current_semester": 1,
    "semester_name": "1st Semester"
  }
}
```

### 4. Students

#### Get Enrolled Students
**GET** `/students`

Returns all students enrolled in classes for the current school year.

**Query Parameters:**
- `class_id` (integer): Filter by class ID
- `course_id` (integer): Filter by course ID
- `academic_year` (integer): Filter by academic year
- `status` (string): Filter by student status
- `search` (string): Search by student name or ID
- `per_page` (integer): Number of results per page (default: 50, max: 100)

**Example Request:**
```bash
GET /api/v1/students?course_id=8&academic_year=1
```

**Response:**
```json
{
  "data": [
    {
      "id": 2065722,
      "full_name": "Bazan, Ryza Antipuesto",
      "first_name": "Ryza",
      "last_name": "Bazan",
      "email": "<EMAIL>",
      "gender": "Female",
      "age": 18,
      "course_id": 8,
      "academic_year": 1,
      "status": "Active",
      "course": {
        "code": "BSBA (2024 - 2025) NON-ABM",
        "title": "Bachelor of Science in Business Administration",
        "department": "BA"
      }
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 50,
    "total": 64
  },
  "school_info": {
    "current_school_year": "2025 - 2026",
    "current_semester": 1,
    "semester_name": "1st Semester"
  }
}
```

### 5. Course Schedules (Website Display Format)

#### Get Course Schedules for Website Display
**GET** `/course-schedules`

Returns schedules for specific courses formatted exactly for website display with proper day abbreviations and time formatting. Perfect for displaying class schedules on your school website.

**Query Parameters:**
- `course_code` (string, required): The exact course code (e.g., "BSIT (2024 - 2025) WEB", "BSBA (2018 - 2019) NON-ABM")
- `academic_year` (integer): Filter by academic year (1, 2, 3, 4)
- `semester` (integer): Filter by semester (1, 2). Defaults to current semester
- `school_year` (string): Filter by school year (e.g., "2024 - 2025"). Defaults to current school year
- `per_page` (integer): Number of results per page (default: 100, max: 500)

**Example Requests:**
```bash
# Get BSIT 2024-2025 Web Development 1st year schedules
GET /api/v1/course-schedules?course_code=BSIT%20(2024%20-%202025)%20WEB&academic_year=1

# Get BSBA 2018-2019 Non-ABM 2nd year schedules
GET /api/v1/course-schedules?course_code=BSBA%20(2018%20-%202019)%20NON-ABM&academic_year=2
```

**Response:**
```json
{
  "data": [
    {
      "code": "GE-1",
      "title": "Understanding the Self",
      "schedule": "08:00 AM - 10:00 AM MWF",
      "room": "Room 501",
      "section": "A",
      "faculty": "John Doe",
      "units": 3,
      "academic_year": "1",
      "semester": "1",
      "classification": "college",
      "maximum_slots": 40,
      "raw_schedules": [
        {
          "day_of_week": "Monday",
          "start_time": "08:00",
          "end_time": "10:00",
          "formatted_time_range": "08:00 AM - 10:00 AM",
          "room": "Room 501"
        },
        {
          "day_of_week": "Wednesday",
          "start_time": "08:00",
          "end_time": "10:00",
          "formatted_time_range": "08:00 AM - 10:00 AM",
          "room": "Room 501"
        },
        {
          "day_of_week": "Friday",
          "start_time": "08:00",
          "end_time": "10:00",
          "formatted_time_range": "08:00 AM - 10:00 AM",
          "room": "Room 501"
        }
      ]
    },
    {
      "code": "ITW 101",
      "title": "Introduction to Computing",
      "schedule": "11:00 AM - 12:30 PM TTH",
      "room": "Computer Lab 1",
      "section": "A",
      "faculty": "Jane Smith",
      "units": 3,
      "academic_year": "1",
      "semester": "1",
      "classification": "college",
      "maximum_slots": 35,
      "raw_schedules": [
        {
          "day_of_week": "Tuesday",
          "start_time": "11:00",
          "end_time": "12:30",
          "formatted_time_range": "11:00 AM - 12:30 PM",
          "room": "Computer Lab 1"
        },
        {
          "day_of_week": "Thursday",
          "start_time": "11:00",
          "end_time": "12:30",
          "formatted_time_range": "11:00 AM - 12:30 PM",
          "room": "Computer Lab 1"
        }
      ]
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 100,
    "total": 25,
    "last_page": 1,
    "from": 1,
    "to": 25
  },
  "course_info": {
    "course_code": "BSIT (2024 - 2025) WEB",
    "course_title": "Bachelor of Science in Information Technology (Web Development)",
    "course_department": "IT",
    "academic_year": 1,
    "semester": 1
  },
  "school_info": {
    "current_school_year": "2025 - 2026",
    "current_semester": 1,
    "semester_name": "1st Semester"
  }
}
```

#### Day Abbreviation Format
The `schedule` field uses standard day abbreviations:
- **MWF**: Monday, Wednesday, Friday
- **TTH**: Tuesday, Thursday
- **MW**: Monday, Wednesday
- **TH**: Thursday only
- **S**: Saturday
- **SU**: Sunday

#### Website Display Format
The data is formatted exactly as requested for easy website display:

| Code | Title | Schedule | Room |
|------|-------|----------|------|
| GE-1 | Understanding the Self | 08:00 AM - 10:00 AM MWF | Room 501 |
| ITW 101 | Introduction to Computing | 11:00 AM - 12:30 PM TTH | Computer Lab 1 |

You can easily loop through the `data` array to create this table format in your website.

## Features

### 1. Automatic Current School Year Filtering
All endpoints automatically filter data based on the current school year and semester using the `GeneralSettingsService`.

### 2. Comprehensive Filtering
Each endpoint supports multiple filter parameters to help you find exactly what you need.

### 3. Flexible Data Loading
Use the `with_*` parameters to control which related data is included in responses, optimizing performance.

### 4. Pagination
All list endpoints support pagination with customizable page sizes.

### 5. Caching
Responses are cached for 5 minutes to improve performance.

### 6. RESTful Design
The API follows RESTful conventions for easy integration.

## Error Handling

The API returns standard HTTP status codes:
- `200`: Success
- `404`: Resource not found
- `422`: Validation error
- `500`: Server error

Error responses include a message explaining the issue:
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "per_page": ["The per page may not be greater than 100."]
  }
}
```

## Rate Limiting

The API uses Laravel's default rate limiting. If you exceed the limit, you'll receive a `429 Too Many Requests` response.

## Integration Examples

### Website Course Schedule Display (Recommended)
```javascript
// Fetch course schedules formatted for website display
async function displayCourseSchedule(courseCode, academicYear) {
  try {
    const response = await fetch(`/api/v1/course-schedules?course_code=${encodeURIComponent(courseCode)}&academic_year=${academicYear}`);
    const data = await response.json();

    if (data.data.length === 0) {
      console.log('No schedules found for this course');
      return;
    }

    // Create HTML table
    let html = `
      <h3>${data.course_info.course_title} - Year ${academicYear}</h3>
      <table class="schedule-table">
        <thead>
          <tr>
            <th>Code</th>
            <th>Title</th>
            <th>Schedule</th>
            <th>Room</th>
            <th>Section</th>
            <th>Faculty</th>
            <th>Units</th>
          </tr>
        </thead>
        <tbody>
    `;

    data.data.forEach(subject => {
      html += `
        <tr>
          <td>${subject.code}</td>
          <td>${subject.title}</td>
          <td>${subject.schedule}</td>
          <td>${subject.room}</td>
          <td>${subject.section}</td>
          <td>${subject.faculty}</td>
          <td>${subject.units}</td>
        </tr>
      `;
    });

    html += '</tbody></table>';
    document.getElementById('schedule-container').innerHTML = html;

  } catch (error) {
    console.error('Error fetching course schedule:', error);
  }
}

// Usage examples
displayCourseSchedule('BSIT (2024 - 2025) WEB', 1);
displayCourseSchedule('BSBA (2018 - 2019) NON-ABM', 2);
```

### Multiple Course Comparison
```javascript
// Compare schedules between different course versions
async function compareCourseVersions(baseCourse, years) {
  const promises = years.map(year =>
    fetch(`/api/v1/course-schedules?course_code=${encodeURIComponent(baseCourse + ' (' + year + ')')}&academic_year=1`)
      .then(response => response.json())
  );

  const results = await Promise.all(promises);

  results.forEach((data, index) => {
    console.log(`\n${baseCourse} (${years[index]}):`);
    data.data.forEach(subject => {
      console.log(`  ${subject.code}: ${subject.schedule} - ${subject.room}`);
    });
  });
}

// Compare BSIT curriculum between different years
compareCourseVersions('BSIT', ['2018 - 2019', '2024 - 2025']);
```

### React Component Example
```jsx
import React, { useState, useEffect } from 'react';

const CourseScheduleTable = ({ courseCode, academicYear }) => {
  const [schedules, setSchedules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [courseInfo, setCourseInfo] = useState(null);

  useEffect(() => {
    const fetchSchedules = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `/api/v1/course-schedules?course_code=${encodeURIComponent(courseCode)}&academic_year=${academicYear}`
        );
        const data = await response.json();

        setSchedules(data.data);
        setCourseInfo(data.course_info);
      } catch (error) {
        console.error('Error fetching schedules:', error);
      } finally {
        setLoading(false);
      }
    };

    if (courseCode && academicYear) {
      fetchSchedules();
    }
  }, [courseCode, academicYear]);

  if (loading) return <div>Loading schedules...</div>;

  return (
    <div className="course-schedule">
      <h2>{courseInfo?.course_title} - Year {academicYear}</h2>
      <table className="table table-striped">
        <thead>
          <tr>
            <th>Code</th>
            <th>Title</th>
            <th>Schedule</th>
            <th>Room</th>
            <th>Section</th>
            <th>Faculty</th>
            <th>Units</th>
          </tr>
        </thead>
        <tbody>
          {schedules.map((subject, index) => (
            <tr key={index}>
              <td>{subject.code}</td>
              <td>{subject.title}</td>
              <td>{subject.schedule}</td>
              <td>{subject.room}</td>
              <td>{subject.section}</td>
              <td>{subject.faculty}</td>
              <td>{subject.units}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default CourseScheduleTable;
```

### Website Class Schedule Display (Alternative)
```javascript
// Fetch all classes for display
fetch('/api/v1/classes?with_schedules=true&per_page=100')
  .then(response => response.json())
  .then(data => {
    // Display classes with their schedules
    data.data.forEach(classItem => {
      console.log(`${classItem.subject_code} - ${classItem.section}`);
      classItem.schedules.forEach(schedule => {
        console.log(`  ${schedule.day_of_week}: ${schedule.time_range}`);
      });
    });
  });
```

### Student Enrollment Check
```javascript
// Check if a student is enrolled in a specific class
fetch(`/api/v1/students?class_id=372&search=Ryza`)
  .then(response => response.json())
  .then(data => {
    if (data.data.length > 0) {
      console.log('Student is enrolled in this class');
    }
  });
```

This API provides a comprehensive and easy-to-use interface for accessing school class and enrollment data, perfect for building websites, mobile apps, or integration with other systems.
