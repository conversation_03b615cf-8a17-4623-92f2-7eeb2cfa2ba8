# Course Schedule API Examples

This document provides practical examples for using the `/api/v1/course-schedules` endpoint to display course schedules on your school website.

## Basic Usage

### Get BSIT 2024-2025 Web Development 1st Year Schedule
```bash
curl "http://your-domain.com/api/v1/course-schedules?course_code=BSIT%20(2024%20-%202025)%20WEB&academic_year=1"
```

### Get BSBA 2018-2019 Non-ABM 2nd Year Schedule  
```bash
curl "http://your-domain.com/api/v1/course-schedules?course_code=BSBA%20(2018%20-%202019)%20NON-ABM&academic_year=2"
```

## HTML Table Generation

### Simple HTML Table
```html
<!DOCTYPE html>
<html>
<head>
    <title>Course Schedule</title>
    <style>
        .schedule-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .schedule-table th,
        .schedule-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .schedule-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .schedule-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div id="schedule-container">
        <!-- Schedule will be loaded here -->
    </div>

    <script>
        async function loadCourseSchedule() {
            try {
                const response = await fetch('/api/v1/course-schedules?course_code=BSIT%20(2024%20-%202025)%20WEB&academic_year=1');
                const data = await response.json();
                
                let html = `
                    <h2>${data.course_info.course_title} - Year ${data.course_info.academic_year}</h2>
                    <p><strong>Department:</strong> ${data.course_info.course_department}</p>
                    <p><strong>School Year:</strong> ${data.school_info.current_school_year} - ${data.school_info.semester_name}</p>
                    
                    <table class="schedule-table">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Title</th>
                                <th>Schedule</th>
                                <th>Room</th>
                                <th>Section</th>
                                <th>Faculty</th>
                                <th>Units</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                data.data.forEach(subject => {
                    html += `
                        <tr>
                            <td>${subject.code}</td>
                            <td>${subject.title}</td>
                            <td><strong>${subject.schedule}</strong></td>
                            <td>${subject.room}</td>
                            <td>${subject.section}</td>
                            <td>${subject.faculty}</td>
                            <td>${subject.units}</td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table>';
                document.getElementById('schedule-container').innerHTML = html;
                
            } catch (error) {
                console.error('Error loading schedule:', error);
                document.getElementById('schedule-container').innerHTML = '<p>Error loading schedule. Please try again.</p>';
            }
        }
        
        // Load schedule when page loads
        loadCourseSchedule();
    </script>
</body>
</html>
```

## PHP Laravel Blade Example

### Controller
```php
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class ScheduleController extends Controller
{
    public function showCourseSchedule(Request $request)
    {
        $courseCode = $request->get('course_code', 'BSIT (2024 - 2025) WEB');
        $academicYear = $request->get('academic_year', 1);
        
        $response = Http::get(config('app.url') . '/api/v1/course-schedules', [
            'course_code' => $courseCode,
            'academic_year' => $academicYear,
            'per_page' => 100
        ]);
        
        $scheduleData = $response->json();
        
        return view('schedule.course', compact('scheduleData', 'courseCode', 'academicYear'));
    }
}
```

### Blade Template (resources/views/schedule/course.blade.php)
```blade
@extends('layouts.app')

@section('title', 'Course Schedule')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-12">
            @if(isset($scheduleData['course_info']))
                <h1>{{ $scheduleData['course_info']['course_title'] }}</h1>
                <p class="lead">Year {{ $scheduleData['course_info']['academic_year'] }} - {{ $scheduleData['course_info']['course_department'] }} Department</p>
                <p><strong>School Year:</strong> {{ $scheduleData['school_info']['current_school_year'] }} - {{ $scheduleData['school_info']['semester_name'] }}</p>
            @endif
            
            @if(count($scheduleData['data']) > 0)
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>Code</th>
                                <th>Title</th>
                                <th>Schedule</th>
                                <th>Room</th>
                                <th>Section</th>
                                <th>Faculty</th>
                                <th>Units</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($scheduleData['data'] as $subject)
                                <tr>
                                    <td><strong>{{ $subject['code'] }}</strong></td>
                                    <td>{{ $subject['title'] }}</td>
                                    <td><span class="badge bg-primary">{{ $subject['schedule'] }}</span></td>
                                    <td>{{ $subject['room'] }}</td>
                                    <td>{{ $subject['section'] }}</td>
                                    <td>{{ $subject['faculty'] }}</td>
                                    <td>{{ $subject['units'] }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <p><strong>Total Subjects:</strong> {{ $scheduleData['meta']['total'] }}</p>
                </div>
            @else
                <div class="alert alert-warning">
                    <h4>No Schedule Found</h4>
                    <p>No schedule data available for the selected course and academic year.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
```

## Course Selection Form

### HTML Form for Course Selection
```html
<form id="course-form" class="mb-4">
    <div class="row">
        <div class="col-md-6">
            <label for="course_code" class="form-label">Course Code</label>
            <select id="course_code" name="course_code" class="form-select" required>
                <option value="">Select Course</option>
                <option value="BSIT (2024 - 2025) WEB">BSIT (2024 - 2025) WEB</option>
                <option value="BSIT (2018 - 2019) MOBILE">BSIT (2018 - 2019) MOBILE</option>
                <option value="BSBA (2024 - 2025) ABM">BSBA (2024 - 2025) ABM</option>
                <option value="BSBA (2018 - 2019) NON-ABM">BSBA (2018 - 2019) NON-ABM</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="academic_year" class="form-label">Academic Year</label>
            <select id="academic_year" name="academic_year" class="form-select" required>
                <option value="1">1st Year</option>
                <option value="2">2nd Year</option>
                <option value="3">3rd Year</option>
                <option value="4">4th Year</option>
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">&nbsp;</label>
            <button type="submit" class="btn btn-primary d-block w-100">Load Schedule</button>
        </div>
    </div>
</form>

<script>
document.getElementById('course-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const courseCode = document.getElementById('course_code').value;
    const academicYear = document.getElementById('academic_year').value;
    
    if (!courseCode || !academicYear) {
        alert('Please select both course and academic year');
        return;
    }
    
    // Show loading
    document.getElementById('schedule-container').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>';
    
    try {
        const response = await fetch(`/api/v1/course-schedules?course_code=${encodeURIComponent(courseCode)}&academic_year=${academicYear}`);
        const data = await response.json();
        
        // Generate table HTML (same as previous example)
        // ... table generation code here ...
        
    } catch (error) {
        console.error('Error:', error);
        document.getElementById('schedule-container').innerHTML = '<div class="alert alert-danger">Error loading schedule. Please try again.</div>';
    }
});
</script>
```

## Day Abbreviation Reference

The API returns schedule strings with these day abbreviations:

- **MWF** = Monday, Wednesday, Friday
- **TTH** = Tuesday, Thursday
- **MW** = Monday, Wednesday  
- **TH** = Thursday only
- **T** = Tuesday only
- **F** = Friday only
- **S** = Saturday
- **SU** = Sunday

## Common Course Codes

Here are some example course codes you can use:

### BSIT (Information Technology)
- `BSIT (2024 - 2025) WEB` - Web Development
- `BSIT (2018 - 2019) MOBILE` - Mobile Development

### BSBA (Business Administration)  
- `BSBA (2024 - 2025) ABM` - ABM Track
- `BSBA (2018 - 2019) NON-ABM` - Non-ABM Track

### BSHRM (Hotel and Restaurant Management)
- `BSHRM (2009 - 2010)` - Standard curriculum

To get a list of all available course codes, you can use:
```bash
curl "http://your-domain.com/api/v1/settings"
```

This API endpoint provides everything you need to create beautiful, functional course schedule displays for your school website!
