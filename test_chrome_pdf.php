<?php

declare(strict_types=1);

require __DIR__.'/vendor/autoload.php';

use HeadlessChromium\BrowserFactory;
use Illuminate\Support\Facades\File;

// Set up error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "Starting Chrome-PHP PDF test...\n";

try {
    // Create test directory if it doesn't exist
    $testDir = __DIR__.'/storage/app/test';
    if (! file_exists($testDir)) {
        mkdir($testDir, 0755, true);
        echo "Created test directory: $testDir\n";
    }

    // Create custom Chrome data directory
    $chromeUserDataDir = __DIR__.'/storage/app/chrome-data-test';
    if (! file_exists($chromeUserDataDir)) {
        mkdir($chromeUserDataDir, 0755, true);
        echo "Created Chrome user data directory: $chromeUserDataDir\n";
    }
    chmod($chromeUserDataDir, 0777);

    // Create subdirectories that Chrome might need
    $directories = [
        '/Default',
        '/cache',
        '/Crashpad',
        '/.local/share/applications',
    ];

    foreach ($directories as $dir) {
        $fullPath = $chromeUserDataDir.$dir;
        if (! file_exists($fullPath)) {
            mkdir($fullPath, 0777, true);
            echo "Created directory: $fullPath\n";
        }
    }

    // Create mimeapps.list file
    $mimeappsFile = $chromeUserDataDir.'/.local/share/applications/mimeapps.list';
    if (! file_exists($mimeappsFile)) {
        touch($mimeappsFile);
        echo "Created file: $mimeappsFile\n";
    }

    $outputPath = $testDir.'/chrome_php_test.pdf';
    echo "Output PDF will be saved to: $outputPath\n";

    // Initialize BrowserFactory with custom options
    echo "Initializing BrowserFactory...\n";
    $browserFactory = new BrowserFactory;
    $browser = $browserFactory->createBrowser([
        'userDataDir' => $chromeUserDataDir,
        'ignoreCertificateErrors' => true,
        'noSandbox' => true,
        'keepAlive' => false,
        'sendSyncDefaultTimeout' => 45000,
        'windowSize' => [1024, 768],
        'enableImages' => false,
        'debugLogger' => true,
        'args' => [
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-extensions',
            '--disable-software-rasterizer',
            '--disable-default-apps',
            '--disable-setuid-sandbox',
            '--no-first-run',
            '--no-sandbox',
            '--no-zygote',
            '--single-process',
            '--homedir='.$chromeUserDataDir,
            '--crash-dumps-dir='.$chromeUserDataDir,
            '--user-data-dir='.$chromeUserDataDir,
            '--disable-crashpad',
        ],
    ]);

    echo "Browser instance created successfully\n";

    // Create a page and set HTML content
    $page = $browser->createPage();
    echo "Page created\n";

    // Create simple HTML content
    $html = '<!DOCTYPE html><html><head><title>Chrome-PHP Test</title></head><body><h1>Test PDF Generation with Chrome-PHP</h1><p>Generated at: '.date('Y-m-d H:i:s').'</p></body></html>';

    // Navigate to a URL or set HTML content
    $page->setHtml($html);
    echo "HTML content set\n";

    // Generate PDF and save to file
    echo "Generating PDF...\n";
    $pdf = $page->pdf(['printBackground' => true]);
    $pdf->saveToFile($outputPath);

    echo "PDF generated and saved to: $outputPath\n";

    // Check if the PDF was created
    if (file_exists($outputPath)) {
        echo 'PDF file exists with size: '.filesize($outputPath)." bytes\n";
    } else {
        echo "ERROR: PDF file was not created\n";
    }

    // Clean up
    $browser->close();
    echo "Browser closed\n";

    echo "Test completed successfully!\n";

} catch (Exception $e) {
    echo 'ERROR: '.$e->getMessage()."\n";
    echo 'Stack trace: '.$e->getTraceAsString()."\n";
}
