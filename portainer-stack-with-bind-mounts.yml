version: "3.8"

services:
  app:
    image: ${APP_IMAGE:-ghcr.io/yukazakiri/dccp-admin:latest} # IMPORTANT: Replace YOUR_GITHUB_USERNAME or set APP_IMAGE in Portainer
    container_name: dccp-admin-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8000}:8000"
    environment:
      APP_NAME: "${APP_NAME:-DCCP Admin}"
      APP_ENV: production
      APP_DEBUG: false
      APP_KEY: "${APP_KEY}"
      APP_URL: "${APP_URL:-http://localhost:8000}"
      APP_TIMEZONE: UTC

      # Database Configuration
      DB_CONNECTION: pgsql
      DB_HOST: "${DB_HOST}"
      DB_PORT: "${DB_PORT:-5432}"
      DB_DATABASE: "${DB_DATABASE}"
      DB_USERNAME: "${DB_USERNAME}"
      DB_PASSWORD: "${DB_PASSWORD}"

      # Redis Configuration
      REDIS_HOST: "${REDIS_HOST}"
      REDIS_PORT: "${REDIS_PORT:-6379}"
      REDIS_PASSWORD: "${REDIS_PASSWORD:-}"
      REDIS_CLIENT: phpredis

      # Cache and Session
      CACHE_STORE: redis
      CACHE_PREFIX: dccp_admin
      SESSION_DRIVER: redis
      SESSION_LIFETIME: 120
      SESSION_ENCRYPT: false

      # Queue Configuration
      QUEUE_CONNECTION: redis

      # Logging
      LOG_CHANNEL: stderr
      LOG_LEVEL: "${LOG_LEVEL:-warning}"

      # Mail Configuration
      MAIL_MAILER: "${MAIL_MAILER:-smtp}"
      MAIL_HOST: "${MAIL_HOST:-}"
      MAIL_PORT: "${MAIL_PORT:-587}"
      MAIL_USERNAME: "${MAIL_USERNAME:-}"
      MAIL_PASSWORD: "${MAIL_PASSWORD:-}"
      MAIL_ENCRYPTION: "${MAIL_ENCRYPTION:-tls}"
      MAIL_FROM_ADDRESS: "${MAIL_FROM_ADDRESS:-<EMAIL>}"
      MAIL_FROM_NAME: "${MAIL_FROM_NAME:-DCCP Admin}"

      # Laravel Optimization
      OCTANE_SERVER: frankenphp
      BCRYPT_ROUNDS: 12

      # Optional Features
      RUN_MIGRATIONS: "${RUN_MIGRATIONS:-false}"

    volumes:
      - app_storage:/app/storage/app
      - app_logs:/app/storage/logs
      - app_cache:/app/storage/framework/cache
      - app_sessions:/app/storage/framework/sessions
      - app_views:/app/storage/framework/views
      - app_public:/app/storage/app/public

    networks:
      - dccp-network

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 1G
        reservations:
          cpus: "0.5"
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s

    labels:

  worker:
    image: ${APP_IMAGE:-ghcr.io/yukazakiri/dccp-admin:latest} # IMPORTANT: Replace YOUR_GITHUB_USERNAME or set APP_IMAGE
    container_name: dccp-admin-worker
    restart: unless-stopped
    command:
      ["php", "artisan", "queue:work", "--sleep=3", "--tries=3", "--timeout=90"]
    environment:
      APP_NAME: "${APP_NAME:-DCCP Admin}"
      APP_ENV: production
      APP_DEBUG: false
      APP_KEY: "${APP_KEY}"
      APP_URL: "${APP_URL:-http://localhost:8000}" # Worker might not need URL
      APP_TIMEZONE: UTC

      # Database Configuration
      DB_CONNECTION: pgsql
      DB_HOST: "${DB_HOST:-postgres}" # Use internal postgres if profile is active, otherwise external
      DB_PORT: "${DB_PORT:-5432}"
      DB_DATABASE: "${DB_DATABASE}"
      DB_USERNAME: "${DB_USERNAME}"
      DB_PASSWORD: "${DB_PASSWORD}"

      # Redis Configuration
      REDIS_HOST: "${REDIS_HOST:-redis}" # Use internal redis if profile is active, otherwise external
      REDIS_PORT: "${REDIS_PORT:-6379}"
      REDIS_PASSWORD: "${REDIS_PASSWORD:-}"
      REDIS_CLIENT: phpredis

      # Cache and Session
      CACHE_STORE: redis
      CACHE_PREFIX: dccp_admin
      SESSION_DRIVER: redis # Worker might not interact with sessions

      # Queue Configuration (Crucial for worker)
      QUEUE_CONNECTION: redis

      # Logging
      LOG_CHANNEL: stderr
      LOG_LEVEL: "${LOG_LEVEL:-warning}"

      # Mail Configuration (If jobs send emails)
      MAIL_MAILER: "${MAIL_MAILER:-smtp}"
      MAIL_HOST: "${MAIL_HOST:-}"
      MAIL_PORT: "${MAIL_PORT:-587}"
      MAIL_USERNAME: "${MAIL_USERNAME:-}"
      MAIL_PASSWORD: "${MAIL_PASSWORD:-}"
      MAIL_ENCRYPTION: "${MAIL_ENCRYPTION:-tls}"
      MAIL_FROM_ADDRESS: "${MAIL_FROM_ADDRESS:-<EMAIL>}"
      MAIL_FROM_NAME: "${MAIL_FROM_NAME:-DCCP Admin}"

      # Laravel Optimization (OCTANE_SERVER is not relevant for worker)
      BCRYPT_ROUNDS: 12

      # Optional Features (Worker should not run migrations)
      RUN_MIGRATIONS: "false"

    volumes:
      - app_storage:/app/storage/app
      - app_logs:/app/storage/logs
      - app_cache:/app/storage/framework/cache
      - app_public:/app/storage/app/public

    networks:
      - dccp-network

    depends_on: # Worker depends on app for code, and potentially redis/postgres if internal
      app:
        condition: service_started # Wait for app service to fully start (though worker is independent after code sync)
      redis: # Optional, only if using internal Redis
        condition: service_healthy
        required: false # Make it optional based on profile
      postgres: # Optional, only if using internal PostgreSQL
        condition: service_healthy
        required: false # Make it optional based on profile

    deploy:
      resources:
        limits:
          cpus: "${WORKER_CPU_LIMIT:-1.0}"
          memory: "${WORKER_MEMORY_LIMIT:-512M}"
        reservations:
          cpus: "0.25"
          memory: "128M"
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 5 # Workers can be more persistent
        window: 120s

    labels: # For consistency and potential Portainer grouping
      - "com.docker.compose.project=dccp-admin"
      - "com.portainer.stack.name=dccp-admin"

  # Optional: Include Redis if not using external
  redis:
    image: redis:7-alpine
    container_name: dccp-admin-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass "${REDIS_PASSWORD:-}"
    ports:
      - "${REDIS_EXTERNAL_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - dccp-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 512M
        reservations:
          cpus: "0.1"
          memory: 64M
    profiles:
      - with-redis

  # Optional: Include PostgreSQL if not using external
  postgres:
    image: postgres:16-alpine
    container_name: dccp-admin-postgres
    restart: unless-stopped
    ports:
      - "${POSTGRES_EXTERNAL_PORT:-5432}:5432"
    environment:
      POSTGRES_DB: "${DB_DATABASE}"
      POSTGRES_USER: "${DB_USERNAME}"
      POSTGRES_PASSWORD: "${DB_PASSWORD}"
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - dccp-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME} -d ${DB_DATABASE}"]
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 1G
        reservations:
          cpus: "0.2"
          memory: 128M
    profiles:
      - with-postgres

volumes:
  app_storage:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: "${STORAGE_PATH:-/opt/dccp-admin/storage}/app"
  app_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: "${STORAGE_PATH:-/opt/dccp-admin/storage}/logs"
  app_cache:
    driver: local
  app_sessions:
    driver: local
  app_views:
    driver: local
  app_public:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: "${STORAGE_PATH:-/opt/dccp-admin/storage}/app/public"
  redis_data:
    driver: local
  postgres_data:
    driver: local

networks:
  dccp-network:
    driver: bridge
    external: false
    ipam:
      config:
        - subnet: **********/16
