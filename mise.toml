# ~/.config/mise/config.toml (or global mise.toml)
# You might want to define global aliases or tools here.
# For example, to ensure PHP and Composer are always available:
# [tools]
# php = "8.2" # Or your desired PHP version
# composer = "2" # Or your desired Composer version

# mise.toml (in your Laravel project root directory)
# This file defines project-specific tools and tasks.

[tools]
# Specify the PHP version for this project.
# Ensure you have mise installed the correct PHP version.
php = "8.2" # Adjust to your Laravel project's PHP requirement

# Specify the Composer version for this project.
# This ensures consistency across development and production.
composer = "2" # Adjust if you have a specific Composer version

[tasks]
# Task to install Composer dependencies.
# Use --no-dev to prevent installation of development dependencies in production.
# --optimize-autoloader optimizes the autoloader for faster performance.
# --no-interaction prevents interactive prompts, crucial for automated deployments.
composer-install = "composer install --no-dev --optimize-autoloader --no-interaction"

# Task to run database migrations.
# --force is needed in production to bypass the confirmation prompt.
migrate = "php artisan migrate --force"

# Task to run database seeders (optional, use with caution in production).
# seed = "php artisan db:seed --force"

# Task to clear various caches (config, route, view, application).
# This is crucial after deployments to ensure new code is reflected.
clear-cache = [
    "php artisan config:clear",
    "php artisan route:clear",
    "php artisan view:clear",
    "php artisan cache:clear",
]

# Task to optimize the application for production.
# This includes caching configuration, routes, and compiling views.
optimize = [
    "php artisan config:cache", # Caches configuration for faster loading
    "php artisan route:cache",  # Caches routes for faster routing
    "php artisan view:cache",   # Compiles blade views for faster rendering
]

# Task to optimize Composer's autoloader for production.
# This is generally run after 'composer-install'.
optimize-autoloader = "composer dump-autoload --optimize --no-dev"

# Task to run storage link.
# Useful if your public/storage directory needs to be linked to storage/app/public.
storage-link = "php artisan storage:link"

# Task to run all necessary production setup steps.
# You can customize the order and inclusion of these tasks.
deploy-setup = [
    "mise run composer-install",
    "mise run clear-cache",
    "mise run migrate",
    "mise run optimize",
    "mise run optimize-autoloader", # Redundant if composer-install already used --optimize-autoloader
    "mise run storage-link",        # Only if you use storage links
    # Add any other production-specific commands here
    # e.g., 'php artisan queue:restart' if you run queues
]

# Task to clear all compiled and cached files for a fresh start.
# Useful for debugging or when things go wrong after a deployment.
# This is more aggressive than `clear-cache`.
prune = [
    "php artisan cache:clear",
    "php artisan config:clear",
    "php artisan route:clear",
    "php artisan view:clear",
    "rm -f bootstrap/cache/*.php", # Remove all cached files explicitly
    "composer dump-autoload",      # Rebuild autoloader
]

# A simple task to check the application's health.
health-check = "php artisan about"
