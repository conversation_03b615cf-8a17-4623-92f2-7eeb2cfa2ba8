name: CI/CD Pipeline

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  # Use docker.io for Docker Hub
  REGISTRY: docker.io
  # The Docker Hub repository name, e.g., 'dccpadminv2'
  IMAGE_NAME: dccpadminv2

jobs:
  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write # Keep for ghcr cache

    outputs:
      image_tag: ${{ steps.image_tag.outputs.tag }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Generate image tag
        id: image_tag
        run: echo "tag=$(echo $GITHUB_SHA | cut -c1-8)" >> $GITHUB_OUTPUT

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ env.REGISTRY }}/${{ secrets.DOCKERHUB_USERNAME }}/${{ env.IMAGE_NAME }}:${{ steps.image_tag.outputs.tag }}
          # Cache from GHCR to speed up builds
          cache-from: type=gha
          cache-to: type=gha,mode=max

  test-browsershot:
    name: Test Browsershot PDF Generation
    if: github.event_name != 'pull_request'
    runs-on: ubuntu-latest
    needs: build

    steps:
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Run Browsershot test in container
        run: |
          IMAGE_FQN="${{ env.REGISTRY }}/${{ secrets.DOCKERHUB_USERNAME }}/${{ env.IMAGE_NAME }}"
          IMAGE_TAG="${{ needs.build.outputs.image_tag }}"
          echo "Testing image ${IMAGE_FQN}:${IMAGE_TAG}"
          docker run --rm \
            --entrypoint="php" \
            "${IMAGE_FQN}:${IMAGE_TAG}" \
            artisan tinker --execute="\Spatie\Browsershot\Browsershot::html('<h1>Hello from GitHub Actions!</h1>')->save('/tmp/test.pdf');"

  publish:
    name: Tag and Publish Latest Image
    if: github.event_name != 'pull_request' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    needs: [build, test-browsershot] # Depends on build and test

    steps:
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Pull, Tag and Push Latest Image
        run: |
          IMAGE_FQN="${{ env.REGISTRY }}/${{ secrets.DOCKERHUB_USERNAME }}/${{ env.IMAGE_NAME }}"
          IMAGE_TAG="${{ needs.build.outputs.image_tag }}"
          echo "Pulling ${IMAGE_FQN}:${IMAGE_TAG}"
          docker pull "${IMAGE_FQN}:${IMAGE_TAG}"
          echo "Tagging ${IMAGE_FQN}:${IMAGE_TAG} as ${IMAGE_FQN}:latest"
          docker tag "${IMAGE_FQN}:${IMAGE_TAG}" "${IMAGE_FQN}:latest"
          echo "Pushing image ${IMAGE_FQN}:latest"
          docker push "${IMAGE_FQN}:latest"
