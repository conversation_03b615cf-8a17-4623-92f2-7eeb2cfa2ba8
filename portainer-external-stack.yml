version: '3.8'

services:
  app:
    image: ${APP_IMAGE:-ghcr.io/your_github_username/dccp-admin:latest} # Edit this default or set APP_IMAGE in Portainer
    container_name: dccp-admin-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8000}:8000"
    environment:
      APP_NAME: "${APP_NAME:-DCCP Admin}"
      APP_ENV: production
      APP_DEBUG: false
      APP_KEY: "${APP_KEY}"
      APP_URL: "${APP_URL:-http://localhost:8000}"
      APP_TIMEZONE: UTC
      
      # External Database Configuration
      DB_CONNECTION: pgsql
      DB_HOST: "${DB_HOST}"
      DB_PORT: "${DB_PORT:-5432}"
      DB_DATABASE: "${DB_DATABASE}"
      DB_USERNAME: "${DB_USERNAME}"
      DB_PASSWORD: "${DB_PASSWORD}"
      
      # External Redis Configuration
      REDIS_HOST: "${REDIS_HOST}"
      REDIS_PORT: "${REDIS_PORT:-6379}"
      REDIS_PASSWORD: "${REDIS_PASSWORD:-}"
      REDIS_CLIENT: phpredis
      
      # Cache and Session
      CACHE_STORE: redis
      CACHE_PREFIX: dccp_admin
      SESSION_DRIVER: redis
      SESSION_LIFETIME: 120
      SESSION_ENCRYPT: false
      
      # Queue Configuration
      QUEUE_CONNECTION: redis
      
      # Logging
      LOG_CHANNEL: stderr
      LOG_LEVEL: "${LOG_LEVEL:-warning}"
      
      # Mail Configuration
      MAIL_MAILER: "${MAIL_MAILER:-smtp}"
      MAIL_HOST: "${MAIL_HOST:-}"
      MAIL_PORT: "${MAIL_PORT:-587}"
      MAIL_USERNAME: "${MAIL_USERNAME:-}"
      MAIL_PASSWORD: "${MAIL_PASSWORD:-}"
      MAIL_ENCRYPTION: "${MAIL_ENCRYPTION:-tls}"
      MAIL_FROM_ADDRESS: "${MAIL_FROM_ADDRESS:-<EMAIL>}"
      MAIL_FROM_NAME: "${MAIL_FROM_NAME:-DCCP Admin}"
      
      # Laravel Optimization
      OCTANE_SERVER: frankenphp
      BCRYPT_ROUNDS: 12
      
      # Optional Features
      RUN_MIGRATIONS: "${RUN_MIGRATIONS:-false}"
      
      worker:
        image: ${APP_IMAGE:-ghcr.io/your_github_username/dccp-admin:latest} # Edit this default or set APP_IMAGE in Portainer
        container_name: dccp-admin-worker
        restart: unless-stopped
        command: ["php", "artisan", "queue:work", "--sleep=3", "--tries=3", "--timeout=90"]
        environment:
          APP_NAME: "${APP_NAME:-DCCP Admin}"
          APP_ENV: production
          APP_DEBUG: false
          APP_KEY: "${APP_KEY}"
          APP_URL: "${APP_URL:-http://localhost:8000}" # Worker might not need URL, but good for consistency
          APP_TIMEZONE: UTC
      
          # Database Configuration
          DB_CONNECTION: pgsql
          DB_HOST: "${DB_HOST}"
          DB_PORT: "${DB_PORT:-5432}"
          DB_DATABASE: "${DB_DATABASE}"
          DB_USERNAME: "${DB_USERNAME}"
          DB_PASSWORD: "${DB_PASSWORD}"
      
          # Redis Configuration
          REDIS_HOST: "${REDIS_HOST}"
          REDIS_PORT: "${REDIS_PORT:-6379}"
          REDIS_PASSWORD: "${REDIS_PASSWORD:-}"
          REDIS_CLIENT: phpredis
      
          # Cache and Session
          CACHE_STORE: redis
          CACHE_PREFIX: dccp_admin
          SESSION_DRIVER: redis # Worker might not interact with sessions directly
          SESSION_LIFETIME: 120
          SESSION_ENCRYPT: false # Ensure consistency
      
          # Queue Configuration (Crucial for worker)
          QUEUE_CONNECTION: redis
      
          # Logging
          LOG_CHANNEL: stderr
          LOG_LEVEL: "${LOG_LEVEL:-warning}"
      
          # Mail Configuration (If jobs send emails)
          MAIL_MAILER: "${MAIL_MAILER:-smtp}"
          MAIL_HOST: "${MAIL_HOST:-}"
          MAIL_PORT: "${MAIL_PORT:-587}"
          MAIL_USERNAME: "${MAIL_USERNAME:-}"
          MAIL_PASSWORD: "${MAIL_PASSWORD:-}"
          MAIL_ENCRYPTION: "${MAIL_ENCRYPTION:-tls}"
          MAIL_FROM_ADDRESS: "${MAIL_FROM_ADDRESS:-<EMAIL>}"
          MAIL_FROM_NAME: "${MAIL_FROM_NAME:-DCCP Admin}"
      
          # Laravel Optimization (OCTANE_SERVER is not relevant for worker)
          OCTANE_SERVER: frankenphp 
          BCRYPT_ROUNDS: 12
      
          # Optional Features (Worker should not run migrations)
          RUN_MIGRATIONS: "false"
      
        volumes:
          # Mount necessary volumes for the worker to access code and potentially storage
          - app_storage:/app/storage/app
          - app_logs:/app/storage/logs # Worker should also log here
          - app_cache:/app/storage/framework/cache # If it uses any file-based cache
          - app_public:/app/storage/app/public # If jobs access public storage linked files
          # - app_sessions:/app/storage/framework/sessions # Likely not needed
          # - app_views:/app/storage/framework/views       # Likely not needed
    
        networks:
          - dccp-network
    
        deploy:
          resources:
            limits:
              cpus: '1.0' # Adjust as needed, can be parameterized e.g. ${WORKER_CPU_LIMIT:-1.0}
              memory: '512M' # Adjust as needed, e.g. ${WORKER_MEMORY_LIMIT:-512M}
            reservations:
              cpus: '0.25'
              memory: '128M'
          restart_policy:
            condition: on-failure
            delay: 10s
            max_attempts: 3 # Or higher for queue workers
            window: 120s
    
        labels: # For consistency and potential Portainer grouping
          - "com.docker.compose.project=dccp-admin"
          - "com.portainer.stack.name=dccp-admin"

    volumes:
      - app_storage:/app/storage/app
      - app_logs:/app/storage/logs
      - app_cache:/app/storage/framework/cache
      - app_sessions:/app/storage/framework/sessions
      - app_views:/app/storage/framework/views
      - app_public:/app/storage/app/public
    
    networks:
      - dccp-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    deploy:
      resources:
        limits:
          cpus: "${APP_CPU_LIMIT:-2.0}"
          memory: "${APP_MEMORY_LIMIT:-1G}"
        reservations:
          cpus: '0.5'
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
    
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dccp-admin.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.routers.dccp-admin.entrypoints=websecure"
      - "traefik.http.routers.dccp-admin.tls.certresolver=letsencrypt"
      - "traefik.http.services.dccp-admin.loadbalancer.server.port=8000"
      - "com.docker.compose.project=dccp-admin"
      - "com.portainer.stack.name=dccp-admin"

volumes:
  app_storage:
    driver: local
  app_logs:
    driver: local
  app_cache:
    driver: local
  app_sessions:
    driver: local
  app_views:
    driver: local
  app_public:
    driver: local

networks:
  dccp-network:
    driver: bridge
    external: false
    ipam:
      config:
        - subnet: **********/16