<?php

declare(strict_types=1);

namespace Tests\Feature\Api;

use App\Models\Student;
use App\Models\User;
// use Illuminate\Foundation\Testing\RefreshDatabase; // Removed RefreshDatabase
use Laravel\Sanctum\Sanctum;

// Base URL for the student API
const BASE_URL = '/api/admin/students';

// --- Helper function to get authenticated headers ---
// Note: This helper is not used when using Sanctum::actingAs, but kept for potential future use
function getAuthHeaders(User $user): array
{
    $token = $user->createToken('test-token')->plainTextToken;

    return ['Authorization' => 'Bearer '.$token];
}

// --- Test Suite ---

test('guest cannot access student list', function (): void {
    $response = $this->getJson(BASE_URL);
    $response->assertUnauthorized(); // Expect 401
});

test('authenticated user can access student list', function (): void {
    // Assume user with ID 1 exists in your current database
    $user = User::find(1);
    if (! $user) {
        $this->markTestSkipped('User with ID 1 not found in the database.');

        return;
    }
    Sanctum::actingAs($user);

    $response = $this->getJson(BASE_URL);
    $response->assertOk(); // Expect 200
    // Optionally assert structure or data presence
    $response->assertJsonStructure(['data', 'links', 'meta']);
});

test('guest cannot access student detail', function (): void {
    // Assume student with ID 123 exists
    $studentId = 123;
    $response = $this->getJson(BASE_URL.'/'.$studentId);
    $response->assertUnauthorized();
});

test('authenticated user can access student detail', function (): void {
    $user = User::find(1);
    if (! $user) {
        $this->markTestSkipped('User with ID 1 not found.');

        return;
    }
    Sanctum::actingAs($user);

    // Assume student with ID 123 exists
    $studentId = 123;
    $student = Student::find($studentId);
    if (! $student) {
        $this->markTestSkipped('Student with ID 123 not found.');

        return;
    }

    $response = $this->getJson(BASE_URL.'/'.$studentId);
    $response->assertOk();
    $response->assertJsonPath('data.id', $studentId); // Verify correct student data
});

test('guest cannot create student', function (): void {
    $response = $this->postJson(BASE_URL, []); // Send empty data
    $response->assertUnauthorized();
});

test('authenticated user gets validation error when creating student with invalid data', function (): void {
    $user = User::find(1);
    if (! $user) {
        $this->markTestSkipped('User with ID 1 not found.');

        return;
    }
    Sanctum::actingAs($user);

    $response = $this->postJson(BASE_URL, []); // Send empty data
    $response->assertStatus(422); // Expect validation error
});

// Add a test for successful creation if you have valid data structure
// test('authenticated user can create student', function () { ... });

test('guest cannot update student', function (): void {
    $studentId = 123; // Assume student exists
    $response = $this->putJson(BASE_URL.'/'.$studentId, []);
    $response->assertUnauthorized();
});

test('authenticated user gets validation error when updating student with invalid data', function (): void {
    $user = User::find(1);
    if (! $user) {
        $this->markTestSkipped('User with ID 1 not found.');

        return;
    }
    Sanctum::actingAs($user);

    $studentId = 123; // Assume student exists
    $student = Student::find($studentId);
    if (! $student) {
        $this->markTestSkipped('Student with ID 123 not found.');

        return;
    }

    $response = $this->putJson(BASE_URL.'/'.$studentId, []);
    $response->assertStatus(422); // Expect validation error
});

// Add a test for successful update if you have valid data structure
// test('authenticated user can update student', function () { ... });

test('guest cannot delete student', function (): void {
    $studentId = 123; // Assume student exists
    $response = $this->deleteJson(BASE_URL.'/'.$studentId);
    $response->assertUnauthorized();
});

// Note: This test WILL delete student 123 from your actual database if it passes.
// Consider commenting it out or using a dedicated test student ID if needed.
test('authenticated user can delete student', function (): void {
    $user = User::find(1);
    if (! $user) {
        $this->markTestSkipped('User with ID 1 not found.');

        return;
    }
    Sanctum::actingAs($user);

    $studentId = 123; // Assume student exists
    $student = Student::find($studentId);
    if (! $student) {
        $this->markTestSkipped('Student with ID 123 not found.');

        return;
    }

    $response = $this->deleteJson(BASE_URL.'/'.$studentId);
    // Expect 200 OK or 204 No Content depending on handler implementation
    $response->assertSuccessful();

    // Since we are not using RefreshDatabase, the student is now soft deleted.
    // We can assert it's soft deleted.
    $this->assertSoftDeleted('students', ['id' => $studentId]);

    // Restore the student so it's available for the next test run
    $deletedStudent = Student::withTrashed()->find($studentId);
    if ($deletedStudent) {
        $deletedStudent->restore();
        $this->assertDatabaseHas('students', ['id' => $studentId, 'deleted_at' => null]); // Verify restoration
    }

    // Optionally assert the student is actually deleted (soft or hard)
    // $this->assertDatabaseMissing('students', ['id' => $studentId]); // For hard delete
    // $this->assertSoftDeleted('students', ['id' => $student->id]); // If using SoftDeletes
});

// --- Auth Endpoint Tests ---

test('can login via api', function (): void {
    $user = User::factory()->create([
        'password' => bcrypt($password = 'password'), // Create user with known password
    ]);

    $response = $this->postJson('/api/auth/login', [
        'email' => $user->email,
        'password' => $password,
    ]);

    $response->assertStatus(201); // Expect 201 Created for successful login/token creation
    $response->assertJsonStructure(['token']); // Check if token is returned
});

test('cannot login with invalid credentials', function (): void {
    $user = User::factory()->create([
        'password' => bcrypt('password'),
    ]);

    $response = $this->postJson('/api/auth/login', [
        'email' => $user->email,
        'password' => 'wrong-password',
    ]);

    $response->assertUnauthorized(); // Expect 401 Unauthorized for invalid credentials
});

test('authenticated user can logout via api', function (): void {
    $user = User::find(1); // Use existing user
    if (! $user) {
        $this->markTestSkipped('User with ID 1 not found.');

        return;
    }
    Sanctum::actingAs($user);

    // Ensure the user has at least one token before logout attempt
    $user->createToken('logout-test-token');
    expect($user->tokens()->count())->toBeGreaterThan(0);

    $response = $this->postJson('/api/auth/logout');

    $response->assertOk(); // Or assertNoContent() depending on implementation

    // Refresh user model and check tokens are revoked
    $user->refresh();
    // Note: Logout might revoke only the current token or all tokens.
    // Adjust assertion based on package behavior.
    // expect($user->tokens()->count())->toBe(0); // If all tokens are revoked
});

// --- Default /api/user Endpoint Tests ---

test('guest cannot access user endpoint', function (): void {
    $response = $this->getJson('/api/user');
    $response->assertUnauthorized();
});

test('authenticated user can access user endpoint', function (): void {
    $user = User::find(1); // Use existing user
    if (! $user) {
        $this->markTestSkipped('User with ID 1 not found.');

        return;
    }
    Sanctum::actingAs($user);

    $response = $this->getJson('/api/user');
    $response->assertOk();
    $response->assertJsonPath('id', $user->id); // Verify correct user data
});
