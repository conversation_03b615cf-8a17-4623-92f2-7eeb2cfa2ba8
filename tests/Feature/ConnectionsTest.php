<?php

declare(strict_types=1);

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

test('postgresql environment variables are properly configured', function (): void {
    // Check if all required PostgreSQL variables are set
    $requiredVars = [
        'DB_CONNECTION' => env('DB_CONNECTION'),
        'DB_HOST' => env('DB_HOST'),
        'DB_PORT' => env('DB_PORT'),
        'DB_DATABASE' => env('DB_DATABASE'),
        'DB_USERNAME' => env('DB_USERNAME'),
        'DB_PASSWORD' => env('DB_PASSWORD'),
    ];

    // Verify each variable is set
    foreach ($requiredVars as $key => $value) {
        expect($value)->not->toBeNull("$key is not set in .env")
            ->not->toBeEmpty("$key is empty in .env");
    }

    // Verify connection type is postgresql
    expect($requiredVars['DB_CONNECTION'])
        ->toBe('pgsql', 'DB_CONNECTION should be set to "pgsql"');
});

test('postgresql connection is working', function (): void {
    try {
        // Try to connect and perform a simple query
        $result = DB::connection('pgsql')
            ->select('SELECT version()');

        expect($result)
            ->toBeArray()
            ->sequence(
                fn ($value) => $value->toBeInstanceOf(stdClass::class)
            );

    } catch (Exception $e) {
        test()->fail(
            'Failed to connect to PostgreSQL database: '.$e->getMessage()
        );
    }
})->depends('postgresql environment variables are properly configured');

test('redis environment variables are properly configured', function (): void {
    // Check if all required Redis variables are set
    $requiredVars = [
        'REDIS_CLIENT' => env('REDIS_CLIENT'),
        'REDIS_HOST' => env('REDIS_HOST'),
        'REDIS_PORT' => env('REDIS_PORT'),
    ];

    // Verify each variable is set
    foreach ($requiredVars as $key => $value) {
        expect($value)->not->toBeNull("$key is not set in .env");
    }

    // Verify Redis client is set to predis
    expect($requiredVars['REDIS_CLIENT'])
        ->toBe('predis', 'REDIS_CLIENT should be set to "predis"');
});

test('redis connection is working', function (): void {
    try {
        // Test Redis connection by setting and getting a value
        $testKey = 'connection_test_'.time();
        $testValue = 'working';

        Redis::set($testKey, $testValue);
        $result = Redis::get($testKey);
        Redis::del($testKey);

        expect($result)->toBe($testValue, 'Redis read/write operation failed');

    } catch (Exception $e) {
        test()->fail(
            'Redis connection failed: '.$e->getMessage()
        );
    }
})->depends('redis environment variables are properly configured');
