<?php

namespace Tests\Feature;

use App\Models\Course;
use App\Models\Student;
use App\Models\Subject;
use App\Models\SubjectEnrollment;
use App\Models\StudentEnrollment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PartialEnrollmentTest extends TestCase
{
    use RefreshDatabase;

    public function test_subject_enrollment_can_store_partial_units()
    {
        // Create a course
        $course = Course::factory()->create([
            'lec_per_unit' => 100,
            'lab_per_unit' => 50,
        ]);

        // Create a student
        $student = Student::factory()->create([
            'course_id' => $course->id,
        ]);

        // Create a subject with 3 lecture units and 2 lab units
        $subject = Subject::factory()->create([
            'course_id' => $course->id,
            'lecture' => 3,
            'laboratory' => 2,
        ]);

        // Create a student enrollment
        $enrollment = StudentEnrollment::factory()->create([
            'student_id' => $student->id,
            'course_id' => $course->id,
        ]);

        // Create a subject enrollment with partial units (only 2 lab units)
        $subjectEnrollment = SubjectEnrollment::create([
            'student_id' => $student->id,
            'subject_id' => $subject->id,
            'enrollment_id' => $enrollment->id,
            'enrolled_lecture_units' => 0, // Student doesn't want lecture units
            'enrolled_laboratory_units' => 2, // Student wants only lab units
            'lecture_fee' => 0,
            'laboratory_fee' => 100, // 2 units * 50 per unit
            'school_year' => '2024-2025',
            'semester' => 1,
        ]);

        $this->assertDatabaseHas('subject_enrollments', [
            'id' => $subjectEnrollment->id,
            'enrolled_lecture_units' => 0,
            'enrolled_laboratory_units' => 2,
            'lecture_fee' => 0,
            'laboratory_fee' => 100,
        ]);
    }

    public function test_subject_enrollment_can_store_full_units()
    {
        // Create a course
        $course = Course::factory()->create([
            'lec_per_unit' => 100,
            'lab_per_unit' => 50,
        ]);

        // Create a student
        $student = Student::factory()->create([
            'course_id' => $course->id,
        ]);

        // Create a subject with 3 lecture units and 2 lab units
        $subject = Subject::factory()->create([
            'course_id' => $course->id,
            'lecture' => 3,
            'laboratory' => 2,
        ]);

        // Create a student enrollment
        $enrollment = StudentEnrollment::factory()->create([
            'student_id' => $student->id,
            'course_id' => $course->id,
        ]);

        // Create a subject enrollment with full units
        $subjectEnrollment = SubjectEnrollment::create([
            'student_id' => $student->id,
            'subject_id' => $subject->id,
            'enrollment_id' => $enrollment->id,
            'enrolled_lecture_units' => 3, // Full lecture units
            'enrolled_laboratory_units' => 2, // Full lab units
            'lecture_fee' => 500, // 5 total units * 100 per unit
            'laboratory_fee' => 100, // 2 units * 50 per unit
            'school_year' => '2024-2025',
            'semester' => 1,
        ]);

        $this->assertDatabaseHas('subject_enrollments', [
            'id' => $subjectEnrollment->id,
            'enrolled_lecture_units' => 3,
            'enrolled_laboratory_units' => 2,
            'lecture_fee' => 500,
            'laboratory_fee' => 100,
        ]);
    }
}
