<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Jobs\GenerateAssessmentPdfJob;
use App\Models\Course;
use App\Models\GeneralSetting;
use App\Models\Student;
use App\Models\StudentEnrollment;
use App\Models\User;
use App\Models\UserSetting;
use App\Services\GeneralSettingsService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

final class PdfGenerationWithSettingsTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_uses_general_settings_service_for_pdf_generation(): void
    {
        // Arrange
        $this->createTestData();

        $enrollment = StudentEnrollment::first();

        // Create a test user and user settings
        $user = User::factory()->create();
        Auth::login($user);

        UserSetting::create([
            'user_id' => $user->id,
            'semester' => 2,
            'school_year_start' => 2024,
        ]);

        // Act
        $settingsService = new GeneralSettingsService();

        // Assert that the service returns user-specific settings when available
        $this->assertEquals(2, $settingsService->getCurrentSemester());
        $this->assertEquals(2024, $settingsService->getCurrentSchoolYearStart());
        $this->assertEquals('2024 - 2025', $settingsService->getCurrentSchoolYearString());

        // Test that PDF job can be created without errors
        $job = new GenerateAssessmentPdfJob($enrollment);
        $this->assertInstanceOf(GenerateAssessmentPdfJob::class, $job);
    }

    /** @test */
    public function it_falls_back_to_global_settings_when_no_user_settings(): void
    {
        // Arrange
        $this->createTestData();

        // No user logged in, should use global settings
        Auth::logout();

        // Act
        $settingsService = new GeneralSettingsService();

        // Assert that the service returns global settings
        $this->assertEquals(1, $settingsService->getCurrentSemester());
        $this->assertEquals(2023, $settingsService->getCurrentSchoolYearStart());
        $this->assertEquals('2023 - 2024', $settingsService->getCurrentSchoolYearString());
    }

    /** @test */
    public function it_queues_pdf_generation_job_with_settings(): void
    {
        // Arrange
        Queue::fake();
        $this->createTestData();

        $enrollment = StudentEnrollment::first();

        // Act
        GenerateAssessmentPdfJob::dispatch($enrollment);

        // Assert
        Queue::assertPushed(GenerateAssessmentPdfJob::class, function ($job) use ($enrollment) {
            return $job->enrollmentRecord->id === $enrollment->id;
        });
    }

    private function createTestData(): void
    {
        // Create general settings
        GeneralSetting::create([
            'site_name' => 'Test College',
            'school_portal_title' => 'Test College Portal',
            'support_email' => '<EMAIL>',
            'support_phone' => '************',
            'school_starting_date' => Carbon::create(2023, 8, 1),
            'school_ending_date' => Carbon::create(2024, 5, 31),
            'semester' => 1,
            'school_portal_enabled' => true,
            'online_enrollment_enabled' => true,
        ]);

        // Create test course
        $course = Course::factory()->create([
            'code' => 'TEST101',
            'name' => 'Test Course',
        ]);

        // Create test student
        $student = Student::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
        ]);

        // Create test enrollment
        StudentEnrollment::factory()->create([
            'student_id' => $student->id,
            'course_id' => $course->id,
            'student_name' => 'John Doe',
        ]);
    }
}
