<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Mail\FacultySectionTransferNotification;
use App\Mail\StudentSectionTransferNotification;
use App\Models\Classes;
use App\Models\ClassEnrollment;
use App\Models\Course;
use App\Models\Faculty;
use App\Models\GeneralSetting;
use App\Models\Student;
use App\Models\Subject;
use App\Services\GeneralSettingsService;
use App\Services\StudentTransferEmailService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

final class StudentTransferEmailNotificationTest extends TestCase
{
    use RefreshDatabase;

    private Student $student;
    private Faculty $faculty;
    private Classes $oldClass;
    private Classes $newClass;
    private StudentTransferEmailService $emailService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create general settings with portal URL
        GeneralSetting::create([
            'school_portal_url' => 'https://portal.test.edu',
            'school_starting_date' => Carbon::create(2023, 8, 1),
            'school_ending_date' => Carbon::create(2024, 5, 31),
            'semester' => 1,
        ]);

        // Create test data
        $this->createTestData();
        
        // Initialize email service
        $this->emailService = new StudentTransferEmailService(new GeneralSettingsService());
    }

    /** @test */
    public function it_sends_student_notification_email_for_section_transfer(): void
    {
        Mail::fake();

        $transferResult = [
            'student_id' => $this->student->id,
            'student_name' => $this->student->full_name,
            'old_class_id' => $this->oldClass->id,
            'new_class_id' => $this->newClass->id,
            'old_section' => $this->oldClass->section,
            'new_section' => $this->newClass->section,
            'subject_code' => $this->newClass->subject_code,
            'subject_enrollment_updated' => true,
        ];

        $results = $this->emailService->sendTransferNotifications($transferResult);

        $this->assertTrue($results['student_email_sent']);
        
        Mail::assertSent(StudentSectionTransferNotification::class, function ($mail) {
            return $mail->hasTo($this->student->email) &&
                   $mail->emailData['student_name'] === $this->student->full_name &&
                   $mail->emailData['subject_code'] === $this->newClass->subject_code &&
                   $mail->emailData['portal_url'] === 'https://portal.test.edu';
        });
    }

    /** @test */
    public function it_sends_faculty_notification_email_for_section_transfer(): void
    {
        Mail::fake();

        $transferResult = [
            'student_id' => $this->student->id,
            'student_name' => $this->student->full_name,
            'old_class_id' => $this->oldClass->id,
            'new_class_id' => $this->newClass->id,
            'old_section' => $this->oldClass->section,
            'new_section' => $this->newClass->section,
            'subject_code' => $this->newClass->subject_code,
            'subject_enrollment_updated' => true,
        ];

        $results = $this->emailService->sendTransferNotifications($transferResult);

        $this->assertTrue($results['faculty_email_sent']);
        
        Mail::assertSent(FacultySectionTransferNotification::class, function ($mail) {
            return $mail->hasTo($this->faculty->email) &&
                   $mail->emailData['faculty_name'] === $this->faculty->full_name &&
                   $mail->emailData['student_name'] === $this->student->full_name &&
                   $mail->emailData['subject_code'] === $this->newClass->subject_code &&
                   !$mail->isBulkTransfer;
        });
    }

    /** @test */
    public function it_handles_missing_student_email_gracefully(): void
    {
        Mail::fake();

        // Remove student email
        $this->student->update(['email' => null]);

        $transferResult = [
            'student_id' => $this->student->id,
            'student_name' => $this->student->full_name,
            'old_class_id' => $this->oldClass->id,
            'new_class_id' => $this->newClass->id,
            'old_section' => $this->oldClass->section,
            'new_section' => $this->newClass->section,
            'subject_code' => $this->newClass->subject_code,
            'subject_enrollment_updated' => true,
        ];

        $results = $this->emailService->sendTransferNotifications($transferResult);

        $this->assertFalse($results['student_email_sent']);
        $this->assertTrue($results['faculty_email_sent']); // Faculty email should still work
        
        Mail::assertNotSent(StudentSectionTransferNotification::class);
        Mail::assertSent(FacultySectionTransferNotification::class);
    }

    /** @test */
    public function it_handles_class_without_faculty_gracefully(): void
    {
        Mail::fake();

        // Remove faculty from new class
        $this->newClass->update(['faculty_id' => null]);

        $transferResult = [
            'student_id' => $this->student->id,
            'student_name' => $this->student->full_name,
            'old_class_id' => $this->oldClass->id,
            'new_class_id' => $this->newClass->id,
            'old_section' => $this->oldClass->section,
            'new_section' => $this->newClass->section,
            'subject_code' => $this->newClass->subject_code,
            'subject_enrollment_updated' => true,
        ];

        $results = $this->emailService->sendTransferNotifications($transferResult);

        $this->assertTrue($results['student_email_sent']);
        $this->assertFalse($results['faculty_email_sent']);
        
        Mail::assertSent(StudentSectionTransferNotification::class);
        Mail::assertNotSent(FacultySectionTransferNotification::class);
    }

    /** @test */
    public function it_sends_bulk_transfer_notifications(): void
    {
        Mail::fake();

        // Create additional students for bulk transfer
        $student2 = Student::factory()->create([
            'email' => '<EMAIL>',
            'course_id' => $this->student->course_id,
        ]);

        $bulkTransferResults = [
            'successful_transfers' => [
                [
                    'student_id' => $this->student->id,
                    'student_name' => $this->student->full_name,
                    'old_class_id' => $this->oldClass->id,
                    'new_class_id' => $this->newClass->id,
                    'old_section' => $this->oldClass->section,
                    'new_section' => $this->newClass->section,
                    'subject_code' => $this->newClass->subject_code,
                ],
                [
                    'student_id' => $student2->id,
                    'student_name' => $student2->full_name,
                    'old_class_id' => $this->oldClass->id,
                    'new_class_id' => $this->newClass->id,
                    'old_section' => $this->oldClass->section,
                    'new_section' => $this->newClass->section,
                    'subject_code' => $this->newClass->subject_code,
                ],
            ],
        ];

        $results = $this->emailService->sendBulkTransferNotifications($bulkTransferResults, $this->newClass->id);

        $this->assertEquals(2, $results['student_emails_sent']);
        $this->assertTrue($results['faculty_email_sent']);
        
        // Should send individual emails to each student
        Mail::assertSent(StudentSectionTransferNotification::class, 2);
        
        // Should send one consolidated email to faculty
        Mail::assertSent(FacultySectionTransferNotification::class, function ($mail) {
            return $mail->hasTo($this->faculty->email) &&
                   $mail->isBulkTransfer &&
                   $mail->emailData['student_count'] === 2;
        });
    }

    private function createTestData(): void
    {
        // Create course
        $course = Course::factory()->create([
            'code' => 'CS',
            'name' => 'Computer Science',
        ]);

        // Create subject
        $subject = Subject::factory()->create([
            'code' => 'CS101',
            'title' => 'Introduction to Programming',
            'course_id' => $course->id,
        ]);

        // Create faculty
        $this->faculty = Faculty::factory()->create([
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Professor',
        ]);

        // Create student
        $this->student = Student::factory()->create([
            'email' => '<EMAIL>',
            'first_name' => 'Jane',
            'last_name' => 'Student',
            'course_id' => $course->id,
        ]);

        // Create old class (without faculty)
        $this->oldClass = Classes::factory()->create([
            'subject_code' => $subject->code,
            'section' => 'A',
            'faculty_id' => null,
            'school_year' => '2023 - 2024',
            'semester' => '1',
        ]);

        // Create new class (with faculty)
        $this->newClass = Classes::factory()->create([
            'subject_code' => $subject->code,
            'section' => 'B',
            'faculty_id' => $this->faculty->id,
            'school_year' => '2023 - 2024',
            'semester' => '1',
        ]);
    }
}
