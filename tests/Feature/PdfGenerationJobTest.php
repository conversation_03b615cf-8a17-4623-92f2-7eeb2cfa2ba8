<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Jobs\GenerateAssessmentPdfJob;
use App\Models\StudentEnrollment;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class PdfGenerationJobTest extends TestCase
{
    use RefreshDatabase;

    public function test_pdf_generation_job_can_be_dispatched_with_job_id(): void
    {
        Queue::fake();

        $user = User::factory()->create();
        $enrollment = StudentEnrollment::factory()->create([
            'student_id' => $user->id,
            'status' => 'Verified By Cashier',
        ]);

        $jobId = uniqid('pdf_', true);

        GenerateAssessmentPdfJob::dispatch($enrollment, $jobId, true);

        Queue::assertPushed(GenerateAssessmentPdfJob::class, function ($job) use ($jobId) {
            return $job->getJobId() === $jobId;
        });
    }

    public function test_pdf_generation_job_updates_progress_cache(): void
    {
        $user = User::factory()->create();
        $enrollment = StudentEnrollment::factory()->create([
            'student_id' => $user->id,
            'status' => 'Verified By Cashier',
        ]);

        $jobId = uniqid('pdf_', true);

        // Mock the job progress update
        $progressData = [
            'percentage' => 100,
            'message' => 'PDF generated successfully',
            'failed' => false,
            'updated_at' => now()->toISOString(),
            'enrollment_id' => $enrollment->id,
            'type' => 'pdf_generation',
        ];

        cache()->put("pdf_job_progress:{$jobId}", $progressData, 3600);

        $cachedProgress = cache("pdf_job_progress:{$jobId}");

        $this->assertNotNull($cachedProgress);
        $this->assertEquals(100, $cachedProgress['percentage']);
        $this->assertFalse($cachedProgress['failed']);
        $this->assertEquals('PDF generated successfully', $cachedProgress['message']);
        $this->assertEquals($enrollment->id, $cachedProgress['enrollment_id']);
    }

    public function test_pdf_generation_job_progress_cache_expires(): void
    {
        $user = User::factory()->create();
        $enrollment = StudentEnrollment::factory()->create([
            'student_id' => $user->id,
            'status' => 'Verified By Cashier',
        ]);

        $jobId = uniqid('pdf_', true);

        $progressData = [
            'percentage' => 50,
            'message' => 'Processing...',
            'failed' => false,
            'updated_at' => now()->toISOString(),
            'enrollment_id' => $enrollment->id,
            'type' => 'pdf_generation',
        ];

        // Set cache with short expiration for testing
        cache()->put("pdf_job_progress:{$jobId}", $progressData, 1); // 1 second

        $this->assertNotNull(cache("pdf_job_progress:{$jobId}"));

        // Wait for cache to expire
        sleep(2);

        $this->assertNull(cache("pdf_job_progress:{$jobId}"));
    }

    public function test_pdf_generation_job_handles_failure_state(): void
    {
        $user = User::factory()->create();
        $enrollment = StudentEnrollment::factory()->create([
            'student_id' => $user->id,
            'status' => 'Verified By Cashier',
        ]);

        $jobId = uniqid('pdf_', true);

        $progressData = [
            'percentage' => 100,
            'message' => 'Failed: PDF generation error',
            'failed' => true,
            'updated_at' => now()->toISOString(),
            'enrollment_id' => $enrollment->id,
            'type' => 'pdf_generation',
        ];

        cache()->put("pdf_job_progress:{$jobId}", $progressData, 3600);

        $cachedProgress = cache("pdf_job_progress:{$jobId}");

        $this->assertNotNull($cachedProgress);
        $this->assertEquals(100, $cachedProgress['percentage']);
        $this->assertTrue($cachedProgress['failed']);
        $this->assertStringContains('Failed:', $cachedProgress['message']);
    }

    public function test_pdf_generation_creates_new_file_when_flag_is_true(): void
    {
        Queue::fake();

        $user = User::factory()->create();
        $enrollment = StudentEnrollment::factory()->create([
            'student_id' => $user->id,
            'status' => 'Verified By Cashier',
        ]);

        $jobId = uniqid('pdf_', true);

        GenerateAssessmentPdfJob::dispatch($enrollment, $jobId, true);

        Queue::assertPushed(GenerateAssessmentPdfJob::class, function ($job) {
            // We can't directly test the createNewFile flag without accessing private properties
            // but we can verify the job was dispatched correctly
            return $job->getJobId() !== null;
        });
    }

    public function test_multiple_job_ids_can_be_tracked(): void
    {
        $user = User::factory()->create();
        $enrollment = StudentEnrollment::factory()->create([
            'student_id' => $user->id,
            'status' => 'Verified By Cashier',
        ]);

        $jobId1 = uniqid('pdf_', true);
        $jobId2 = uniqid('pdf_', true);

        $progressData1 = [
            'percentage' => 50,
            'message' => 'Processing job 1...',
            'failed' => false,
            'updated_at' => now()->toISOString(),
            'enrollment_id' => $enrollment->id,
            'type' => 'pdf_generation',
        ];

        $progressData2 = [
            'percentage' => 100,
            'message' => 'Job 2 completed',
            'failed' => false,
            'updated_at' => now()->toISOString(),
            'enrollment_id' => $enrollment->id,
            'type' => 'pdf_generation',
        ];

        cache()->put("pdf_job_progress:{$jobId1}", $progressData1, 3600);
        cache()->put("pdf_job_progress:{$jobId2}", $progressData2, 3600);

        $cachedProgress1 = cache("pdf_job_progress:{$jobId1}");
        $cachedProgress2 = cache("pdf_job_progress:{$jobId2}");

        $this->assertNotNull($cachedProgress1);
        $this->assertNotNull($cachedProgress2);
        $this->assertEquals(50, $cachedProgress1['percentage']);
        $this->assertEquals(100, $cachedProgress2['percentage']);
        $this->assertNotEquals($jobId1, $jobId2);
    }
}
