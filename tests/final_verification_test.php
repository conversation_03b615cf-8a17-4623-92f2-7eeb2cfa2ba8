<?php

/**
 * Final verification test for the trailing spaces fix
 * Tests key scenarios across BSBA, BSHM, and BSIT programs
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Services\EnrollmentService;
use App\Models\Subject;
use App\Models\Classes;
use App\Models\Course;

// Bootstrap Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Final Verification Test ===\n\n";

$enrollmentService = new EnrollmentService();

// Test key courses from each program
$keyCourses = [
    // BSBA
    4 => ['name' => 'BSBA (2018 - 2019) NON-ABM', 'program' => 'BSBA'],
    8 => ['name' => 'BSBA (2024 - 2025) NON-ABM', 'program' => 'BSBA'],
    
    // BSHM
    2 => ['name' => 'BSHM (2018 - 2019) NON-ABM', 'program' => 'BSHM'],
    12 => ['name' => 'BSHM (2024 - 2025) NON-ABM', 'program' => 'BSHM'],
    
    // BSIT
    1 => ['name' => 'BSIT (2018 - 2019) WEB', 'program' => 'BSIT'],
    10 => ['name' => 'BSIT (2024 - 2025) WEB', 'program' => 'BSIT']
];

echo "1. Testing enrollment functionality across programs:\n\n";

$results = [];

foreach ($keyCourses as $courseId => $courseInfo) {
    echo "Testing {$courseInfo['program']} - Course {$courseId}: {$courseInfo['name']}\n";
    
    try {
        $options = $enrollmentService->getSubjectDropdownOptions(
            courseId: $courseId,
            studentId: 1,
            semester: 1,
            schoolYear: '2025 - 2026'
        );
        
        $total = count($options);
        $enabled = 0;
        $withClasses = 0;
        
        foreach ($options as $option) {
            if (!$option['disabled']) $enabled++;
            if (str_contains($option['label'], '⭐')) $withClasses++;
        }
        
        $results[$courseId] = [
            'total' => $total,
            'enabled' => $enabled,
            'withClasses' => $withClasses,
            'success' => true
        ];
        
        echo "  ✅ Success: {$total} subjects, {$enabled} enabled, {$withClasses} with classes\n";
        
    } catch (Exception $e) {
        $results[$courseId] = ['success' => false, 'error' => $e->getMessage()];
        echo "  ❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "2. Summary by program:\n\n";

$programSummary = ['BSBA' => [], 'BSHM' => [], 'BSIT' => []];
foreach ($keyCourses as $courseId => $courseInfo) {
    if (isset($results[$courseId]) && $results[$courseId]['success']) {
        $programSummary[$courseInfo['program']][] = $results[$courseId];
    }
}

foreach ($programSummary as $program => $programResults) {
    if (!empty($programResults)) {
        $avgTotal = array_sum(array_column($programResults, 'total')) / count($programResults);
        $avgEnabled = array_sum(array_column($programResults, 'enabled')) / count($programResults);
        $avgWithClasses = array_sum(array_column($programResults, 'withClasses')) / count($programResults);
        
        echo "{$program}:\n";
        echo "  Average subjects per course: " . round($avgTotal, 1) . "\n";
        echo "  Average enabled per course: " . round($avgEnabled, 1) . "\n";
        echo "  Average with classes per course: " . round($avgWithClasses, 1) . "\n";
        echo "  Success rate: " . count($programResults) . "/" . count(array_filter($keyCourses, fn($c) => $c['program'] === $program)) . "\n\n";
    }
}

echo "3. Checking remaining trailing space issues:\n\n";

$remainingIssues = Subject::whereRaw('code != TRIM(code)')->get();
if ($remainingIssues->count() > 0) {
    echo "Found {$remainingIssues->count()} subjects with trailing spaces:\n";
    foreach ($remainingIssues as $subject) {
        $course = Course::find($subject->course_id);
        $program = 'OTHER';
        if ($course) {
            if (str_contains($course->code, 'BSBA')) $program = 'BSBA';
            elseif (str_contains($course->code, 'BSHM')) $program = 'BSHM';
            elseif (str_contains($course->code, 'BSIT')) $program = 'BSIT';
        }
        echo "  {$program}: Subject ID {$subject->id}, Code: '{$subject->code}'\n";
        
        // Test if this subject appears correctly in enrollment
        if ($course) {
            try {
                $options = $enrollmentService->getSubjectDropdownOptions(
                    courseId: $subject->course_id,
                    studentId: 1,
                    semester: 1,
                    schoolYear: '2025 - 2026'
                );
                
                $found = array_key_exists($subject->id, $options);
                if ($found) {
                    $status = $options[$subject->id]['disabled'] ? 'DISABLED' : 'ENABLED';
                    echo "    ✅ Appears in dropdown: {$status}\n";
                } else {
                    echo "    ❌ Missing from dropdown\n";
                }
            } catch (Exception $e) {
                echo "    ❌ Error testing: " . $e->getMessage() . "\n";
            }
        }
    }
} else {
    echo "✅ No subjects with trailing spaces found\n";
}

echo "\n4. Overall assessment:\n\n";

$totalTests = count($keyCourses);
$successfulTests = count(array_filter($results, fn($r) => $r['success']));
$successRate = ($successfulTests / $totalTests) * 100;

echo "Test Results:\n";
echo "  Total courses tested: {$totalTests}\n";
echo "  Successful tests: {$successfulTests}\n";
echo "  Success rate: " . round($successRate, 1) . "%\n\n";

if ($successRate >= 100) {
    echo "🎉 ALL TESTS PASSED! The trailing spaces fix is working correctly across all programs.\n";
} elseif ($successRate >= 80) {
    echo "⚠️  Most tests passed, but some issues remain. Review the errors above.\n";
} else {
    echo "❌ Multiple issues found. The fix may need additional work.\n";
}

echo "\n=== Verification Complete ===\n";
