<?php

/**
 * Test script to verify the subject enrollment fix for trailing spaces
 * 
 * This script tests the fix for the issue where subjects with similar codes
 * but different trailing spaces (like "THC 1" vs "THC 1 ") weren't showing
 * up properly in the enrollment dropdown.
 * 
 * Run with: php tests/test_subject_enrollment_fix.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Services\EnrollmentService;
use App\Models\Subject;
use App\Models\Classes;
use App\Models\Course;

// Bootstrap Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Subject Enrollment Fix Test ===\n\n";

// Test data
$testCourses = [
    11 => 'BSHM (2024 - 2025) ABM',
    12 => 'BSHM (2024 - 2025) NON-ABM',
    2 => 'BSHM (2018 - 2019) NON-ABM',
    3 => 'BSHM (2018 - 2019) ABM'
];

$enrollmentService = new EnrollmentService();

echo "1. Testing THC 1 subjects after cleanup:\n";
$thcSubjects = Subject::where('code', 'THC 1')->get(['id', 'code', 'title', 'course_id']);
foreach ($thcSubjects as $subject) {
    $course = Course::find($subject->course_id);
    $courseName = $course ? $course->code : 'Unknown';
    echo "   - Subject ID: {$subject->id}, Code: '{$subject->code}', Course: {$courseName}\n";
}

echo "\n2. Testing THC 1 class availability:\n";
$thcClass = Classes::where('subject_code', 'THC 1')
    ->where('school_year', '2025 - 2026')
    ->where('semester', 1)
    ->first();

if ($thcClass) {
    echo "   - Class ID: {$thcClass->id}, Subject Code: '{$thcClass->subject_code}'\n";
    echo "   - Available for courses: " . json_encode($thcClass->course_codes) . "\n";
    echo "   - Section: {$thcClass->section}\n";
} else {
    echo "   - No THC 1 class found!\n";
}

echo "\n3. Testing enrollment dropdown for each course:\n";
foreach ($testCourses as $courseId => $courseName) {
    echo "   Course {$courseId} ({$courseName}):\n";
    
    $options = $enrollmentService->getSubjectDropdownOptions(
        courseId: $courseId,
        studentId: 1, // Dummy student ID
        semester: 1,
        schoolYear: '2025 - 2026'
    );
    
    $thcFound = false;
    foreach ($options as $subjectId => $option) {
        if (str_contains($option['label'], 'THC 1')) {
            $status = $option['disabled'] ? 'DISABLED' : 'ENABLED';
            $star = str_contains($option['label'], '⭐') ? 'HAS CLASS' : 'NO CLASS';
            echo "     ✓ Subject ID: {$subjectId}, Status: {$status}, {$star}\n";
            echo "       Label: {$option['label']}\n";
            $thcFound = true;
        }
    }
    
    if (!$thcFound) {
        echo "     ✗ THC 1 not found in dropdown!\n";
    }
    echo "\n";
}

echo "4. Testing section selection for THC 1:\n";
foreach ([11, 12] as $courseId) {
    $courseName = $testCourses[$courseId];
    echo "   Course {$courseId} ({$courseName}):\n";
    
    // Find the THC 1 subject for this course
    $subject = Subject::where('code', 'THC 1')->where('course_id', $courseId)->first();
    
    if ($subject) {
        $classes = Classes::where('school_year', '2025 - 2026')
            ->where('semester', 1)
            ->whereRaw('LOWER(TRIM(subject_code)) = LOWER(TRIM(?))', [$subject->code])
            ->whereJsonContains('course_codes', (string) $courseId)
            ->get(['id', 'section', 'subject_code']);
        
        if ($classes->count() > 0) {
            foreach ($classes as $class) {
                echo "     ✓ Class ID: {$class->id}, Section: {$class->section}\n";
            }
        } else {
            echo "     ✗ No sections found!\n";
        }
    } else {
        echo "     ✗ THC 1 subject not found for this course!\n";
    }
    echo "\n";
}

echo "=== Test Complete ===\n";
echo "If all tests show ✓ marks, the fix is working correctly!\n";
