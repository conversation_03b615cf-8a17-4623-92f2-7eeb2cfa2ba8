<?php

/**
 * Comprehensive test for subject enrollment across all programs
 * Tests BSBA, BSHM, and BSIT programs to ensure trailing space fix works universally
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Services\EnrollmentService;
use App\Models\Subject;
use App\Models\Classes;
use App\Models\Course;

// Bootstrap Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Comprehensive Enrollment Test Across All Programs ===\n\n";

// Get all courses grouped by program
$allCourses = Course::orderBy('code')->get();
$programCourses = [
    'BSBA' => [],
    'BSHM' => [],
    'BSIT' => [],
    'OTHER' => []
];

foreach ($allCourses as $course) {
    $program = 'OTHER';
    if (str_contains($course->code, 'BSBA')) $program = 'BSBA';
    elseif (str_contains($course->code, 'BSHM')) $program = 'BSHM';
    elseif (str_contains($course->code, 'BSIT')) $program = 'BSIT';
    
    $programCourses[$program][] = $course;
}

echo "1. Program Overview:\n";
foreach ($programCourses as $program => $courses) {
    echo "   {$program}: " . count($courses) . " courses\n";
}

// Check for remaining subjects with trailing spaces
echo "\n2. Remaining subjects with trailing spaces:\n";
$subjectsWithSpaces = Subject::whereRaw('code != TRIM(code)')->get();
if ($subjectsWithSpaces->count() > 0) {
    foreach ($subjectsWithSpaces as $subject) {
        $course = Course::find($subject->course_id);
        $courseName = $course ? $course->code : 'Unknown';
        echo "   ⚠️  Subject ID: {$subject->id}, Code: '{$subject->code}', Course: {$courseName}\n";
    }
} else {
    echo "   ✅ No subjects with trailing spaces found\n";
}

// Check classes for current semester
echo "\n3. Available classes for 2025-2026 Semester 1:\n";
$classes = Classes::where('school_year', '2025 - 2026')
    ->where('semester', 1)
    ->get();

echo "   Total classes: " . $classes->count() . "\n";

// Group classes by program
$classesByProgram = ['BSBA' => 0, 'BSHM' => 0, 'BSIT' => 0, 'OTHER' => 0];
foreach ($classes as $class) {
    $courseIds = is_array($class->course_codes) ? $class->course_codes : json_decode($class->course_codes, true);
    if ($courseIds) {
        foreach ($courseIds as $courseId) {
            $course = Course::find($courseId);
            if ($course) {
                if (str_contains($course->code, 'BSBA')) $classesByProgram['BSBA']++;
                elseif (str_contains($course->code, 'BSHM')) $classesByProgram['BSHM']++;
                elseif (str_contains($course->code, 'BSIT')) $classesByProgram['BSIT']++;
                else $classesByProgram['OTHER']++;
            }
        }
    }
}

foreach ($classesByProgram as $program => $count) {
    echo "   {$program}: {$count} class enrollments available\n";
}

// Test enrollment service for key courses from each program
echo "\n4. Testing enrollment functionality:\n";

$enrollmentService = new EnrollmentService();

// Test representative courses from each program
$testCourses = [
    // BSBA
    4 => 'BSBA (2018 - 2019) NON-ABM',
    8 => 'BSBA (2024 - 2025) NON-ABM',
    
    // BSHM  
    2 => 'BSHM (2018 - 2019) NON-ABM',
    12 => 'BSHM (2024 - 2025) NON-ABM',
    
    // BSIT
    1 => 'BSIT (2018 - 2019) WEB',
    10 => 'BSIT (2024 - 2025) WEB'
];

foreach ($testCourses as $courseId => $courseName) {
    echo "\n   Testing Course {$courseId}: {$courseName}\n";
    
    try {
        $options = $enrollmentService->getSubjectDropdownOptions(
            courseId: $courseId,
            studentId: 1,
            semester: 1,
            schoolYear: '2025 - 2026'
        );
        
        $total = count($options);
        $enabled = 0;
        $withClasses = 0;
        
        foreach ($options as $option) {
            if (!$option['disabled']) $enabled++;
            if (str_contains($option['label'], '⭐')) $withClasses++;
        }
        
        echo "     ✅ Total subjects: {$total}, Enabled: {$enabled}, With classes: {$withClasses}\n";
        
        // Show a few sample subjects
        $count = 0;
        foreach ($options as $subjectId => $option) {
            if ($count < 2) {
                $status = $option['disabled'] ? 'DISABLED' : 'ENABLED';
                $label = substr($option['label'], 0, 50) . (strlen($option['label']) > 50 ? '...' : '');
                echo "       - {$status}: {$label}\n";
                $count++;
            }
        }
        
    } catch (Exception $e) {
        echo "     ❌ Error: " . $e->getMessage() . "\n";
    }
}

echo "\n5. Testing specific subjects that had trailing space issues:\n";

// Test the remaining subjects with trailing spaces
foreach ($subjectsWithSpaces as $subject) {
    echo "\n   Testing Subject ID {$subject->id}: '{$subject->code}'\n";
    
    $course = Course::find($subject->course_id);
    if ($course) {
        try {
            $options = $enrollmentService->getSubjectDropdownOptions(
                courseId: $subject->course_id,
                studentId: 1,
                semester: 1,
                schoolYear: '2025 - 2026'
            );
            
            $found = false;
            foreach ($options as $subjectId => $option) {
                if ($subjectId == $subject->id) {
                    $status = $option['disabled'] ? 'DISABLED' : 'ENABLED';
                    $hasClass = str_contains($option['label'], '⭐') ? 'HAS CLASS' : 'NO CLASS';
                    echo "     ✅ Found in dropdown: {$status}, {$hasClass}\n";
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                echo "     ❌ Not found in dropdown\n";
            }
            
        } catch (Exception $e) {
            echo "     ❌ Error: " . $e->getMessage() . "\n";
        }
    }
}

echo "\n=== Test Complete ===\n";
echo "✅ = Working correctly\n";
echo "⚠️  = Needs attention\n"; 
echo "❌ = Error/Issue\n";
