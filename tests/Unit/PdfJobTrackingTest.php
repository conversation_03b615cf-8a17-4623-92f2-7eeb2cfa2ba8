<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Jobs\GenerateAssessmentPdfJob;
use App\Models\StudentEnrollment;
use Illuminate\Foundation\Testing\TestCase;
use Illuminate\Support\Facades\Cache;

class PdfJobTrackingTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        Cache::flush();
    }

    public function test_job_id_generation_is_unique(): void
    {
        $jobId1 = uniqid('pdf_', true);
        $jobId2 = uniqid('pdf_', true);

        $this->assertNotEquals($jobId1, $jobId2);
        $this->assertStringStartsWith('pdf_', $jobId1);
        $this->assertStringStartsWith('pdf_', $jobId2);
    }

    public function test_progress_data_structure_is_correct(): void
    {
        $jobId = uniqid('pdf_', true);
        $enrollmentId = 123;

        $progressData = [
            'percentage' => 50,
            'message' => 'Processing PDF generation...',
            'failed' => false,
            'updated_at' => now()->toISOString(),
            'enrollment_id' => $enrollmentId,
            'type' => 'pdf_generation',
        ];

        Cache::put("pdf_job_progress:{$jobId}", $progressData, 3600);

        $cached = Cache::get("pdf_job_progress:{$jobId}");

        $this->assertIsArray($cached);
        $this->assertEquals(50, $cached['percentage']);
        $this->assertEquals('Processing PDF generation...', $cached['message']);
        $this->assertFalse($cached['failed']);
        $this->assertEquals($enrollmentId, $cached['enrollment_id']);
        $this->assertEquals('pdf_generation', $cached['type']);
        $this->assertArrayHasKey('updated_at', $cached);
    }

    public function test_job_completion_detection(): void
    {
        $jobId = uniqid('pdf_', true);

        // Test in-progress job
        $inProgressData = [
            'percentage' => 75,
            'message' => 'Almost complete...',
            'failed' => false,
            'updated_at' => now()->toISOString(),
            'enrollment_id' => 123,
            'type' => 'pdf_generation',
        ];

        Cache::put("pdf_job_progress:{$jobId}", $inProgressData, 3600);
        $cached = Cache::get("pdf_job_progress:{$jobId}");
        $this->assertNotEquals(100, $cached['percentage']);

        // Test completed job
        $completedData = [
            'percentage' => 100,
            'message' => 'PDF generated successfully',
            'failed' => false,
            'updated_at' => now()->toISOString(),
            'enrollment_id' => 123,
            'type' => 'pdf_generation',
        ];

        Cache::put("pdf_job_progress:{$jobId}", $completedData, 3600);
        $cached = Cache::get("pdf_job_progress:{$jobId}");
        $this->assertEquals(100, $cached['percentage']);
        $this->assertFalse($cached['failed']);
    }

    public function test_job_failure_detection(): void
    {
        $jobId = uniqid('pdf_', true);

        $failedData = [
            'percentage' => 100,
            'message' => 'Failed: Unable to generate PDF',
            'failed' => true,
            'updated_at' => now()->toISOString(),
            'enrollment_id' => 123,
            'type' => 'pdf_generation',
        ];

        Cache::put("pdf_job_progress:{$jobId}", $failedData, 3600);
        $cached = Cache::get("pdf_job_progress:{$jobId}");

        $this->assertEquals(100, $cached['percentage']);
        $this->assertTrue($cached['failed']);
        $this->assertStringContains('Failed:', $cached['message']);
    }

    public function test_multiple_jobs_can_be_tracked(): void
    {
        $jobIds = [
            uniqid('pdf_', true),
            uniqid('pdf_', true),
            uniqid('pdf_', true),
        ];

        $progressStates = [
            ['percentage' => 30, 'failed' => false, 'message' => 'Starting...'],
            ['percentage' => 75, 'failed' => false, 'message' => 'Processing...'],
            ['percentage' => 100, 'failed' => false, 'message' => 'Complete!'],
        ];

        // Store progress for each job
        foreach ($jobIds as $index => $jobId) {
            $progressData = array_merge($progressStates[$index], [
                'updated_at' => now()->toISOString(),
                'enrollment_id' => 100 + $index,
                'type' => 'pdf_generation',
            ]);

            Cache::put("pdf_job_progress:{$jobId}", $progressData, 3600);
        }

        // Verify all jobs are tracked
        $completedJobs = 0;
        $inProgressJobs = 0;

        foreach ($jobIds as $jobId) {
            $cached = Cache::get("pdf_job_progress:{$jobId}");
            $this->assertNotNull($cached);

            if ($cached['percentage'] === 100 && !$cached['failed']) {
                $completedJobs++;
            } else {
                $inProgressJobs++;
            }
        }

        $this->assertEquals(1, $completedJobs);
        $this->assertEquals(2, $inProgressJobs);
    }

    public function test_cache_cleanup_works(): void
    {
        $jobId = uniqid('pdf_', true);

        $progressData = [
            'percentage' => 100,
            'message' => 'PDF generated successfully',
            'failed' => false,
            'updated_at' => now()->toISOString(),
            'enrollment_id' => 123,
            'type' => 'pdf_generation',
        ];

        Cache::put("pdf_job_progress:{$jobId}", $progressData, 3600);
        $this->assertNotNull(Cache::get("pdf_job_progress:{$jobId}"));

        // Simulate cleanup
        Cache::forget("pdf_job_progress:{$jobId}");
        $this->assertNull(Cache::get("pdf_job_progress:{$jobId}"));
    }

    public function test_job_tracking_array_management(): void
    {
        $trackingJobIds = [];
        $jobId1 = uniqid('pdf_', true);
        $jobId2 = uniqid('pdf_', true);

        // Add jobs to tracking
        $trackingJobIds[] = $jobId1;
        $trackingJobIds[] = $jobId2;

        $this->assertCount(2, $trackingJobIds);
        $this->assertContains($jobId1, $trackingJobIds);
        $this->assertContains($jobId2, $trackingJobIds);

        // Remove completed job
        $index = array_search($jobId1, $trackingJobIds);
        if ($index !== false) {
            unset($trackingJobIds[$index]);
        }

        $this->assertCount(1, $trackingJobIds);
        $this->assertNotContains($jobId1, $trackingJobIds);
        $this->assertContains($jobId2, $trackingJobIds);
    }

    public function test_polling_should_continue_with_active_jobs(): void
    {
        $trackingJobIds = [
            uniqid('pdf_', true),
            uniqid('pdf_', true),
        ];

        // Simulate one completed, one in progress
        $completedJobId = $trackingJobIds[0];
        $inProgressJobId = $trackingJobIds[1];

        Cache::put("pdf_job_progress:{$completedJobId}", [
            'percentage' => 100,
            'message' => 'Complete',
            'failed' => false,
            'updated_at' => now()->toISOString(),
            'enrollment_id' => 123,
            'type' => 'pdf_generation',
        ], 3600);

        Cache::put("pdf_job_progress:{$inProgressJobId}", [
            'percentage' => 60,
            'message' => 'In progress',
            'failed' => false,
            'updated_at' => now()->toISOString(),
            'enrollment_id' => 124,
            'type' => 'pdf_generation',
        ], 3600);

        $remainingJobs = [];
        foreach ($trackingJobIds as $index => $jobId) {
            $progressData = Cache::get("pdf_job_progress:{$jobId}");
            if (!$progressData || $progressData['percentage'] !== 100) {
                $remainingJobs[] = $jobId;
            }
        }

        $this->assertCount(1, $remainingJobs);
        $this->assertContains($inProgressJobId, $remainingJobs);
        $this->assertNotContains($completedJobId, $remainingJobs);
    }

    public function test_job_instance_creates_unique_id(): void
    {
        // Mock StudentEnrollment without database
        $enrollment = new class {
            public int $id = 123;
            public string $student_id = 'TEST_ID';
            public string $status = 'Verified By Cashier';
        };

        $jobId1 = uniqid('pdf_', true);
        $jobId2 = uniqid('pdf_', true);

        // Test that job IDs are unique
        $this->assertNotEquals($jobId1, $jobId2);
        $this->assertStringStartsWith('pdf_', $jobId1);
        $this->assertStringStartsWith('pdf_', $jobId2);
    }
}
