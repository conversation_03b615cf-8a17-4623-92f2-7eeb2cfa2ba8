<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Mail\FacultySectionTransferNotification;
use App\Mail\StudentSectionTransferNotification;
use App\Models\Classes;
use App\Models\Faculty;
use App\Models\Student;
use App\Services\GeneralSettingsService;
use App\Services\StudentTransferEmailService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Mockery;
use Tests\TestCase;

final class StudentTransferEmailServiceTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_be_instantiated(): void
    {
        $settingsService = Mockery::mock(GeneralSettingsService::class);
        $emailService = new StudentTransferEmailService($settingsService);
        
        $this->assertInstanceOf(StudentTransferEmailService::class, $emailService);
    }

    /** @test */
    public function it_handles_missing_data_gracefully(): void
    {
        Mail::fake();
        
        $settingsService = Mockery::mock(GeneralSettingsService::class);
        $settingsService->shouldReceive('getStudentPortalUrl')->andReturn('https://portal.test.edu');
        $settingsService->shouldReceive('getCurrentSchoolYearString')->andReturn('2023 - 2024');
        $settingsService->shouldReceive('getAvailableSemesters')->andReturn([1 => '1st Semester']);
        $settingsService->shouldReceive('getCurrentSemester')->andReturn(1);
        
        $emailService = new StudentTransferEmailService($settingsService);

        $transferResult = [
            'student_id' => 999, // Non-existent student
            'student_name' => 'Test Student',
            'old_class_id' => 999, // Non-existent class
            'new_class_id' => 999, // Non-existent class
            'old_section' => 'A',
            'new_section' => 'B',
            'subject_code' => 'TEST101',
            'subject_enrollment_updated' => true,
        ];

        $results = $emailService->sendTransferNotifications($transferResult);

        $this->assertFalse($results['student_email_sent']);
        $this->assertFalse($results['faculty_email_sent']);
        
        Mail::assertNothingSent();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
