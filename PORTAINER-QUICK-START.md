# DCCP Admin V2 - <PERSON><PERSON>r Quick Start

## 🚀 5-Minute Portainer Deployment

### Prerequisites
- Portainer instance running
- External PostgreSQL database
- External Redis instance

### Step 1: Prepare Image
```bash
# Build and prepare
./portainer-deploy.sh prepare

# OR manually:
docker build --target production -t dccp-admin:latest .
docker save dccp-admin:latest | gzip > dccp-admin.tar.gz
```

### Step 2: Load Image in Portainer
```bash
# On Portainer host:
docker load < dccp-admin.tar.gz
```

### Step 3: Create Stack in Portainer
1. Open Portainer → **Stacks** → **Add stack**
2. Name: `dccp-admin`
3. **Web editor**: Copy `portainer-external-stack.yml`
4. **Environment variables**: Configure below
5. **Deploy the stack**

### Step 4: Required Environment Variables
```
APP_NAME=DCCP Admin V2
APP_KEY=base64:your_generated_key_here
APP_URL=https://your-domain.com
APP_PORT=8000

DB_HOST=your-postgres-host
DB_PORT=5432
DB_DATABASE=dccpadminv2
DB_USERNAME=dccp_user
DB_PASSWORD=your_secure_password

REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=your_username
MAIL_PASSWORD=your_password
MAIL_FROM_ADDRESS=<EMAIL>
```

### Step 5: Post-Deployment Setup
Access container console in Portainer:
```bash
# Run migrations
php artisan migrate --force

# Optimize application
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Create storage link
php artisan storage:link
```

### Step 6: Verify Deployment
- **Health check**: `http://your-domain:8000/health`
- **Application**: `http://your-domain:8000`
- **Portainer logs**: Check container logs for errors

## 🔧 Quick Commands

### Generate App Key
```bash
./portainer-deploy.sh generate-key
```

### Validate Configuration
```bash
./portainer-deploy.sh validate-env
```

### Update Application
1. Build new image: `./portainer-deploy.sh build`
2. Update stack in Portainer
3. Redeploy stack
4. Run migrations if needed

## 📋 Troubleshooting

| Issue | Solution |
|-------|----------|
| Container won't start | Check environment variables and logs |
| Database connection failed | Verify DB_HOST, credentials, and network access |
| Redis connection failed | Check REDIS_HOST and authentication |
| Health check fails | Ensure port 8000 is accessible |
| Image not found | Verify image is loaded: `docker images` |

## 🔗 Stack Files

- **External services**: `portainer-external-stack.yml`
- **With services**: `portainer-stack.yml`
- **Environment**: `portainer.env`

## 📞 Quick Support

```bash
# View logs in Portainer
Container → dccp-admin-app → Logs

# Access console
Container → dccp-admin-app → Console

# Test connectivity
nc -z postgres-host 5432
nc -z redis-host 6379
```

**🎉 Your DCCP Admin V2 is now running in Portainer!**