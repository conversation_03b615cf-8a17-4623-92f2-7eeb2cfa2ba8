#!/bin/bash

# DCCP Admin V2 - Application Monitoring Script
# This script monitors the health and performance of the deployed application

set -e

# Configuration
APP_URL="${APP_URL:-http://localhost:8000}"
HEALTH_ENDPOINT="$APP_URL/health"
CONTAINER_NAME="dccp-admin-app"
LOG_FILE="/tmp/dccp-admin-monitor.log"
ALERT_EMAIL="${ALERT_EMAIL:-}"
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Functions
log_with_timestamp() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    log_with_timestamp "INFO: $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
    log_with_timestamp "SUCCESS: $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
    log_with_timestamp "WARNING: $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
    log_with_timestamp "ERROR: $1"
}

send_alert() {
    local message="$1"
    local severity="$2"
    
    if [ -n "$ALERT_EMAIL" ]; then
        echo "$message" | mail -s "DCCP Admin Alert: $severity" "$ALERT_EMAIL" 2>/dev/null || true
    fi
    
    if [ -n "$SLACK_WEBHOOK" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 DCCP Admin Alert: $severity\\n$message\"}" \
            "$SLACK_WEBHOOK" 2>/dev/null || true
    fi
}

check_application_health() {
    log_info "Checking application health..."
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$HEALTH_ENDPOINT" 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        local status=$(cat /tmp/health_response.json 2>/dev/null | grep -o '"status":"[^"]*"' | cut -d'"' -f4 2>/dev/null || echo "unknown")
        
        if [ "$status" = "healthy" ]; then
            log_success "Application is healthy"
            return 0
        else
            log_error "Application reports unhealthy status: $status"
            send_alert "Application health check failed. Status: $status" "CRITICAL"
            return 1
        fi
    else
        log_error "Health check failed with HTTP status: $response"
        send_alert "Application health check failed. HTTP status: $response" "CRITICAL"
        return 1
    fi
}

check_container_status() {
    log_info "Checking container status..."
    
    if docker ps --filter "name=$CONTAINER_NAME" --filter "status=running" --quiet | grep -q .; then
        log_success "Container is running"
        return 0
    else
        log_error "Container is not running"
        send_alert "Docker container $CONTAINER_NAME is not running" "CRITICAL"
        return 1
    fi
}

check_container_resources() {
    log_info "Checking container resource usage..."
    
    if docker ps --filter "name=$CONTAINER_NAME" --quiet | grep -q .; then
        local stats=$(docker stats --no-stream --format "table {{.CPUPerc}}\t{{.MemUsage}}" "$CONTAINER_NAME" 2>/dev/null | tail -n 1)
        
        if [ -n "$stats" ]; then
            local cpu=$(echo "$stats" | awk '{print $1}' | tr -d '%')
            local memory=$(echo "$stats" | awk '{print $2}')
            
            log_info "Container resources - CPU: ${cpu}%, Memory: ${memory}"
            
            # Alert if CPU usage is above 90%
            if [ $(echo "$cpu > 90" | bc -l 2>/dev/null || echo 0) -eq 1 ]; then
                log_warning "High CPU usage: ${cpu}%"
                send_alert "High CPU usage detected: ${cpu}%" "WARNING"
            fi
            
            return 0
        else
            log_warning "Could not retrieve container stats"
            return 1
        fi
    else
        log_error "Container not found"
        return 1
    fi
}

check_disk_space() {
    log_info "Checking disk space..."
    
    local usage=$(df / | tail -1 | awk '{print $5}' | tr -d '%')
    
    if [ "$usage" -gt 90 ]; then
        log_error "Disk usage is critical: ${usage}%"
        send_alert "Disk usage is critical: ${usage}%" "CRITICAL"
        return 1
    elif [ "$usage" -gt 80 ]; then
        log_warning "Disk usage is high: ${usage}%"
        send_alert "Disk usage is high: ${usage}%" "WARNING"
        return 0
    else
        log_success "Disk usage is normal: ${usage}%"
        return 0
    fi
}

check_database_connectivity() {
    log_info "Checking database connectivity..."
    
    if docker exec "$CONTAINER_NAME" php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database OK';" 2>/dev/null | grep -q "Database OK"; then
        log_success "Database connection is working"
        return 0
    else
        log_error "Database connection failed"
        send_alert "Database connection failed" "CRITICAL"
        return 1
    fi
}

check_redis_connectivity() {
    log_info "Checking Redis connectivity..."
    
    if docker exec "$CONTAINER_NAME" php artisan tinker --execute="Cache::store('redis')->put('monitor_test', 'ok', 60); echo Cache::store('redis')->get('monitor_test');" 2>/dev/null | grep -q "ok"; then
        log_success "Redis connection is working"
        return 0
    else
        log_error "Redis connection failed"
        send_alert "Redis connection failed" "CRITICAL"
        return 1
    fi
}

check_storage_permissions() {
    log_info "Checking storage permissions..."
    
    if docker exec "$CONTAINER_NAME" test -w /app/storage/logs; then
        log_success "Storage is writable"
        return 0
    else
        log_error "Storage is not writable"
        send_alert "Storage directory is not writable" "WARNING"
        return 1
    fi
}

check_application_logs() {
    log_info "Checking for application errors..."
    
    local error_count=$(docker logs "$CONTAINER_NAME" --since="5m" 2>&1 | grep -i "error\|exception\|fatal" | wc -l)
    
    if [ "$error_count" -gt 0 ]; then
        log_warning "Found $error_count errors in the last 5 minutes"
        
        if [ "$error_count" -gt 10 ]; then
            send_alert "High error rate detected: $error_count errors in the last 5 minutes" "WARNING"
        fi
        
        return 1
    else
        log_success "No recent errors found"
        return 0
    fi
}

get_application_metrics() {
    log_info "Gathering application metrics..."
    
    echo "=== Application Metrics ==="
    echo "Timestamp: $(date)"
    echo "Health Endpoint: $HEALTH_ENDPOINT"
    
    # Response time
    local response_time=$(curl -w "%{time_total}" -s -o /dev/null "$APP_URL" 2>/dev/null || echo "N/A")
    echo "Response Time: ${response_time}s"
    
    # Container uptime
    if docker ps --filter "name=$CONTAINER_NAME" --format "{{.Status}}" | grep -q "Up"; then
        local uptime=$(docker ps --filter "name=$CONTAINER_NAME" --format "{{.Status}}")
        echo "Container Status: $uptime"
    fi
    
    # Memory usage
    if command -v free >/dev/null 2>&1; then
        echo "System Memory:"
        free -h
    fi
    
    echo "=========================="
}

cleanup_logs() {
    # Keep only last 1000 lines of log file
    if [ -f "$LOG_FILE" ] && [ $(wc -l < "$LOG_FILE") -gt 1000 ]; then
        tail -n 1000 "$LOG_FILE" > "${LOG_FILE}.tmp"
        mv "${LOG_FILE}.tmp" "$LOG_FILE"
    fi
}

show_help() {
    echo "DCCP Admin V2 - Application Monitoring Script"
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  monitor      Run all health checks (default)"
    echo "  health       Check application health endpoint"
    echo "  container    Check container status"
    echo "  resources    Check container resource usage"
    echo "  database     Check database connectivity"
    echo "  redis        Check Redis connectivity"
    echo "  storage      Check storage permissions"
    echo "  logs         Check for application errors"
    echo "  metrics      Show application metrics"
    echo "  watch        Continuous monitoring (every 30 seconds)"
    echo "  help         Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  APP_URL           Application URL (default: http://localhost:8000)"
    echo "  ALERT_EMAIL       Email address for alerts"
    echo "  SLACK_WEBHOOK     Slack webhook URL for alerts"
}

run_all_checks() {
    log_info "Starting comprehensive health monitoring..."
    echo ""
    
    local failures=0
    
    check_container_status || failures=$((failures + 1))
    check_application_health || failures=$((failures + 1))
    check_database_connectivity || failures=$((failures + 1))
    check_redis_connectivity || failures=$((failures + 1))
    check_storage_permissions || failures=$((failures + 1))
    check_container_resources || true  # Don't count as failure
    check_disk_space || failures=$((failures + 1))
    check_application_logs || true  # Don't count as failure
    
    echo ""
    if [ $failures -eq 0 ]; then
        log_success "All health checks passed!"
    else
        log_error "$failures health check(s) failed"
    fi
    
    echo ""
    get_application_metrics
    cleanup_logs
    
    return $failures
}

# Main script logic
case "${1:-monitor}" in
    "monitor")
        run_all_checks
        ;;
    "health")
        check_application_health
        ;;
    "container")
        check_container_status
        ;;
    "resources")
        check_container_resources
        ;;
    "database")
        check_database_connectivity
        ;;
    "redis")
        check_redis_connectivity
        ;;
    "storage")
        check_storage_permissions
        ;;
    "logs")
        check_application_logs
        ;;
    "metrics")
        get_application_metrics
        ;;
    "watch")
        log_info "Starting continuous monitoring (Ctrl+C to stop)..."
        while true; do
            run_all_checks
            echo ""
            log_info "Waiting 30 seconds for next check..."
            sleep 30
            echo "----------------------------------------"
        done
        ;;
    "help"|*)
        show_help
        ;;
esac