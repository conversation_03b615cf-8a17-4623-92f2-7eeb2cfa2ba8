# DCCP Admin API Documentation

This document provides information about the DCCP Admin API endpoints and how to use them.

## Authentication

Currently, the general settings endpoint is publicly accessible with no authentication required.

## API Endpoints

### General Settings

```
GET /api/v1/settings
```

Returns basic information about the current school year, semester, and system settings.

#### Example Response

```json
{
  "data": {
    "school_year": "2023-2024",
    "school_year_string": "2023 - 2024",
    "semester": "1st Semester",
    "school_portal_url": "https://portal.example.edu",
    "school_portal_enabled": true,
    "online_enrollment_enabled": true,
    "features": {
      "enable_grades": true,
      "enable_enrollment": true 
    },
    "curriculum_year": "2023"
  }
}
```

## Interactive Documentation

For interactive API documentation, visit `/docs/api` when the application is running.

## Rate Limiting

The API has standard Laravel rate limiting applied. Please ensure your application doesn't make excessive requests.

## Caching

The API response is cached for 1 hour to improve performance. If you need the most up-to-date information, the cache is automatically cleared when settings are updated through the admin interface. 