{"mcpServers": {"github.com/modelcontextprotocol/servers/tree/main/src/postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://postgres:postgress@localhost/postgres"], "disabled": false, "autoApprove": []}, "github.com/modelcontextprotocol/servers/tree/main/src/filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/documents/Cline/MCP"], "disabled": false, "autoApprove": []}, "github.com/modelcontextprotocol/servers/tree/main/src/puppeteer": {"command": "docker", "args": ["run", "-i", "--rm", "--init", "-e", "DOCKER_CONTAINER=true", "mcp/puppeteer"], "disabled": false, "autoApprove": []}}}