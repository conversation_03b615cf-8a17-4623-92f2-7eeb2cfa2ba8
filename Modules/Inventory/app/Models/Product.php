<?php

declare(strict_types=1);

namespace Modules\Inventory\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

final class Product extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'inventory_products';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'sku',
        'description',
        'category_id',
        'supplier_id',
        'price',
        'cost',
        'stock_quantity',
        'min_stock_level',
        'max_stock_level',
        'unit',
        'barcode',
        'track_stock',
        'is_active',
        'images',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'price' => 'decimal:2',
        'cost' => 'decimal:2',
        'track_stock' => 'boolean',
        'is_active' => 'boolean',
        'images' => 'array',
    ];

    /**
     * Get the first image URL.
     */
    public function getFirstImageAttribute(): ?string
    {
        if ($this->images && is_array($this->images) && count($this->images) > 0) {
            return asset('storage/'.$this->images[0]);
        }

        return null;
    }

    /**
     * Get all image URLs.
     */
    public function getImageUrlsAttribute(): array
    {
        if ($this->images && is_array($this->images)) {
            return array_map(function ($image) {
                return asset('storage/'.$image);
            }, $this->images);
        }

        return [];
    }

    /**
     * Get the category that owns the product.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    /**
     * Get the supplier that owns the product.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'supplier_id');
    }

    /**
     * Get the stock movements for the product.
     */
    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class, 'product_id')->orderBy('movement_date', 'desc');
    }

    /**
     * Get the amendments for the product.
     */
    public function amendments(): HasMany
    {
        return $this->hasMany(Amendment::class, 'product_id')->orderBy('amendment_date', 'desc');
    }

    /**
     * Get the borrowings for the product.
     */
    public function borrowings(): HasMany
    {
        return $this->hasMany(Borrowing::class, 'product_id')->orderBy('borrowed_date', 'desc');
    }

    /**
     * Get active borrowings (not returned).
     */
    public function activeBorrowings(): HasMany
    {
        return $this->hasMany(Borrowing::class, 'product_id')->whereIn('status', ['borrowed', 'overdue']);
    }

    /**
     * Scope to get only active products.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get products with low stock.
     */
    public function scopeLowStock($query)
    {
        return $query->whereColumn('stock_quantity', '<=', 'min_stock_level');
    }

    /**
     * Check if the product is low on stock.
     */
    public function isLowStock(): bool
    {
        return $this->track_stock && $this->stock_quantity <= $this->min_stock_level;
    }

    /**
     * Check if the product is out of stock.
     */
    public function isOutOfStock(): bool
    {
        return $this->track_stock && $this->stock_quantity <= 0;
    }

    /**
     * Get the profit margin.
     */
    public function getProfitMarginAttribute(): float
    {
        if ($this->cost > 0) {
            return (($this->price - $this->cost) / $this->cost) * 100;
        }

        return 0;
    }

    /**
     * Get the stock status.
     */
    public function getStockStatusAttribute(): string
    {
        if (! $this->track_stock) {
            return 'Not Tracked';
        }

        if ($this->isOutOfStock()) {
            return 'Out of Stock';
        }

        if ($this->isLowStock()) {
            return 'Low Stock';
        }

        return 'In Stock';
    }

    /**
     * Get the total borrowed quantity.
     */
    public function getTotalBorrowedAttribute(): int
    {
        return $this->activeBorrowings()->sum('quantity_borrowed') -
               $this->activeBorrowings()->sum('quantity_returned');
    }

    /**
     * Get the available quantity (stock - borrowed).
     */
    public function getAvailableQuantityAttribute(): int
    {
        return max(0, $this->stock_quantity - $this->total_borrowed);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($product) {
            if (empty($product->sku)) {
                $product->sku = mb_strtoupper(Str::random(8));
            }
        });
    }
}
