<?php

declare(strict_types=1);

namespace Modules\Inventory\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class Amendment extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'inventory_amendments';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'product_id',
        'recorded_quantity',
        'actual_quantity',
        'variance',
        'status',
        'notes',
        'amended_by',
        'approved_by',
        'amendment_date',
        'approved_date',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'amendment_date' => 'datetime',
        'approved_date' => 'datetime',
    ];

    /**
     * Get the product that owns the amendment.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Get the user who made the amendment.
     */
    public function amendedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'amended_by');
    }

    /**
     * Get the user who approved the amendment.
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope to get pending amendments.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get approved amendments.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Get the variance type (shortage/overage).
     */
    public function getVarianceTypeAttribute(): string
    {
        if ($this->variance > 0) {
            return 'Overage';
        }
        if ($this->variance < 0) {
            return 'Shortage';
        }

        return 'No Variance';
    }

    /**
     * Get the absolute variance.
     */
    public function getAbsoluteVarianceAttribute(): int
    {
        return abs($this->variance);
    }

    /**
     * Check if amendment has variance.
     */
    public function hasVariance(): bool
    {
        return $this->variance !== 0;
    }

    /**
     * Approve the amendment.
     */
    public function approve(): bool
    {
        $this->status = 'approved';
        $this->approved_by = auth()->id();
        $this->approved_date = now();

        $saved = $this->save();

        // Update product stock if approved
        if ($saved && $this->hasVariance()) {
            $this->product->update(['stock_quantity' => $this->actual_quantity]);

            // Create stock movement record
            StockMovement::create([
                'product_id' => $this->product_id,
                'type' => 'adjustment',
                'quantity' => $this->variance,
                'previous_stock' => $this->recorded_quantity,
                'new_stock' => $this->actual_quantity,
                'reference' => 'Amendment #'.$this->id,
                'reason' => 'Stock amendment: '.$this->variance_type,
                'user_id' => auth()->id(),
                'movement_date' => now(),
            ]);
        }

        return $saved;
    }

    /**
     * Reject the amendment.
     */
    public function reject(): bool
    {
        $this->status = 'rejected';
        $this->approved_by = auth()->id();
        $this->approved_date = now();

        return $this->save();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($amendment) {
            $amendment->variance = $amendment->actual_quantity - $amendment->recorded_quantity;
            $amendment->amended_by = auth()->id();
            $amendment->amendment_date = now();
        });
    }
}
