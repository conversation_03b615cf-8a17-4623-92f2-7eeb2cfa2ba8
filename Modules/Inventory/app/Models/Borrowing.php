<?php

declare(strict_types=1);

namespace Modules\Inventory\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class Borrowing extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'inventory_borrowings';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'product_id',
        'quantity_borrowed',
        'borrower_name',
        'borrower_email',
        'borrower_phone',
        'department',
        'purpose',
        'status',
        'borrowed_date',
        'expected_return_date',
        'actual_return_date',
        'quantity_returned',
        'return_notes',
        'issued_by',
        'returned_to',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'borrowed_date' => 'datetime',
        'expected_return_date' => 'datetime',
        'actual_return_date' => 'datetime',
    ];

    /**
     * Get the product that was borrowed.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Get the user who issued the item.
     */
    public function issuedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'issued_by');
    }

    /**
     * Get the user who received the return.
     */
    public function returnedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'returned_to');
    }

    /**
     * Scope to get borrowed items.
     */
    public function scopeBorrowed($query)
    {
        return $query->where('status', 'borrowed');
    }

    /**
     * Scope to get returned items.
     */
    public function scopeReturned($query)
    {
        return $query->where('status', 'returned');
    }

    /**
     * Scope to get overdue items.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue')
            ->orWhere(function ($q) {
                $q->where('status', 'borrowed')
                    ->where('expected_return_date', '<', now());
            });
    }

    /**
     * Check if the borrowing is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->status === 'borrowed' &&
               $this->expected_return_date &&
               now()->isAfter($this->expected_return_date);
    }

    /**
     * Get the days overdue.
     */
    public function getDaysOverdueAttribute(): int
    {
        if (! $this->isOverdue() || ! $this->expected_return_date) {
            return 0;
        }

        return (int) $this->expected_return_date->diffInDays(now());
    }

    /**
     * Get the remaining quantity to be returned.
     */
    public function getRemainingQuantityAttribute(): int
    {
        return $this->quantity_borrowed - $this->quantity_returned;
    }

    /**
     * Return the borrowed item (full or partial).
     */
    public function returnItem(int $quantity, ?string $notes = null): bool
    {
        if ($quantity > $this->remaining_quantity) {
            return false;
        }

        $this->quantity_returned += $quantity;
        $this->return_notes = $notes;
        $this->returned_to = auth()->id();

        // If fully returned, mark as returned
        if ($this->quantity_returned >= $this->quantity_borrowed) {
            $this->status = 'returned';
            $this->actual_return_date = now();
        }

        $saved = $this->save();

        if ($saved) {
            // Update product stock
            $this->product->increment('stock_quantity', $quantity);

            // Create stock movement record
            StockMovement::create([
                'product_id' => $this->product_id,
                'type' => 'in',
                'quantity' => $quantity,
                'previous_stock' => $this->product->stock_quantity - $quantity,
                'new_stock' => $this->product->stock_quantity,
                'reference' => 'Return #'.$this->id,
                'reason' => 'Item returned by '.$this->borrower_name.($notes ? ' - '.$notes : ''),
                'user_id' => auth()->id(),
                'movement_date' => now(),
            ]);
        }

        return $saved;
    }

    /**
     * Mark item as lost.
     */
    public function markAsLost(?string $notes = null): bool
    {
        $this->status = 'lost';
        $this->return_notes = $notes;
        $this->returned_to = auth()->id();
        $this->actual_return_date = now();

        return $this->save();
    }

    /**
     * Get the status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'borrowed' => $this->isOverdue() ? 'danger' : 'warning',
            'returned' => 'success',
            'overdue' => 'danger',
            'lost' => 'danger',
            default => 'gray',
        };
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($borrowing) {
            $borrowing->issued_by = auth()->id();
            $borrowing->borrowed_date = now();

            // Update product stock
            $product = Product::find($borrowing->product_id);
            if ($product) {
                $product->decrement('stock_quantity', $borrowing->quantity_borrowed);

                // Create stock movement record
                StockMovement::create([
                    'product_id' => $borrowing->product_id,
                    'type' => 'out',
                    'quantity' => -$borrowing->quantity_borrowed,
                    'previous_stock' => $product->stock_quantity + $borrowing->quantity_borrowed,
                    'new_stock' => $product->stock_quantity,
                    'reference' => 'Borrowing #'.($borrowing->id ?? 'PENDING'),
                    'reason' => 'Item borrowed by '.$borrowing->borrower_name,
                    'user_id' => auth()->id(),
                    'movement_date' => now(),
                ]);
            }
        });
    }
}
