<?php

declare(strict_types=1);

namespace Modules\Inventory\Filament;

use App\Models\GeneralSetting;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Inventory\Filament\Resources\AmendmentResource;
use Modules\Inventory\Filament\Resources\BorrowingResource;
use Modules\Inventory\Filament\Resources\CategoryResource;
use Modules\Inventory\Filament\Resources\ProductResource;
use Modules\Inventory\Filament\Resources\StockMovementResource;
use Modules\Inventory\Filament\Resources\SupplierResource;

final class InventoryPlugin implements Plugin
{
    public static function make(): static
    {
        return app(self::class);
    }

    public static function get(): static
    {
        /** @var static $plugin */
        $plugin = filament(app(static::class)->getId());

        return $plugin;
    }

    public function getId(): string
    {
        return 'inventory';
    }

    public function register(Panel $panel): void
    {
        // Check if the inventory module is enabled
        $generalSettings = GeneralSetting::first();

        if ($generalSettings && $generalSettings->inventory_module_enabled) {
            $panel
                ->resources([
                    CategoryResource::class,
                    SupplierResource::class,
                    ProductResource::class,
                    StockMovementResource::class,
                    AmendmentResource::class,
                    BorrowingResource::class,
                ]);
        }
    }

    public function boot(Panel $panel): void
    {
        //
    }
}
