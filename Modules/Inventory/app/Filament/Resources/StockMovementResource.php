<?php

declare(strict_types=1);

namespace Modules\Inventory\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Modules\Inventory\Models\Product;
use Modules\Inventory\Models\StockMovement;

final class StockMovementResource extends Resource
{
    protected static ?string $model = StockMovement::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-path';

    protected static ?string $navigationLabel = 'Stock Movements';

    protected static ?string $navigationGroup = 'Inventory';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Movement Information')
                    ->schema([
                        Select::make('product_id')
                            ->label('Product')
                            ->options(Product::query()->active()->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state) {
                                    $product = Product::find($state);
                                    if ($product) {
                                        $set('previous_stock', $product->stock_quantity);
                                    }
                                }
                            }),

                        Select::make('type')
                            ->options([
                                'in' => 'Stock In',
                                'out' => 'Stock Out',
                                'adjustment' => 'Adjustment',
                            ])
                            ->required()
                            ->reactive(),

                        TextInput::make('quantity')
                            ->numeric()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, Forms\Get $get, Forms\Set $set) {
                                $previousStock = $get('previous_stock') ?? 0;
                                $type = $get('type');
                                $quantity = (int) $state;

                                if ($type === 'out') {
                                    $quantity = -abs($quantity);
                                } elseif ($type === 'in') {
                                    $quantity = abs($quantity);
                                }

                                $newStock = $previousStock + $quantity;
                                $set('new_stock', max(0, $newStock));
                            }),

                        TextInput::make('previous_stock')
                            ->label('Previous Stock')
                            ->numeric()
                            ->disabled()
                            ->dehydrated(),

                        TextInput::make('new_stock')
                            ->label('New Stock')
                            ->numeric()
                            ->disabled()
                            ->dehydrated(),

                        DateTimePicker::make('movement_date')
                            ->label('Movement Date')
                            ->default(now())
                            ->required(),
                    ])
                    ->columns(2),

                Section::make('Additional Information')
                    ->schema([
                        TextInput::make('reference')
                            ->label('Reference (Order #, Invoice #, etc.)')
                            ->maxLength(255),

                        Textarea::make('reason')
                            ->label('Reason for Movement')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('product.name')
                    ->label('Product')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('product.sku')
                    ->label('SKU')
                    ->searchable()
                    ->toggleable(),

                BadgeColumn::make('type')
                    ->label('Type')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'in' => 'Stock In',
                        'out' => 'Stock Out',
                        'adjustment' => 'Adjustment',
                        default => ucfirst($state),
                    })
                    ->colors([
                        'success' => 'in',
                        'danger' => 'out',
                        'warning' => 'adjustment',
                    ]),

                TextColumn::make('quantity')
                    ->sortable()
                    ->formatStateUsing(function ($record) {
                        $prefix = $record->quantity >= 0 ? '+' : '';

                        return $prefix.$record->quantity;
                    })
                    ->color(function ($record) {
                        return $record->quantity >= 0 ? 'success' : 'danger';
                    }),

                TextColumn::make('previous_stock')
                    ->label('Previous')
                    ->sortable(),

                TextColumn::make('new_stock')
                    ->label('New Stock')
                    ->sortable(),

                TextColumn::make('reference')
                    ->searchable()
                    ->toggleable(),

                TextColumn::make('user.name')
                    ->label('User')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('movement_date')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->options([
                        'in' => 'Stock In',
                        'out' => 'Stock Out',
                        'adjustment' => 'Adjustment',
                    ]),

                SelectFilter::make('product_id')
                    ->label('Product')
                    ->options(Product::query()->pluck('name', 'id'))
                    ->searchable(),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('movement_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => StockMovementResource\Pages\ListStockMovements::route('/'),
            'create' => StockMovementResource\Pages\CreateStockMovement::route('/create'),
            'view' => StockMovementResource\Pages\ViewStockMovement::route('/{record}'),
            'edit' => StockMovementResource\Pages\EditStockMovement::route('/{record}/edit'),
        ];
    }
}
