<?php

declare(strict_types=1);

namespace Modules\Inventory\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Modules\Inventory\Models\Amendment;
use Modules\Inventory\Models\Product;

final class AmendmentResource extends Resource
{
    protected static ?string $model = Amendment::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';

    protected static ?string $navigationLabel = 'Amendments';

    protected static ?string $navigationGroup = 'Inventory';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Amendment Information')
                    ->schema([
                        Select::make('product_id')
                            ->label('Product')
                            ->options(Product::query()->active()->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state) {
                                    $product = Product::find($state);
                                    if ($product) {
                                        $set('recorded_quantity', $product->stock_quantity);
                                    }
                                }
                            }),

                        TextInput::make('recorded_quantity')
                            ->label('Recorded Quantity')
                            ->numeric()
                            ->required()
                            ->disabled()
                            ->dehydrated(),

                        TextInput::make('actual_quantity')
                            ->label('Actual Counted Quantity')
                            ->numeric()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, Forms\Get $get, Forms\Set $set) {
                                $recorded = $get('recorded_quantity') ?? 0;
                                $actual = (int) $state;
                                $variance = $actual - $recorded;
                                $set('variance', $variance);
                            }),

                        TextInput::make('variance')
                            ->label('Variance')
                            ->numeric()
                            ->disabled()
                            ->dehydrated()
                            ->helperText('Positive = Overage, Negative = Shortage'),

                        Textarea::make('notes')
                            ->label('Amendment Notes')
                            ->rows(3)
                            ->placeholder('Explain the reason for the variance...')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('product.name')
                    ->label('Product')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('product.sku')
                    ->label('SKU')
                    ->searchable()
                    ->toggleable(),

                TextColumn::make('recorded_quantity')
                    ->label('Recorded')
                    ->sortable(),

                TextColumn::make('actual_quantity')
                    ->label('Actual')
                    ->sortable(),

                TextColumn::make('variance')
                    ->label('Variance')
                    ->sortable()
                    ->formatStateUsing(function ($state) {
                        $prefix = $state >= 0 ? '+' : '';

                        return $prefix.$state;
                    })
                    ->color(function ($state) {
                        if ($state > 0) {
                            return 'success';
                        }
                        if ($state < 0) {
                            return 'danger';
                        }

                        return 'gray';
                    }),

                BadgeColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn (string $state): string => ucfirst($state))
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'approved',
                        'danger' => 'rejected',
                    ]),

                TextColumn::make('amendedBy.name')
                    ->label('Amended By')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('amendment_date')
                    ->label('Amendment Date')
                    ->dateTime()
                    ->sortable(),

                TextColumn::make('approved_date')
                    ->label('Approved Date')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                    ]),

                SelectFilter::make('product_id')
                    ->label('Product')
                    ->options(Product::query()->pluck('name', 'id'))
                    ->searchable(),
            ])
            ->actions([
                Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn (Amendment $record): bool => $record->status === 'pending')
                    ->requiresConfirmation()
                    ->action(function (Amendment $record) {
                        $record->approve();
                        Notification::make()
                            ->title('Amendment approved successfully')
                            ->success()
                            ->send();
                    }),

                Action::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn (Amendment $record): bool => $record->status === 'pending')
                    ->requiresConfirmation()
                    ->action(function (Amendment $record) {
                        $record->reject();
                        Notification::make()
                            ->title('Amendment rejected')
                            ->warning()
                            ->send();
                    }),

                ViewAction::make(),
                EditAction::make()
                    ->visible(fn (Amendment $record): bool => $record->status === 'pending'),
                DeleteAction::make()
                    ->visible(fn (Amendment $record): bool => $record->status === 'pending'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('amendment_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => AmendmentResource\Pages\ListAmendments::route('/'),
            'create' => AmendmentResource\Pages\CreateAmendment::route('/create'),
            'view' => AmendmentResource\Pages\ViewAmendment::route('/{record}'),
            'edit' => AmendmentResource\Pages\EditAmendment::route('/{record}/edit'),
        ];
    }
}
