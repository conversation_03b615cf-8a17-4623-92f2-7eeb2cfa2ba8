<?php

declare(strict_types=1);

namespace Modules\Inventory\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Modules\Inventory\Models\Borrowing;
use Modules\Inventory\Models\Product;

final class BorrowingResource extends Resource
{
    protected static ?string $model = Borrowing::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-right-on-rectangle';

    protected static ?string $navigationLabel = 'Borrowings';

    protected static ?string $navigationGroup = 'Inventory';

    protected static ?int $navigationSort = 6;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Product Information')
                    ->schema([
                        Select::make('product_id')
                            ->label('Product')
                            ->options(Product::query()->active()->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state) {
                                    $product = Product::find($state);
                                    if ($product) {
                                        $set('available_quantity', $product->available_quantity);
                                    }
                                }
                            }),

                        TextInput::make('available_quantity')
                            ->label('Available Quantity')
                            ->disabled()
                            ->helperText('Available quantity (excluding borrowed items)'),

                        TextInput::make('quantity_borrowed')
                            ->label('Quantity to Borrow')
                            ->numeric()
                            ->required()
                            ->minValue(1),
                    ])
                    ->columns(3),

                Section::make('Borrower Information')
                    ->schema([
                        TextInput::make('borrower_name')
                            ->label('Borrower Name')
                            ->required()
                            ->maxLength(255),

                        TextInput::make('borrower_email')
                            ->label('Email')
                            ->email()
                            ->maxLength(255),

                        TextInput::make('borrower_phone')
                            ->label('Phone')
                            ->tel()
                            ->maxLength(255),

                        TextInput::make('department')
                            ->label('Department')
                            ->maxLength(255),

                        Textarea::make('purpose')
                            ->label('Purpose of Borrowing')
                            ->rows(2)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Section::make('Return Information')
                    ->schema([
                        DateTimePicker::make('expected_return_date')
                            ->label('Expected Return Date')
                            ->default(now()->addDays(7)),

                        TextInput::make('quantity_returned')
                            ->label('Quantity Returned')
                            ->numeric()
                            ->default(0)
                            ->visible(fn ($context) => $context === 'edit'),

                        Textarea::make('return_notes')
                            ->label('Return Notes')
                            ->rows(2)
                            ->visible(fn ($context) => $context === 'edit')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('product.name')
                    ->label('Product')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('product.sku')
                    ->label('SKU')
                    ->searchable()
                    ->toggleable(),

                TextColumn::make('borrower_name')
                    ->label('Borrower')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('department')
                    ->label('Department')
                    ->searchable()
                    ->toggleable(),

                TextColumn::make('quantity_borrowed')
                    ->label('Borrowed')
                    ->sortable(),

                TextColumn::make('quantity_returned')
                    ->label('Returned')
                    ->sortable(),

                TextColumn::make('remaining_quantity')
                    ->label('Remaining')
                    ->sortable()
                    ->color(function ($state) {
                        return $state > 0 ? 'warning' : 'success';
                    }),

                BadgeColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn (string $state): string => ucfirst($state))
                    ->colors([
                        'warning' => 'borrowed',
                        'success' => 'returned',
                        'danger' => ['overdue', 'lost'],
                    ]),

                TextColumn::make('borrowed_date')
                    ->label('Borrowed Date')
                    ->date()
                    ->sortable(),

                TextColumn::make('expected_return_date')
                    ->label('Expected Return')
                    ->date()
                    ->sortable()
                    ->color(function ($record) {
                        return $record && $record->isOverdue() ? 'danger' : null;
                    }),

                TextColumn::make('days_overdue')
                    ->label('Days Overdue')
                    ->formatStateUsing(function ($record) {
                        if (! $record || ! $record->isOverdue()) {
                            return '-';
                        }

                        return $record->days_overdue.' days';
                    })
                    ->color(function ($record) {
                        return $record && $record->isOverdue() ? 'danger' : null;
                    })
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'borrowed' => 'Borrowed',
                        'returned' => 'Returned',
                        'overdue' => 'Overdue',
                        'lost' => 'Lost',
                    ]),

                SelectFilter::make('product_id')
                    ->label('Product')
                    ->options(Product::query()->pluck('name', 'id'))
                    ->searchable(),
            ])
            ->actions([
                Action::make('return')
                    ->label('Return')
                    ->icon('heroicon-o-arrow-left-on-rectangle')
                    ->color('success')
                    ->visible(fn (Borrowing $record): bool => in_array($record->status, ['borrowed', 'overdue']))
                    ->form([
                        TextInput::make('return_quantity')
                            ->label('Quantity to Return')
                            ->numeric()
                            ->required()
                            ->default(fn (Borrowing $record) => $record->remaining_quantity)
                            ->minValue(1)
                            ->maxValue(fn (Borrowing $record) => $record->remaining_quantity),

                        Textarea::make('return_notes')
                            ->label('Return Notes')
                            ->rows(2),
                    ])
                    ->action(function (Borrowing $record, array $data) {
                        $record->returnItem($data['return_quantity'], $data['return_notes'] ?? null);
                        Notification::make()
                            ->title('Item returned successfully')
                            ->success()
                            ->send();
                    }),

                Action::make('mark_lost')
                    ->label('Mark as Lost')
                    ->icon('heroicon-o-exclamation-triangle')
                    ->color('danger')
                    ->visible(fn (Borrowing $record): bool => in_array($record->status, ['borrowed', 'overdue']))
                    ->requiresConfirmation()
                    ->form([
                        Textarea::make('loss_notes')
                            ->label('Loss Notes')
                            ->required()
                            ->rows(2),
                    ])
                    ->action(function (Borrowing $record, array $data) {
                        $record->markAsLost($data['loss_notes']);
                        Notification::make()
                            ->title('Item marked as lost')
                            ->warning()
                            ->send();
                    }),

                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('borrowed_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => BorrowingResource\Pages\ListBorrowings::route('/'),
            'create' => BorrowingResource\Pages\CreateBorrowing::route('/create'),
            'view' => BorrowingResource\Pages\ViewBorrowing::route('/{record}'),
            'edit' => BorrowingResource\Pages\EditBorrowing::route('/{record}/edit'),
        ];
    }
}
