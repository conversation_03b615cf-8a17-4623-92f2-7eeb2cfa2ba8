<?php

declare(strict_types=1);

namespace Modules\Inventory\Filament\Resources\BorrowingResource\Pages;

use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Modules\Inventory\Filament\Resources\BorrowingResource;
use Modules\Inventory\Models\Product;

final class CreateBorrowing extends CreateRecord
{
    protected static string $resource = BorrowingResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Validate available quantity
        $product = Product::find($data['product_id']);
        if ($product && $data['quantity_borrowed'] > $product->available_quantity) {
            Notification::make()
                ->title('Insufficient quantity available')
                ->body("Only {$product->available_quantity} items are available for borrowing.")
                ->danger()
                ->send();

            $this->halt();
        }

        return $data;
    }
}
