<?php

declare(strict_types=1);

namespace Modules\Inventory\Filament\Resources\StockMovementResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Modules\Inventory\Filament\Resources\StockMovementResource;
use Modules\Inventory\Models\Product;

final class CreateStockMovement extends CreateRecord
{
    protected static string $resource = StockMovementResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set the user who created the movement
        $data['user_id'] = auth()->id();

        // Update the product stock quantity
        if (isset($data['product_id']) && isset($data['new_stock'])) {
            $product = Product::find($data['product_id']);
            if ($product) {
                $product->update(['stock_quantity' => $data['new_stock']]);
            }
        }

        return $data;
    }
}
