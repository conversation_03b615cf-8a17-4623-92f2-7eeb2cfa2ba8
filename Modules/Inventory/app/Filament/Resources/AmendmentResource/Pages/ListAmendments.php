<?php

declare(strict_types=1);

namespace Modules\Inventory\Filament\Resources\AmendmentResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Inventory\Filament\Resources\AmendmentResource;

final class ListAmendments extends ListRecords
{
    protected static string $resource = AmendmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
