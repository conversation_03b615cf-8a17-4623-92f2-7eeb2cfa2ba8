<?php

declare(strict_types=1);

namespace Modules\Inventory\Filament\Resources\AmendmentResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Modules\Inventory\Filament\Resources\AmendmentResource;

final class ViewAmendment extends ViewRecord
{
    protected static string $resource = AmendmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
