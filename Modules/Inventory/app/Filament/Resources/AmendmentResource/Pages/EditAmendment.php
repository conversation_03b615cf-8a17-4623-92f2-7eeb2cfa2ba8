<?php

declare(strict_types=1);

namespace Modules\Inventory\Filament\Resources\AmendmentResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Inventory\Filament\Resources\AmendmentResource;

final class EditAmendment extends EditRecord
{
    protected static string $resource = AmendmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
