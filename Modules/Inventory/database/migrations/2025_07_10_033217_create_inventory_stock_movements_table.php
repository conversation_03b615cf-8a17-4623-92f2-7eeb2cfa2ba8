<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_stock_movements', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id');
            $table->enum('type', ['in', 'out', 'adjustment']); // in = stock increase, out = stock decrease, adjustment = manual correction
            $table->integer('quantity'); // positive for 'in', negative for 'out'
            $table->integer('previous_stock');
            $table->integer('new_stock');
            $table->string('reference')->nullable(); // order number, invoice number, etc.
            $table->text('reason')->nullable(); // reason for the movement
            $table->unsignedBigInteger('user_id')->nullable(); // who made the movement
            $table->timestamp('movement_date');
            $table->timestamps();

            $table->foreign('product_id')->references('id')->on('inventory_products')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->index(['product_id', 'movement_date']);
            $table->index(['type', 'movement_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_stock_movements');
    }
};
