<?php

declare(strict_types=1);

namespace Modules\Inventory\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Inventory\Models\Category;
use Modules\Inventory\Models\Product;
use Modules\Inventory\Models\Supplier;

final class InventorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample categories
        $electronics = Category::create([
            'name' => 'Electronics',
            'description' => 'Electronic devices and components',
            'slug' => 'electronics',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $office = Category::create([
            'name' => 'Office Supplies',
            'description' => 'Office and stationery supplies',
            'slug' => 'office-supplies',
            'is_active' => true,
            'sort_order' => 2,
        ]);

        // Create sample suppliers
        $techSupplier = Supplier::create([
            'name' => 'Tech Solutions Inc.',
            'contact_person' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'address' => '123 Tech Street',
            'city' => 'San Francisco',
            'state' => 'CA',
            'postal_code' => '94105',
            'country' => 'USA',
            'is_active' => true,
        ]);

        $officeSupplier = Supplier::create([
            'name' => 'Office World',
            'contact_person' => 'Jane Doe',
            'email' => '<EMAIL>',
            'phone' => '******-0456',
            'address' => '456 Business Ave',
            'city' => 'New York',
            'state' => 'NY',
            'postal_code' => '10001',
            'country' => 'USA',
            'is_active' => true,
        ]);

        // Create sample products
        Product::create([
            'name' => 'Laptop Computer',
            'sku' => 'LAPTOP001',
            'description' => 'High-performance laptop for business use',
            'category_id' => $electronics->id,
            'supplier_id' => $techSupplier->id,
            'price' => 999.99,
            'cost' => 750.00,
            'stock_quantity' => 25,
            'min_stock_level' => 5,
            'max_stock_level' => 50,
            'unit' => 'pcs',
            'is_active' => true,
        ]);

        Product::create([
            'name' => 'Wireless Mouse',
            'sku' => 'MOUSE001',
            'description' => 'Ergonomic wireless mouse',
            'category_id' => $electronics->id,
            'supplier_id' => $techSupplier->id,
            'price' => 29.99,
            'cost' => 15.00,
            'stock_quantity' => 100,
            'min_stock_level' => 20,
            'max_stock_level' => 200,
            'unit' => 'pcs',
            'is_active' => true,
        ]);

        Product::create([
            'name' => 'Office Chair',
            'sku' => 'CHAIR001',
            'description' => 'Comfortable ergonomic office chair',
            'category_id' => $office->id,
            'supplier_id' => $officeSupplier->id,
            'price' => 199.99,
            'cost' => 120.00,
            'stock_quantity' => 15,
            'min_stock_level' => 3,
            'max_stock_level' => 30,
            'unit' => 'pcs',
            'is_active' => true,
        ]);

        Product::create([
            'name' => 'Printer Paper',
            'sku' => 'PAPER001',
            'description' => 'A4 white printer paper - 500 sheets',
            'category_id' => $office->id,
            'supplier_id' => $officeSupplier->id,
            'price' => 9.99,
            'cost' => 6.00,
            'stock_quantity' => 2, // Low stock for testing
            'min_stock_level' => 10,
            'max_stock_level' => 100,
            'unit' => 'reams',
            'is_active' => true,
        ]);
    }
}
