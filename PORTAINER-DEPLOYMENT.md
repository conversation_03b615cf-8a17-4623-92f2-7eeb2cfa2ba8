# DCCP Admin V2 - Portainer Deployment Guide

## 🐳 Deploying to Portainer

This guide walks you through deploying DCCP Admin V2 to your Portainer instance using Docker stacks.

## Prerequisites

- Portainer CE/EE installed and running
- Docker image built and available
- External PostgreSQL database
- External Redis instance (recommended)
- Network access between Portainer and external services

## 🚀 Quick Deployment Steps

### Step 1: Build and Push Docker Image

First, build your Docker image and make it available to Portainer:

```bash
# Build the production image
docker build --target production -t dccp-admin:latest .

# Option A: Save image and load on Portainer host
docker save dccp-admin:latest | gzip > dccp-admin.tar.gz
# Transfer to Portainer host and load:
# docker load < dccp-admin.tar.gz

# Option B: Push to registry (recommended)
docker tag dccp-admin:latest your-registry.com/dccp-admin:latest
docker push your-registry.com/dccp-admin:latest
```

### Step 2: Access Portainer

1. Open your Portainer web interface
2. Navigate to **Stacks** section
3. Click **Add stack**

### Step 3: Configure Stack

#### Method A: Web Editor
1. Choose **Web editor**
2. Name your stack: `dccp-admin`
3. Copy the contents of `portainer-external-stack.yml` into the editor

#### Method B: Git Repository
1. Choose **Repository** 
2. Repository URL: `https://github.com/your-org/DccpAdminV2`
3. Compose path: `portainer-external-stack.yml`
4. Branch: `main`

### Step 4: Configure Environment Variables

In the **Environment variables** section, add these variables:

| Variable | Value | Description |
|----------|--------|-------------|
| `APP_NAME` | `DCCP Admin V2` | Application name |
| `APP_KEY` | `base64:your_key_here` | Laravel encryption key |
| `APP_URL` | `https://your-domain.com` | Public application URL |
| `APP_PORT` | `8000` | External port mapping |
| `DOMAIN` | `your-domain.com` | Domain for Traefik |
| `DB_HOST` | `your-postgres-host` | PostgreSQL hostname |
| `DB_PORT` | `5432` | PostgreSQL port |
| `DB_DATABASE` | `dccpadminv2` | Database name |
| `DB_USERNAME` | `dccp_user` | Database username |
| `DB_PASSWORD` | `your_password` | Database password |
| `REDIS_HOST` | `your-redis-host` | Redis hostname |
| `REDIS_PORT` | `6379` | Redis port |
| `REDIS_PASSWORD` | `your_redis_password` | Redis password |
| `MAIL_MAILER` | `smtp` | Mail driver |
| `MAIL_HOST` | `smtp.mailgun.org` | SMTP hostname |
| `MAIL_PORT` | `587` | SMTP port |
| `MAIL_USERNAME` | `your_username` | SMTP username |
| `MAIL_PASSWORD` | `your_password` | SMTP password |
| `MAIL_FROM_ADDRESS` | `<EMAIL>` | From email |
| `LOG_LEVEL` | `warning` | Logging level |
| `RUN_MIGRATIONS` | `false` | Auto-run migrations |

### Step 5: Deploy Stack

1. Click **Deploy the stack**
2. Wait for deployment to complete
3. Check **Containers** section for running status

### Step 6: Run Initial Setup

After deployment, execute these commands in the container:

```bash
# Access container shell in Portainer
# Or use Portainer's console feature

# Generate application key (if not set)
php artisan key:generate --force

# Run migrations
php artisan migrate --force

# Optimize application
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# Create storage link
php artisan storage:link
```

## 📋 Deployment Variations

### With Included Services

To deploy with included Redis and PostgreSQL:

1. Use `portainer-stack.yml` instead
2. Add these environment variables:
   - `INCLUDE_REDIS=true`
   - `INCLUDE_POSTGRES=true`
3. Configure internal service credentials

### Multiple Environments

Create separate stacks for different environments:

- `dccp-admin-prod` (production)
- `dccp-admin-staging` (staging)
- `dccp-admin-dev` (development)

Each with different environment variables and domains.

## 🔧 Portainer Configuration

### Networks

The stack creates a custom network `dccp-network`. Ensure it doesn't conflict with existing networks.

### Volumes

Persistent volumes are created for:
- `app_storage` - Application storage
- `app_logs` - Application logs  
- `app_cache` - Framework cache
- `app_sessions` - Session storage
- `app_views` - Compiled views
- `app_public` - Public storage

### Resource Limits

Default resource limits:
- **CPU**: 2.0 cores limit, 0.5 cores reserved
- **Memory**: 1GB limit, 256MB reserved

Adjust via environment variables:
- `APP_CPU_LIMIT`
- `APP_MEMORY_LIMIT`

## 🌐 Reverse Proxy Setup

### Traefik Integration

The stack includes Traefik labels:

```yaml
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.dccp-admin.rule=Host(`your-domain.com`)"
  - "traefik.http.routers.dccp-admin.entrypoints=websecure"
  - "traefik.http.routers.dccp-admin.tls.certresolver=letsencrypt"
```

### Nginx Proxy Manager

For Nginx Proxy Manager:

1. Remove Traefik labels
2. Add custom labels:
   ```yaml
   labels:
     - "npm.enable=true"
     - "npm.host=your-domain.com"
     - "npm.port=8000"
   ```

### Manual Reverse Proxy

Configure your reverse proxy to forward:
- **Host**: `your-domain.com`
- **Backend**: `http://container-ip:8000`
- **Health Check**: `http://container-ip:8000/health`

## 📊 Monitoring in Portainer

### Container Health

Monitor container health in Portainer:

1. Go to **Containers**
2. Click on `dccp-admin-app`
3. Check **Health** status
4. View **Stats** for resource usage

### Application Logs

View logs in Portainer:

1. Select container
2. Click **Logs**
3. Use filters for error levels

### Health Checks

The container includes health checks:
- **Endpoint**: `/health`
- **Interval**: 30 seconds
- **Timeout**: 10 seconds
- **Retries**: 3

## 🔒 Security Considerations

### Secrets Management

Use Portainer's secrets feature:

1. Create secrets for sensitive values:
   - `dccp_app_key`
   - `dccp_db_password`
   - `dccp_redis_password`

2. Update stack to use secrets:
   ```yaml
   environment:
     APP_KEY_FILE: /run/secrets/dccp_app_key
   secrets:
     - dccp_app_key
   ```

### Network Security

- Use custom networks for isolation
- Limit exposed ports
- Enable container firewalls if available

### Access Control

- Restrict Portainer access
- Use strong passwords
- Enable 2FA if available

## 🚨 Troubleshooting

### Common Issues

**Stack deployment fails:**
1. Check environment variables
2. Verify image availability
3. Review Docker logs in Portainer

**Application won't start:**
1. Check container logs
2. Verify external service connectivity
3. Validate environment configuration

**Health checks failing:**
1. Ensure port 8000 is accessible
2. Check application startup time
3. Verify health endpoint response

**Database connection issues:**
1. Test connectivity from container
2. Verify credentials
3. Check firewall rules

### Debugging Commands

Access container console in Portainer:

```bash
# Test database connection
php artisan tinker
>>> DB::connection()->getPdo()

# Test Redis connection
php artisan tinker
>>> Cache::store('redis')->put('test', 'ok')

# Check configuration
php artisan config:show database
php artisan config:show cache

# View application status
php artisan about
```

## 🔄 Updates and Maintenance

### Updating the Application

1. Build new Docker image
2. Update image tag in stack
3. Redeploy stack in Portainer
4. Run migrations if needed

### Backup Procedures

**Application Data:**
1. Use Portainer's volume backup
2. Export volumes to external storage

**Database Backup:**
Configure external PostgreSQL backups independently.

**Configuration Backup:**
1. Export stack configuration
2. Save environment variables
3. Document custom settings

## 📈 Scaling

### Horizontal Scaling

Add replicas in stack configuration:

```yaml
deploy:
  replicas: 3
  update_config:
    parallelism: 1
    delay: 10s
```

### Load Balancing

Configure load balancer for multiple instances:
- Round-robin distribution
- Health check integration
- Session affinity if needed

## 🎯 Production Checklist

- [ ] Docker image built and available
- [ ] External services configured and accessible
- [ ] Environment variables set correctly
- [ ] SSL certificates configured
- [ ] Backup procedures established
- [ ] Monitoring configured
- [ ] Health checks validated
- [ ] Security settings reviewed
- [ ] Access controls implemented
- [ ] Documentation updated

## 📞 Support

For Portainer-specific issues:
- Check Portainer documentation
- Review container logs in Portainer
- Use Portainer community forums

For application issues:
- Check application logs
- Verify configuration
- Test external service connectivity

Your DCCP Admin V2 application is now ready for production deployment in Portainer! 🚀